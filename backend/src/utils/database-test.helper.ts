import { Injectable } from '@nestjs/common';
import { DataSource, createConnection, Connection } from 'typeorm';
import { ConfigService } from '@nestjs/config';

/**
 * Helper class for database operations in tests
 * Follows the project's best practices of using real database connections
 */
@Injectable()
export class DatabaseTestHelper {
  private connection: Connection;
  private configService: ConfigService;

  constructor() {
    this.configService = new ConfigService();
  }

  /**
   * Initialize a test database connection
   */
  async initializeTestDatabase(): Promise<void> {
    try {
      this.connection = await createConnection({
        type: 'postgres',
        host: this.configService.get<string>('DB_HOST', 'localhost'),
        port: this.configService.get<number>('DB_PORT', 5432),
        username: this.configService.get<string>('DB_USERNAME', 'postgres'),
        password: this.configService.get<string>('DB_PASSWORD', 'postgres'),
        database: 'wodleague', // Usar la base de datos real wodleague
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: false, // No sincronizamos en pruebas con DB real
      });
      
      console.log('Test database connection established to wodleague');
    } catch (error) {
      console.error('Error connecting to wodleague database:', error.message);
      throw error;
    }
  }

  /**
   * Close the test database connection
   */
  async closeTestDatabase(): Promise<void> {
    if (this.connection && this.connection.isConnected) {
      await this.connection.close();
      console.log('Test database connection closed');
    }
  }

  /**
   * Clean up test data from a specific table
   */
  async cleanTable(tableName: string): Promise<void> {
    if (this.connection && this.connection.isConnected) {
      await this.connection.query(`DELETE FROM ${tableName}`);
      console.log(`Cleaned up test data from ${tableName}`);
    }
  }

  /**
   * Get the current database connection
   */
  getConnection(): Connection {
    return this.connection;
  }
}
