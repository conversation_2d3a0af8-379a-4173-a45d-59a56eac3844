import { 
  List, 
  Datagrid, 
  TextField, 
  NumberField,
  DateField,
  FunctionField
} from 'react-admin';

// Lista de Resultados (solo lectura para recursos públicos)
export const ResultadoList = () => (
  <List title="Resultados">
    <Datagrid>
      <TextField source="id" label="ID" />
      <TextField source="usuarioId" label="Usuario ID" />
      <TextField source="wodId" label="WOD ID" />
      <NumberField source="puntuacion" label="Puntuación" />
      <FunctionField 
        label="Tiempo" 
        render={(record: any) => {
          if (!record.tiempo) return '-';
          const minutes = Math.floor(record.tiempo / 60);
          const seconds = record.tiempo % 60;
          return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
        }}
      />
      <TextField source="estadoVerificacion" label="Estado" />
      <DateField source="createdAt" label="Fecha" showTime />
    </Datagrid>
  </List>
);
