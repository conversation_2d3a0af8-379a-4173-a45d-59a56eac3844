import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateResultadoDto {
  @IsNotEmpty()
  @IsUUID()
  usuarioId: string;

  @IsNotEmpty()
  @IsUUID()
  wodId: string;

  @IsNotEmpty()
  @IsNumber()
  valor: number;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaEnvio: Date;

  @IsOptional()
  @IsBoolean()
  validado?: boolean;

  @IsOptional()
  @IsUUID()
  validadoPor?: string;

  @IsOptional()
  @IsString()
  comentario?: string;

  @IsOptional()
  @IsBoolean()
  tiempoCap?: boolean;
}
