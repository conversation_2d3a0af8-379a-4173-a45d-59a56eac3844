/**
 * Auth Service
 * Gestiona todas las operaciones relacionadas con autenticación
 * Utiliza el cliente API centralizado y manejo de tokens mejorado
 */
import apiClient from '../api/apiClient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { jwtDecode } from 'jwt-decode';
import cacheManager from '../utils/cacheManager';

const authService = {
  /**
   * Registra un nuevo usuario
   * @param {Object} userData - Datos del usuario incluyendo email, password, etc.
   * @returns {Promise<Object>} - Respuesta de la API con tokens
   */
  async register(userData) {
    try {
      const response = await apiClient.post('/api/auth/register', userData);
      
      // Guardar tokens si existen en la respuesta
      if (response && response.access_token) {
        await AsyncStorage.setItem('auth-token', response.access_token);
        
        if (response.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.refresh_token);
        }
        
        // Guardar email y nombre de usuario para conveniencia
        if (userData.email) {
          await AsyncStorage.setItem('userEmail', userData.email);
        }
        
        if (userData.username || userData.fullName) {
          await AsyncStorage.setItem('userName', userData.username || userData.fullName);
        }
      }
      
      return response;
    } catch (error) {
      console.error('Error en registro:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Inicia sesión de usuario
   * @param {string} email - Email del usuario
   * @param {string} password - Contraseña del usuario
   * @returns {Promise<Object>} - Información de la sesión incluyendo si es primer login
   */
  async login(email, password) {
    try {
      // Aquí hacemos la petición al endpoint directamente sin usar apiClient
      // porque aún no tenemos token para el interceptor
      const response = await apiClient.post('/api/auth/login', { email, password });
      
      // Almacenar tokens
      if (response && response.access_token) {
        await AsyncStorage.setItem('auth-token', response.access_token);
        
        if (response.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.refresh_token);
        }
        
        // Almacenar email y username
        await AsyncStorage.setItem('userEmail', email);
        
        // Decodificar token para obtener información del usuario
        const decoded = jwtDecode(response.access_token);
        
        // Verificar si es primer inicio de sesión (según setupCompleted)
        const isSetupCompleted = decoded.setupCompleted || false;
        
        // Si el token indica que ya se completó el setup, actualizar AsyncStorage
        if (isSetupCompleted) {
          await AsyncStorage.setItem('setupCompleted', 'true');
        } else {
          // Verificar si hay información en AsyncStorage como respaldo
          const storedSetupCompleted = await AsyncStorage.getItem('setupCompleted');
          if (storedSetupCompleted === 'true' && !isSetupCompleted) {
            console.log('📝 Actualizando estado de setupCompleted desde AsyncStorage');
          }
        }
        
        // Limpiar cualquier caché anterior
        await cacheManager.clear();
        
        // El primer login es cuando setupCompleted es false y no hay valor en storage
        const isFirstLogin = !isSetupCompleted && 
                            await AsyncStorage.getItem('setupCompleted') !== 'true';
        
        return {
          ...response,
          isFirstLogin,
          userInfo: {
            id: decoded.sub,
            email: decoded.email,
            emailVerified: decoded.emailVerificado || false
          }
        };
      }
      
      throw new Error('No se recibió token de acceso');
    } catch (error) {
      console.error(`Error en login para usuario ${email}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Refresca el token usando el refresh token
   * @returns {Promise<Object>} - Nuevos tokens
   */
  async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh-token');
      if (!refreshToken) {
        throw new Error('No hay refresh token disponible');
      }
      
      const response = await apiClient.post('/api/auth/refresh', { refreshToken });
      
      if (response && response.access_token) {
        // Guardar nuevo token
        await AsyncStorage.setItem('auth-token', response.access_token);
        
        // Guardar nuevo refresh token si existe
        if (response.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.refresh_token);
        }
        
        return response;
      }
      
      throw new Error('Respuesta de refresh inválida');
    } catch (error) {
      console.error('Error al refrescar token:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Envía email de verificación
   * @param {string} email - Email del usuario
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async sendVerificationEmail(email) {
    try {
      return await apiClient.post('/api/auth/send-verification-email', { email });
    } catch (error) {
      console.error('Error al enviar email de verificación:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Verifica el email del usuario con token
   * @param {string} token - Token de verificación
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async verifyEmail(token) {
    try {
      return await apiClient.get(`/api/auth/verify-email/${token}`);
    } catch (error) {
      console.error('Error al verificar email:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Verifica estado de verificación del email
   * @returns {Promise<Object>} - Estado de verificación
   */
  async checkEmailVerification() {
    try {
      return await apiClient.get('/api/auth/email-verification-status');
    } catch (error) {
      console.error('Error al verificar estado del email:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Solicita restablecimiento de contraseña
   * @param {string} email - Email del usuario
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async requestPasswordReset(email) {
    try {
      return await apiClient.post('/api/auth/forgot-password', { email });
    } catch (error) {
      console.error('Error al solicitar reset de contraseña:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Restablece contraseña con token
   * @param {string} token - Token de reset
   * @param {string} newPassword - Nueva contraseña
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async resetPassword(token, newPassword) {
    try {
      return await apiClient.post('/api/auth/reset-password', { 
        token, 
        password: newPassword 
      });
    } catch (error) {
      console.error('Error al resetear contraseña:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Cambia contraseña (usuario autenticado)
   * @param {string} currentPassword - Contraseña actual
   * @param {string} newPassword - Nueva contraseña
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async changePassword(currentPassword, newPassword) {
    try {
      return await apiClient.post('/api/auth/change-password', {
        currentPassword,
        newPassword
      });
    } catch (error) {
      console.error('Error al cambiar contraseña:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Cierra sesión
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      // Intentar hacer logout en el servidor
      try {
        await apiClient.post('/api/auth/logout');
      } catch (logoutError) {
        // Continuamos aunque falle el logout en el servidor
        console.warn('Error en logout en servidor:', logoutError.message);
      }
      
      // Eliminar tokens
      await AsyncStorage.removeItem('auth-token');
      await AsyncStorage.removeItem('refresh-token');
      
      // Limpiar toda la caché
      await cacheManager.clear();
      
      console.log('🔒 Sesión cerrada correctamente');
    } catch (error) {
      console.error('Error en proceso de logout:', error);
      
      // Intentar limpiar tokens y caché de todas formas
      try {
        await AsyncStorage.removeItem('auth-token');
        await AsyncStorage.removeItem('refresh-token');
        await cacheManager.clear();
      } catch (cleanupError) {
        console.error('Error en limpieza final:', cleanupError);
      }
      
      throw error;
    }
  },
  
  /**
   * Obtiene información del usuario actual desde el token
   * @returns {Promise<Object|null>} - Información del usuario o null
   */
  async getCurrentUser() {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      if (!token) return null;
      
      try {
        const decoded = jwtDecode(token);
        
        // Verificar si el token ha expirado
        if (decoded.exp * 1000 < Date.now()) {
          console.log('Token expirado, intentando renovar...');
          try {
            await this.refreshToken();
            // Obtener token nuevo
            const newToken = await AsyncStorage.getItem('auth-token');
            if (!newToken) return null;
            
            // Decodificar nuevo token
            const newDecoded = jwtDecode(newToken);
            return {
              id: newDecoded.sub,
              email: newDecoded.email,
              emailVerified: newDecoded.emailVerificado || false,
              setupCompleted: newDecoded.setupCompleted || false,
              onBoarding: newDecoded.onBoarding || false,
            };
          } catch (refreshError) {
            console.error('Error renovando token:', refreshError);
            return null;
          }
        }
        
        // Token válido, retornar datos
        return {
          id: decoded.sub,
          email: decoded.email,
          emailVerified: decoded.emailVerificado || false,
          setupCompleted: decoded.setupCompleted || false,
          onBoarding: decoded.onBoarding || false,
        };
      } catch (decodeError) {
        console.error('Error decodificando token:', decodeError);
        return null;
      }
    } catch (error) {
      console.error('Error obteniendo usuario actual:', error);
      return null;
    }
  }
};

export default authService;
