#!/bin/bash

# ======================================================
# 🚀 The WOD League - Backend Startup Script
# ======================================================
# This script helps developers to start the NestJS backend
# environment with proper configurations and checks.
# ======================================================

# Set colors for better visual feedback
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}🏋️ THE WOD LEAGUE - NESTJS BACKEND STARTUP 🏋️${NC}"
echo -e "${BLUE}==================================================${NC}"

# Navigate to backend directory
cd "$(dirname "$0")/backend" || { 
  echo -e "${RED}❌ Error: Could not navigate to backend directory${NC}"; 
  exit 1; 
}

# Check if node modules are installed
if [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}📦 Node modules not found. Installing dependencies...${NC}"
  npm install || { 
    echo -e "${RED}❌ Error installing dependencies${NC}"; 
    exit 1; 
  }
  echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
  echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Check for environment variables
if [ ! -f ".env" ] && [ -f ".env.development" ]; then
  echo -e "${YELLOW}⚙️ .env file not found. Creating from .env.development...${NC}"
  cp .env.development .env
  echo -e "${GREEN}✅ .env file created from example${NC}"
  echo -e "${YELLOW}⚠️ Please update .env with your actual settings${NC}"
elif [ ! -f ".env" ] && [ ! -f ".env.development" ]; then
  echo -e "${RED}❌ Neither .env nor .env.development files found${NC}"
  echo -e "${YELLOW}⚠️ You may need to create an .env file manually${NC}"
else
  echo -e "${GREEN}✅ Environment file found${NC}"
fi

# Determine development mode
MODE="start:dev"
if [ "$1" == "--production" ]; then
  MODE="start:prod"
  echo -e "${YELLOW}🌐 Starting in PRODUCTION mode${NC}"
elif [ "$1" == "--build" ]; then
  echo -e "${YELLOW}🏗️ Building NestJS before starting...${NC}"
  npm run build || {
    echo -e "${RED}❌ Build failed${NC}";
    exit 1;
  }
  echo -e "${GREEN}✅ Build completed successfully${NC}"
  MODE="start:prod"
else
  echo -e "${YELLOW}🛠️ Starting in DEVELOPMENT mode${NC}"
fi

# Check for Docker services
echo -e "${YELLOW}🔍 Checking Docker services...${NC}"
if docker ps | grep -q 'postgres'; then
  echo -e "${GREEN}✅ PostgreSQL service is running in Docker${NC}"
else
  echo -e "${RED}❌ PostgreSQL service is NOT running in Docker${NC}"
  echo -e "${YELLOW}⚠️ You must run the database before starting the server${NC}"
  echo -e "${BLUE}💻 Run this command to start the database:${NC}"
  echo -e "${GREEN}   docker-compose up -d postgres${NC}"
  echo -e "${RED}⛔ Stopping execution. Database is required to run the server.${NC}"
  exit 1
fi

if docker ps | grep -q 'redis'; then
  echo -e "${GREEN}✅ Redis service is running in Docker${NC}"
else
  echo -e "${YELLOW}⚠️ Redis service might not be running in Docker${NC}"
  echo -e "${YELLOW}⚠️ Make sure to run docker-compose up before starting the server${NC}"
fi

# Starting the NestJS server
echo -e "${GREEN}🚀 Starting NestJS server...${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "${YELLOW}👉 Server will be available at:${NC}"
echo -e "${YELLOW}   http://localhost:3000/api${NC}"
echo -e "${BLUE}==================================================${NC}"

# Start NestJS in the appropriate mode in background
echo -e "${YELLOW}🔄 Starting NestJS server in background...${NC}"
npm run $MODE &
BACKEND_PID=$!

# Wait a moment for the backend to start
echo -e "${YELLOW}⏳ Waiting for backend to initialize...${NC}"
sleep 5

# Check if backend is running
BACKEND_URL="http://localhost:3000/api"
echo -e "${YELLOW}🔍 Checking if backend is available...${NC}"

# Wait up to 30 seconds for backend to be ready
for i in {1..6}; do
  if curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL" | grep -q "\(200\|404\|401\)"; then
    echo -e "${GREEN}✅ Backend is ready at $BACKEND_URL${NC}"
    break
  else
    echo -e "${YELLOW}⏳ Backend not ready yet, waiting... (attempt $i/6)${NC}"
    sleep 5
  fi

  if [ $i -eq 6 ]; then
    echo -e "${RED}❌ Backend failed to start properly${NC}"
    echo -e "${YELLOW}⚠️ Killing backend process and exiting...${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
  fi
done

# Navigate back to root and start React Admin
echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}🖥️  STARTING REACT ADMIN FRONTEND 🖥️${NC}"
echo -e "${BLUE}==================================================${NC}"

cd ..

# Check if react-admin directory exists
if [ ! -d "react-admin" ]; then
  echo -e "${RED}❌ React Admin directory not found${NC}"
  echo -e "${YELLOW}⚠️ Killing backend process and exiting...${NC}"
  kill $BACKEND_PID 2>/dev/null
  exit 1
fi

# Navigate to react-admin and check dependencies
cd react-admin

if [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}📦 React Admin node modules not found. Installing dependencies...${NC}"
  npm install || {
    echo -e "${RED}❌ Error installing React Admin dependencies${NC}";
    echo -e "${YELLOW}⚠️ Killing backend process and exiting...${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1;
  }
  echo -e "${GREEN}✅ React Admin dependencies installed successfully${NC}"
else
  echo -e "${GREEN}✅ React Admin dependencies already installed${NC}"
fi

# Start React Admin
echo -e "${GREEN}🚀 Starting React Admin...${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "${YELLOW}👉 Backend available at: http://localhost:3000/api${NC}"
echo -e "${YELLOW}👉 React Admin will be available at: http://localhost:3001${NC}"
echo -e "${BLUE}==================================================${NC}"

# Function to handle cleanup on script exit
cleanup() {
  echo -e "\n${YELLOW}🛑 Shutting down services...${NC}"
  echo -e "${YELLOW}⚠️ Killing backend process (PID: $BACKEND_PID)...${NC}"
  kill $BACKEND_PID 2>/dev/null
  echo -e "${GREEN}✅ Services stopped${NC}"
  exit 0
}

# Set trap to handle Ctrl+C
trap cleanup SIGINT SIGTERM

# Start React Admin (this will run in foreground)
npm start

# If npm start exits, cleanup backend
cleanup
