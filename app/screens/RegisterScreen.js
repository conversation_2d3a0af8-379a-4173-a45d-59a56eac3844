import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Keyboard,
  TouchableWithoutFeedback,
  SafeAreaView,
  ActivityIndicator
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Colors from '../constants/colors';
import authApiNest from '../api/authApiNest';

const RegisterScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: true, // Siempre aceptado por defecto
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
  };

  const toggleTerms = () => {
    setFormData({ ...formData, acceptTerms: !formData.acceptTerms });
  };

  //===========================================================
  // Validar Email
  //===========================================================
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  //===========================================================
  // Dificultad de contraseña
  //===========================================================
  const checkPasswordLength = (password) => password.length >= 8;
  const checkPasswordAlphanumeric = (password) => /^(?=.*[A-Za-z])(?=.*\d).+$/.test(password);
  const checkPasswordSpecialChars = (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, level: 'Vacío' };

    let strength = 0;

    // Longitud mínima (8 caracteres)
    if (checkPasswordLength(password)) {
      strength += 1;
    }

    // Combinación de letras y números
    if (checkPasswordAlphanumeric(password)) {
      strength += 1;
    }

    // Caracteres especiales
    if (checkPasswordSpecialChars(password)) {
      strength += 1;
    }

    // Determinar nivel
    let level = 'Débil';
    let color = '#FF3B30'; // Rojo para débil

    if (strength === 2) {
      level = 'Medio';
      color = '#FFCC00'; // Amarillo para medio
    } else if (strength === 3) {
      level = 'Fuerte';
      color = Colors.PRIMARY; // Verde para fuerte
    }

    return {
      strength,
      level,
      color,
      percentage: (strength / 3) * 100
    };
  };

  const [isRegistering, setIsRegistering] = useState(false);

  const handleSubmit = async () => {
    // Validaciones
    if (!formData.nombre.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'El nombre es obligatorio',
      });
      return;
    }

    if (!validateEmail(formData.email)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Ingresa un email válido',
      });
      return;
    }

    if (!checkPasswordLength(formData.password)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'La contraseña debe tener al menos 8 caracteres',
      });
      return;
    }

    if (!checkPasswordAlphanumeric(formData.password)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'La contraseña debe contener letras y números',
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Las contraseñas no coinciden',
      });
      return;
    }

    if (!formData.acceptTerms) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Debes aceptar los términos y condiciones',
      });
      return;
    }

    setIsRegistering(true);

    try {
      // Preparar datos de usuario para NestJS (adaptado al formato que espera el backend)
      const userData = {
        // Campos obligatorios según el backend
        email: formData.email,
        password: formData.password,
        nombre: formData.nombre,
        alias: formData.nombre, // Por defecto usamos el nombre como alias
        nivel: 'Intermedio', // Valores permitidos: RX, Intermedio, Scaled
        genero: 'Masculino' // Valores permitidos: Masculino, Femenino
        // Ya no enviamos: username, fullName, setupCompleted, stats
      };

      console.log('Registrando usuario en NestJS:', formData.email);
      
      // Registrar usuario directamente en NestJS
      const registerResponse = await authApiNest.register(userData);
      
      console.log('Registro exitoso:', registerResponse);

      // Guardar datos necesarios en AsyncStorage (NO la contraseña)
      await AsyncStorage.multiSet([
        ['userEmail', formData.email],
        ['userName', formData.nombre],
        ['setupCompleted', 'false'] // Marcar explícitamente que el setup NO está completado
      ]);

      Toast.show({
        type: 'success',
        text1: '¡Registro exitoso!',
        text2: 'Revisa tu email para verificar tu cuenta',
      });

      // Navegar a la pantalla de login con mensaje sobre verificación
      navigation.navigate('Login', { showVerificationMessage: true });
    } catch (error) {
      console.error('Error al registrar usuario:', error);
      
      let errorMessage = 'Error al crear la cuenta.';
      
      // Analizar tipo de error según la respuesta del servidor
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        switch (status) {
          case 400:
            // Error de validación
            errorMessage = errorData?.message || 'Datos de registro inválidos';
            break;
          case 409:
            // Conflicto - email ya existe
            errorMessage = 'Este email ya está en uso.';
            break;
          case 422:
            // Error de procesamiento - contraseña débil
            errorMessage = 'La contraseña no cumple con los requisitos de seguridad.';
            break;
          default:
            errorMessage = errorData?.message || 'Error al registrar en la plataforma.';
        }
      } else if (error.request) {
        // Sin respuesta del servidor
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      } else {
        // Error en la solicitud
        errorMessage = error.message || 'Error al procesar la solicitud';
      }

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: errorMessage,
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        bounces={false}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Crear Cuenta</Text>
          <View style={styles.headerRightSpace} />
        </View>

        <View style={styles.welcomeContainer}>
          <Text style={styles.welcomeTitle}>¡Empecemos!</Text>
        </View>

        <View style={styles.formBackground}>
          <View style={styles.formContainer}>
            <Text style={styles.inputLabel}>Nombre</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="nombre"
                placeholderTextColor="#6c6c6c"
                value={formData.nombre}
                onChangeText={(text) => handleChange('nombre', text)}
              />
            </View>

            <Text style={styles.inputLabel}>Email</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                placeholderTextColor="#6c6c6c"
                keyboardType="email-address"
                autoCapitalize="none"
                value={formData.email}
                onChangeText={(text) => handleChange('email', text)}
              />
            </View>

            <Text style={styles.inputLabel}>Contraseña</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor="#6c6c6c"
                secureTextEntry={!showPassword}
                value={formData.password}
                onChangeText={(text) => handleChange('password', text)}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color="#6c6c6c"
                />
              </TouchableOpacity>
            </View>

            <Text style={styles.inputLabel}>Confirmar contraseña</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                placeholder="••••••••••"
                placeholderTextColor="#6c6c6c"
                secureTextEntry={!showConfirmPassword}
                value={formData.confirmPassword}
                onChangeText={(text) => handleChange('confirmPassword', text)}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color="#6c6c6c"
                />
              </TouchableOpacity>
            </View>

            {formData.password && (
              <View style={{
                width: '100%',
                marginTop: 10,
                marginBottom: 15
              }}>
                <View style={{
                  width: '100%',
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: '#2e2e2e',
                  overflow: 'hidden',
                  marginBottom: 8
                }}>
                  <View
                    style={{
                      height: '100%',
                      width: `${getPasswordStrength(formData.password).percentage}%`,
                      backgroundColor: getPasswordStrength(formData.password).color,
                      shadowColor: getPasswordStrength(formData.password).color,
                      shadowOffset: { width: 0, height: 0 },
                      shadowOpacity: 0.8,
                      shadowRadius: 4,
                    }}
                  />
                </View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Text style={{
                    color: '#ffffff',
                    fontSize: 12,
                    fontWeight: '500'
                  }}>
                    Seguridad de la contraseña
                  </Text>
                  <Text style={{
                    color: getPasswordStrength(formData.password).color,
                    fontSize: 14,
                    fontWeight: 'bold'
                  }}>
                    {getPasswordStrength(formData.password).level}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            Al registrarte, aceptas los{' '}
            <Text style={styles.termsLink}>Términos de uso</Text> y la{' '}
            <Text style={styles.termsLink}>Política de privacidad</Text>
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.registerButton, isRegistering && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={isRegistering}
        >
          {isRegistering ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.registerButtonText}>Crear Cuenta</Text>
          )}
        </TouchableOpacity>

        <View style={[styles.emptyComponent, { height: 20 }]} />

        <Text style={styles.orText}>o regístrate con</Text>

        <View style={styles.socialButtonsContainer}>
          <TouchableOpacity style={styles.socialButton}>
            <Ionicons name="logo-google" size={22} color={Colors.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.socialButton}>
            <Ionicons name="logo-instagram" size={22} color={Colors.WHITE} />
          </TouchableOpacity>
        </View>

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>
            ¿Ya tienes cuenta? Inicia sesión en <Text style={styles.loginLink} onPress={() => navigation.navigate('Login')}>Log in</Text>
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 25,
    paddingTop: 20,
    marginBottom: 40,
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightSpace: {
    width: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    textAlign: 'center',
    flex: 1,
  },
  welcomeContainer: {
    paddingHorizontal: 25,
    marginBottom: 30,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.WHITE,
    textAlign: 'center',
  },
  emptyComponent: {
    width: '100%',
  },
  formBackground: {
    backgroundColor: '#2e2e2e',
    borderRadius: 0,
    padding: 15,
    paddingBottom: 0,
    marginHorizontal: 0,
    marginBottom: 20,
  },
  formContainer: {
    paddingHorizontal: 15,
  },
  inputLabel: {
    fontSize: 14,
    color: Colors.PRIMARY,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 20,
    height: 50,
    borderWidth: 1,
    borderColor: '#2e2e2e',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#232323',
    height: '100%',
  },
  eyeIcon: {
    padding: 5,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 0,
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  termsText: {
    color: '#cacaca',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  termsLink: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  registerButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 15,
    height: 55,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  disabledButton: {
    opacity: 0.7,
  },
  registerButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.BLACK,
  },
  orText: {
    fontSize: 14,
    color: '#CACACA',
    textAlign: 'center',
    marginBottom: 15,
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  socialButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#303030',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  loginContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  loginText: {
    fontSize: 14,
    color: '#CACACA',
  },
  loginLink: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
});

export default RegisterScreen;