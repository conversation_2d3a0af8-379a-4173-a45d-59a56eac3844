import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUsuarioDto } from './dto/create-usuario.dto';
import { UpdateUsuarioDto } from './dto/update-usuario.dto';
import { Usuario, RolUsuario } from './entities/usuario.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsuariosService {
  constructor(
    @InjectRepository(Usuario)
    private usuariosRepository: Repository<Usuario>,
  ) {}

  async create(createUsuarioDto: CreateUsuarioDto): Promise<Usuario> {
    // Verificar si el email ya existe
    const existingUser = await this.usuariosRepository.findOne({
      where: { email: createUsuarioDto.email },
    });

    if (existingUser) {
      throw new ConflictException('El email ya está registrado');
    }

    // Hashear la contraseña
    const hashedPassword = await bcrypt.hash(createUsuarioDto.password, 10);

    // Crear el nuevo usuario
    const newUser = this.usuariosRepository.create({
      ...createUsuarioDto,
      password: hashedPassword,
    });

    return this.usuariosRepository.save(newUser);
  }

  async findAll(): Promise<Usuario[]> {
    return this.usuariosRepository.find({
      relations: ['box'],
    });
  }

  async findOne(id: string): Promise<Usuario> {
    const usuario = await this.usuariosRepository.findOne({
      where: { id },
      relations: ['box'],
    });

    if (!usuario) {
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }

    return usuario;
  }

  /**
   * Find a user by email
   * @param email - User email
   * @returns The user, if found
   */
  async findByEmail(email: string): Promise<Usuario | null> {
    const usuario = await this.usuariosRepository.findOne({
      where: { email },
    });

    return usuario;
  }

  /**
   * Find a user by verification token
   * @param token - Verification token
   * @returns The user, if found
   */
  async findByVerificationToken(token: string): Promise<Usuario | null> {
    const usuario = await this.usuariosRepository.findOne({
      where: { tokenVerificacion: token },
    });

    return usuario;
  }

  async update(id: string, updateUsuarioDto: UpdateUsuarioDto): Promise<Usuario> {
    const usuario = await this.findOne(id);

    // Si se está actualizando la contraseña, hashearla
    if ('password' in updateUsuarioDto && updateUsuarioDto.password) {
      const hashedPassword = await bcrypt.hash(updateUsuarioDto.password, 10);
      updateUsuarioDto = { ...updateUsuarioDto, password: hashedPassword };
    }

    // Actualizar el usuario
    const updatedUser = this.usuariosRepository.merge(usuario, updateUsuarioDto);
    return this.usuariosRepository.save(updatedUser);
  }

  async remove(id: string): Promise<void> {
    const result = await this.usuariosRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }
  }

  async updateLastLogin(id: string): Promise<void> {
    await this.usuariosRepository.update(id, {
      ultimaConexion: new Date(),
    });
  }
  
  /**
   * Actualiza únicamente el rol de un usuario
   * @param id - ID del usuario
   * @param rol - Nuevo rol (Usuario, BoxOwner, Admin)
   * @returns El usuario actualizado
   */
  async updateRol(id: string, rol: RolUsuario): Promise<Usuario> {
    const usuario = await this.findOne(id);
    
    // Validación específica para BoxOwner: debe tener un box asignado
    if (rol === RolUsuario.BOX_OWNER && !usuario.box_id) {
      throw new BadRequestException('Un BoxOwner debe tener un box asignado');
    }
    
    // Si cambia de BoxOwner a otro rol, verificar si es propietario de algún box
    if (usuario.rol === RolUsuario.BOX_OWNER && rol !== RolUsuario.BOX_OWNER) {
      // Aquí podrías agregar una verificación adicional si es necesario
      // Por ejemplo, verificar si es el único owner de algún box
    }
    
    usuario.rol = rol;
    return this.usuariosRepository.save(usuario);
  }

  /**
   * Updates a user's profile photo
   * @param id - User ID
   * @param photoData - Object containing photo information
   * @returns Updated user with photo information
   */
  async updateProfilePhoto(id: string, photoData: {
    filename: string;
    path: string;
    mimetype: string;
    size: number;
  }): Promise<Usuario> {
    // Find the user first
    const usuario = await this.findOne(id);

    // Generate the photo URL (this could be a relative path or full URL depending on your setup)
    const photoUrl = `/uploads/profiles/${photoData.filename}`;

    console.log(`Actualizando foto de perfil para usuario ${id}: ${photoUrl}`);

    // Update the user's photo field
    usuario.foto = photoUrl;

    // Store additional metadata if needed
    // This could be saved in a separate photos table if you want to track history
    // or have multiple photos per user

    // Save the updated user
    const updatedUser = await this.usuariosRepository.save(usuario);

    console.log('Usuario actualizado con nueva foto:', updatedUser.foto);

    return updatedUser;
  }

  /**
   * Find a user by password reset token
   * @param token - The password reset token
   * @returns The user with the specified token if found and not expired
   * @throws NotFoundException if user is not found or token is expired
   */
  async findByPasswordResetToken(token: string): Promise<Usuario> {
    const usuario = await this.usuariosRepository.findOne({
      where: { tokenResetPassword: token }
    });

    if (!usuario) {
      throw new NotFoundException('Token de recuperación no válido');
    }

    // Check if token has expired
    if (usuario.tokenResetPasswordExpira && new Date() > usuario.tokenResetPasswordExpira) {
      throw new NotFoundException('El token de recuperación ha expirado');
    }

    return usuario;
  }
}
