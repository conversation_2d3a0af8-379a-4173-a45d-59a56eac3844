import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { InscripcionesService } from './inscripciones.service';
import { CreateInscripcionDto } from './dto/create-inscripcion.dto';
import { UpdateInscripcionDto } from './dto/update-inscripcion.dto';
import { ConfirmarPagoDto } from './dto/confirmar-pago.dto';
import { Inscripcion, EstadoInscripcion } from './entities/inscripcion.entity';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RolUsuario } from '../usuarios/entities/usuario.entity';

@Controller('inscripciones')
export class InscripcionesController {
  constructor(private readonly inscripcionesService: InscripcionesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createInscripcionDto: CreateInscripcionDto): Promise<Inscripcion> {
    return this.inscripcionesService.create(createInscripcionDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  findAll(): Promise<Inscripcion[]> {
    return this.inscripcionesService.findAll();
  }

  @Get('estado/:estado')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  findByEstado(@Param('estado') estado: EstadoInscripcion): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByEstado(estado);
  }

  @Get('pendientes')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  findPendientesPago(): Promise<Inscripcion[]> {
    return this.inscripcionesService.findPendientesPago();
  }

  @Get('recientes')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  findRecientes(@Query('dias') dias?: number): Promise<Inscripcion[]> {
    return this.inscripcionesService.findRecientes(dias);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: string): Promise<Inscripcion> {
    return this.inscripcionesService.findOne(id);
  }

  @Get('usuario/:usuarioId')
  @UseGuards(JwtAuthGuard)
  findByUsuario(@Param('usuarioId') usuarioId: string): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByUsuario(usuarioId);
  }

  @Get('liga/:ligaId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  findByLiga(@Param('ligaId') ligaId: string): Promise<Inscripcion[]> {
    return this.inscripcionesService.findByLiga(ligaId);
  }

  @Get('usuario/:usuarioId/liga/:ligaId')
  @UseGuards(JwtAuthGuard)
  findByUsuarioAndLiga(
    @Param('usuarioId') usuarioId: string,
    @Param('ligaId') ligaId: string,
  ): Promise<Inscripcion> {
    return this.inscripcionesService.findByUsuarioAndLiga(usuarioId, ligaId);
  }

  @Get('liga/:ligaId/estadisticas')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  contarInscripcionesPorLiga(@Param('ligaId') ligaId: string): Promise<{ total: number, pagadas: number, pendientes: number, canceladas: number, rechazadas: number }> {
    return this.inscripcionesService.contarInscripcionesPorLiga(ligaId);
  }

  @Get('liga/:ligaId/categorias')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  contarInscripcionesPorCategoria(@Param('ligaId') ligaId: string): Promise<{ categoria: string, total: number, pagadas: number }[]> {
    return this.inscripcionesService.contarInscripcionesPorCategoria(ligaId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  update(@Param('id') id: string, @Body() updateInscripcionDto: UpdateInscripcionDto): Promise<Inscripcion> {
    return this.inscripcionesService.update(id, updateInscripcionDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  remove(@Param('id') id: string): Promise<void> {
    return this.inscripcionesService.remove(id);
  }

  @Post(':id/confirmar-pago')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  confirmarPago(@Param('id') id: string, @Body() confirmarPagoDto: ConfirmarPagoDto): Promise<Inscripcion> {
    return this.inscripcionesService.confirmarPago(id, confirmarPagoDto);
  }

  @Post(':id/cancelar')
  @UseGuards(JwtAuthGuard)
  cancelarInscripcion(@Param('id') id: string, @Body('comentarios') comentarios?: string): Promise<Inscripcion> {
    return this.inscripcionesService.cancelarInscripcion(id, comentarios);
  }

  @Post(':id/rechazar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  rechazarInscripcion(@Param('id') id: string, @Body('comentarios') comentarios: string): Promise<Inscripcion> {
    return this.inscripcionesService.rechazarInscripcion(id, comentarios);
  }
}
