import { Test, TestingModule } from '@nestjs/testing';
import { Logger, NotFoundException } from '@nestjs/common';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import { UpdateUsuarioDto } from '../../modules/usuarios/dto/update-usuario.dto';
import * as bcrypt from 'bcrypt';
import { DatabaseTestHelper, UsuariosTestHelper, usuariosFixtures } from '../helper';

/**
 * Test de actualización de perfil de usuario
 * 
 * Este test utiliza una base de datos real (configurada para pruebas)
 * para verificar las operaciones de actualización de perfil.
 */
describe('Actualizar Perfil Usuario Tests', () => {
  let usuariosService: UsuariosService;
  let testUserId: string;
  const logger = new Logger('ActualizarPerfilUsuarioTest');

  beforeAll(async () => {
    // Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection('.env.development'))) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Configuración del módulo de testing usando el helper especializado para usuarios
    const module: TestingModule = await UsuariosTestHelper.createUsuariosTestingModule();
    
    usuariosService = module.get<UsuariosService>(UsuariosService);

    // Crear un usuario de prueba
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = {
      nombre: 'Usuario Actualización',
      alias: 'usuario_actualizacion',
      email: '<EMAIL>',
      password: hashedPassword,
      nivel: NivelUsuario.INTERMEDIO,
      genero: GeneroUsuario.MASCULINO,
    };

    // Limpiar datos anteriores y crear usuario fresco
    try {
      // Intentamos encontrar y eliminar el usuario si ya existe
      const existingUser = await usuariosService['usuariosRepository'].findOne({
        where: { email: testUser.email },
      });
      
      if (existingUser) {
        await usuariosService['usuariosRepository'].delete(existingUser.id);
      }
    } catch (error) {
      // Ignoramos errores, el usuario posiblemente no existe
    }

    // Crear usuario nuevo para las pruebas
    const createdUser = await usuariosService['usuariosRepository'].save(
      usuariosService['usuariosRepository'].create(testUser)
    );
    
    testUserId = createdUser.id;
  });

  afterAll(async () => {
    // Limpiar: eliminar el usuario de prueba usando el helper
    try {
      await DatabaseTestHelper.cleanTestData(usuariosService['usuariosRepository'], { id: testUserId });
      logger.log('Datos de prueba limpiados correctamente');
    } catch (error) {
      logger.error(`Error al limpiar datos de prueba: ${error.message}`);
    }
  });

  // Test para actualizar perfil con datos válidos
  it('debe actualizar el perfil con datos válidos', async () => {
    // Datos para actualizar
    const updateData: UpdateUsuarioDto = {
      nombre: 'Nombre Actualizado',
      alias: 'alias_actualizado',
      foto: 'nueva_foto.jpg',
      nivel: NivelUsuario.RX
    };

    // Actualizar perfil
    const updatedUser = await usuariosService.update(testUserId, updateData);

    // Verificaciones
    expect(updatedUser).toBeDefined();
    expect(updatedUser.id).toBe(testUserId);
    expect(updatedUser.nombre).toBe(updateData.nombre);
    expect(updatedUser.alias).toBe(updateData.alias);
    expect(updatedUser.foto).toBe(updateData.foto);
    expect(updatedUser.nivel).toBe(updateData.nivel);
    
    // La siguiente línea asegura que no se modificaron otros campos
    expect(updatedUser.email).toBe('<EMAIL>');
    expect(updatedUser.genero).toBe(GeneroUsuario.MASCULINO);
  });

  // Test para actualizar contraseña
  it('debe actualizar la contraseña correctamente', async () => {
    // Datos para actualizar
    const updateData: UpdateUsuarioDto = {
      password: 'nuevaPassword456'
    };

    // Actualizar perfil
    const updatedUser = await usuariosService.update(testUserId, updateData);

    // Verificar que se actualizó correctamente
    expect(updatedUser).toBeDefined();
    
    // La contraseña debe estar hasheada
    expect(updatedUser.password).not.toBe(updateData.password);
    
    // Verificar si la contraseña es válida usando bcrypt
    const isPasswordValid = await bcrypt.compare(
      updateData.password as string,
      updatedUser.password
    );
    expect(isPasswordValid).toBe(true);
  });

  // Test para intentar actualizar un usuario inexistente
  it('debe arrojar NotFoundException al actualizar un usuario inexistente', async () => {
    const updateData: UpdateUsuarioDto = {
      nombre: 'Esto No Funcionará'
    };

    // ID inexistente
    const nonExistentId = '00000000-0000-0000-0000-000000000000';

    // Debe arrojar excepción
    await expect(usuariosService.update(nonExistentId, updateData))
      .rejects
      .toThrow(NotFoundException);
  });
});
