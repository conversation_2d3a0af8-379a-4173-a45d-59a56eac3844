import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('movimientos')
export class Movimiento {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  nombre: string;

  // Variantes RX
  @Column({ length: 100, nullable: true })
  rxMasc: string;

  @Column({ length: 100, nullable: true })
  rxFem: string;

  // Variantes INT (Intermedio)
  @Column({ length: 100, nullable: true })
  intMasc: string;

  @Column({ length: 100, nullable: true })
  intFem: string;

  // Variantes SC (Scaled)
  @Column({ length: 100, nullable: true })
  scMasc: string;

  @Column({ length: 100, nullable: true })
  scFem: string;
  
  @Column({ nullable: true, length: 255 })
  descripcion: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
