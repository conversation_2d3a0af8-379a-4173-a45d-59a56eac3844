import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateMovimientoDto {
  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsOptional()
  @IsString()
  descripcion?: string;

  // RX Masculino
  @IsOptional()
  @IsString()
  rxMasc?: string;

  // RX Femenino
  @IsOptional()
  @IsString()
  rxFem?: string;

  // INT Masculino
  @IsOptional()
  @IsString()
  intMasc?: string;

  // INT Femenino
  @IsOptional()
  @IsString()
  intFem?: string;

  // SC Masculino
  @IsOptional()
  @IsString()
  scMasc?: string;

  // SC Femenino
  @IsOptional()
  @IsString()
  scFem?: string;
}
