import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ClasificacionesService } from '../modules/clasificaciones/clasificaciones.service';
import { ClasificacionesGateway } from '../websockets/clasificaciones.gateway';
import { LigasService } from '../modules/ligas/ligas.service';

@Injectable()
export class ClasificacionJob {
  private readonly logger = new Logger(ClasificacionJob.name);

  constructor(
    private clasificacionesService: ClasificacionesService,
    private clasificacionesGateway: ClasificacionesGateway,
    private ligasService: LigasService,
  ) {}

  @Cron(CronExpression.EVERY_10_MINUTES)
  async actualizarClasificaciones() {
    this.logger.log('Ejecutando tarea programada: Actualización de clasificaciones');
    
    try {
      // Obtener todas las ligas activas
      const ligas = await this.ligasService.findActivas();
      
      for (const liga of ligas) {
        // Actualizar clasificaciones para cada categoría
        if (liga.categoriaRx && liga.generoMasculino) {
          await this.actualizarYEmitir(liga.id, 'RX-Masculino');
        }
        if (liga.categoriaRx && liga.generoFemenino) {
          await this.actualizarYEmitir(liga.id, 'RX-Femenino');
        }
        if (liga.categoriaIntermedio && liga.generoMasculino) {
          await this.actualizarYEmitir(liga.id, 'Intermedio-Masculino');
        }
        if (liga.categoriaIntermedio && liga.generoFemenino) {
          await this.actualizarYEmitir(liga.id, 'Intermedio-Femenino');
        }
        if (liga.categoriaScaled && liga.generoMasculino) {
          await this.actualizarYEmitir(liga.id, 'Scaled-Masculino');
        }
        if (liga.categoriaScaled && liga.generoFemenino) {
          await this.actualizarYEmitir(liga.id, 'Scaled-Femenino');
        }
      }
      
      this.logger.log('Clasificaciones actualizadas correctamente');
    } catch (error) {
      this.logger.error(`Error al actualizar clasificaciones: ${error.message}`);
    }
  }

  private async actualizarYEmitir(ligaId: string, categoria: string): Promise<void> {
    try {
      await this.clasificacionesService.actualizarClasificacion(ligaId, categoria);
      this.clasificacionesGateway.emitirActualizacionClasificacion(ligaId, categoria);
      this.logger.log(`Clasificación actualizada y emitida: ${ligaId} - ${categoria}`);
    } catch (error) {
      this.logger.error(`Error al actualizar clasificación ${ligaId} - ${categoria}: ${error.message}`);
    }
  }
}
