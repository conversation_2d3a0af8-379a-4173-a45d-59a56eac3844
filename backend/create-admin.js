const { Client } = require('pg');
const bcrypt = require('bcrypt');

async function createAdminUser() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'thewodleague',
    user: 'postgres',
    password: 'postgres',
  });

  try {
    await client.connect();
    console.log('Conectado a la base de datos');

    // Verificar si ya existe un usuario admin
    const existingAdmin = await client.query(
      "SELECT * FROM usuarios WHERE email = '<EMAIL>'"
    );

    if (existingAdmin.rows.length > 0) {
      console.log('El usuario admin ya existe');
      return;
    }

    // Crear hash de la contraseña
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Insertar usuario admin
    const result = await client.query(`
      INSERT INTO usuarios (
        id, nombre, email, password, alias, edad, nivel, genero, 
        rol, estado, setup_completed, on_boarding, email_verificado,
        created_at, updated_at
      ) VALUES (
        gen_random_uuid(),
        'Administrador',
        '<EMAIL>',
        $1,
        'admin',
        30,
        'RX',
        'Masculino',
        'Admin',
        'Activo',
        true,
        true,
        true,
        NOW(),
        NOW()
      ) RETURNING *
    `, [hashedPassword]);

    console.log('Usuario admin creado exitosamente:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('ID:', result.rows[0].id);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

createAdminUser();
