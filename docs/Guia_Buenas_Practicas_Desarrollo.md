# Guía de Buenas Prácticas de Desarrollo - The WOD League

Este documento establece las buenas prácticas, estándares de código y reglas a seguir durante el desarrollo del proyecto The WOD League. Seguir estas directrices asegurará un código consistente, mantenible y de alta calidad.

## Tabla de Contenidos

1. [Estándares de Código](#estándares-de-código)
2. [Arquitectura y Estructura](#arquitectura-y-estructura)
3. [Patrones de Diseño](#patrones-de-diseño)
4. [Gestión de Errores](#gestión-de-errores)
5. [Testing](#testing)
6. [Seguridad](#seguridad)
7. [Rendimiento](#rendimiento)
8. [Control de Versiones](#control-de-versiones)
9. [Documentación](#documentación)
10. [Referencias y Recursos](#referencias-y-recursos)

## Estándares de Código

### Estilo de Código

- Utilizamos [ESLint](https://eslint.org/) y [Prettier](https://prettier.io/) para mantener un estilo de código consistente.
- La configuración está definida en los archivos `.eslintrc.js` y `.prettierrc`.
- Ejecuta `npm run lint` antes de cada commit para asegurar que el código cumple con los estándares.

### Convenciones de Nomenclatura

- **Archivos**: Utiliza kebab-case para nombres de archivos (ej. `user-service.ts`).
- **Clases**: Utiliza PascalCase para nombres de clases (ej. `UserService`).
- **Interfaces**: Utiliza PascalCase con prefijo I (ej. `IUserService`).
- **Variables y Funciones**: Utiliza camelCase (ej. `getUserById`).
- **Constantes**: Utiliza UPPER_SNAKE_CASE para constantes globales (ej. `MAX_USERS`).
- **Enums**: Utiliza PascalCase para el nombre del enum y UPPER_SNAKE_CASE para sus valores (ej. `enum RolUsuario { ADMIN = 'Admin', USUARIO = 'Usuario' }`).

### TypeScript

- Utiliza tipos explícitos siempre que sea posible.
- Evita el uso de `any`. Utiliza `unknown` si es necesario un tipo genérico.
- Utiliza interfaces para definir la forma de los objetos.
- Utiliza tipos de unión e intersección cuando sea apropiado.
- Aprovecha las características avanzadas de TypeScript como genéricos, tipos condicionales, etc.

```typescript
// Mal
function getUser(id: any): any {
  // ...
}

// Bien
interface User {
  id: string;
  name: string;
  email: string;
}

function getUser(id: string): Promise<User | null> {
  // ...
}
```

## Arquitectura y Estructura

### Estructura de Módulos

Seguimos la arquitectura modular de NestJS:

```
src/
├── modules/               # Módulos de la aplicación
│   ├── usuarios/          # Módulo de usuarios
│   │   ├── dto/           # Data Transfer Objects
│   │   ├── entities/      # Entidades de base de datos
│   │   ├── interfaces/    # Interfaces
│   │   ├── usuarios.controller.ts
│   │   ├── usuarios.service.ts
│   │   ├── usuarios.module.ts
│   │   └── usuarios.repository.ts
│   ├── auth/              # Módulo de autenticación
│   ├── ligas/             # Módulo de ligas
│   └── ...
├── common/                # Código compartido
│   ├── decorators/        # Decoradores personalizados
│   ├── filters/           # Filtros de excepción
│   ├── guards/            # Guards de autenticación
│   ├── interceptors/      # Interceptores
│   ├── pipes/             # Pipes de validación
│   └── utils/             # Utilidades
├── config/                # Configuración
├── app.module.ts          # Módulo principal
└── main.ts                # Punto de entrada
```

### Principios SOLID

- **S**: Principio de Responsabilidad Única - Cada clase debe tener una única responsabilidad.
- **O**: Principio de Abierto/Cerrado - Las entidades deben estar abiertas para extensión pero cerradas para modificación.
- **L**: Principio de Sustitución de Liskov - Los objetos de una superclase deben poder ser reemplazados por objetos de una subclase sin afectar la funcionalidad.
- **I**: Principio de Segregación de Interfaces - Muchas interfaces específicas son mejores que una interfaz general.
- **D**: Principio de Inversión de Dependencias - Depende de abstracciones, no de implementaciones concretas.

## Patrones de Diseño

### Inyección de Dependencias

Utiliza la inyección de dependencias proporcionada por NestJS:

```typescript
@Injectable()
export class UsuariosService {
  constructor(
    @InjectRepository(Usuario)
    private usuariosRepository: Repository<Usuario>,
    private configService: ConfigService,
  ) {}
}
```

### Patrón Repositorio

Utiliza el patrón repositorio para abstraer la capa de acceso a datos:

```typescript
@Injectable()
export class UsuariosRepository {
  constructor(
    @InjectRepository(Usuario)
    private repository: Repository<Usuario>,
  ) {}

  async findById(id: string): Promise<Usuario | null> {
    return this.repository.findOne({ where: { id } });
  }
}
```

### Patrón DTO (Data Transfer Object)

Utiliza DTOs para validar y transferir datos entre capas:

```typescript
export class CreateUsuarioDto {
  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @MinLength(8)
  password: string;
}
```

## Gestión de Errores

### Excepciones HTTP

Utiliza las excepciones proporcionadas por NestJS:

```typescript
if (!usuario) {
  throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
}

if (emailExistente) {
  throw new ConflictException('El email ya está registrado');
}
```

### Filtros de Excepciones

Implementa filtros de excepciones personalizados para manejar errores específicos:

```typescript
@Catch(TypeORMError)
export class DatabaseExceptionFilter implements ExceptionFilter {
  catch(exception: TypeORMError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    
    response.status(500).json({
      statusCode: 500,
      message: 'Error de base de datos',
      error: exception.message,
    });
  }
}
```

### Logging

Utiliza el sistema de logging de NestJS para registrar errores y eventos importantes:

```typescript
private readonly logger = new Logger(UsuariosService.name);

async findOne(id: string): Promise<Usuario> {
  try {
    const usuario = await this.usuariosRepository.findOne({ where: { id } });
    if (!usuario) {
      this.logger.warn(`Usuario con ID ${id} no encontrado`);
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }
    return usuario;
  } catch (error) {
    this.logger.error(`Error al buscar usuario: ${error.message}`, error.stack);
    throw error;
  }
}
```

## Testing

### Tests Unitarios

- Escribe tests unitarios para cada servicio y controlador.
- Utiliza mocks para aislar la unidad que estás probando.
- Sigue la convención de nombrado `*.spec.ts`.

```typescript
describe('UsuariosService', () => {
  let service: UsuariosService;
  let repository: MockType<Repository<Usuario>>;

  beforeEach(async () => {
    // Configuración del módulo de test
  });

  it('debería encontrar un usuario por ID', async () => {
    // Arrange
    const mockUsuario = { id: '1', nombre: 'Test' };
    repository.findOne.mockReturnValue(mockUsuario);
    
    // Act
    const result = await service.findOne('1');
    
    // Assert
    expect(result).toEqual(mockUsuario);
    expect(repository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
  });
});
```

### Tests de Integración

- Escribe tests de integración para verificar la interacción entre módulos.
- Utiliza la base de datos de test (SQLite en memoria o una instancia de PostgreSQL para tests).
- Sigue la convención de nombrado `*.e2e-spec.ts`.

### Cobertura de Tests

- Mantén una cobertura de tests de al menos 80%.
- Ejecuta `npm run test:cov` para verificar la cobertura.

## Seguridad

### Autenticación y Autorización

- Utiliza JWT para la autenticación.
- Implementa guards para proteger rutas.
- Utiliza roles para la autorización.

```typescript
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(RolUsuario.ADMIN)
@Get()
findAll(): Promise<Usuario[]> {
  return this.usuariosService.findAll();
}
```

### Validación de Datos

- Utiliza class-validator para validar DTOs.
- Implementa ValidationPipe a nivel global.

```typescript
app.useGlobalPipes(
  new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }),
);
```

### Protección contra Ataques Comunes

- Implementa rate limiting para prevenir ataques de fuerza bruta.
- Utiliza helmet para proteger contra vulnerabilidades web comunes.
- Implementa CORS correctamente.

```typescript
app.use(helmet());
app.enableCors({
  origin: configService.get('CORS_ORIGIN'),
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  credentials: true,
});
```

## Rendimiento

### Optimización de Consultas

- Utiliza índices en la base de datos para campos frecuentemente consultados.
- Utiliza relaciones eager/lazy apropiadamente.
- Implementa paginación para grandes conjuntos de datos.

```typescript
async findAll(page = 1, limit = 10): Promise<Pagination<Usuario>> {
  const [items, total] = await this.usuariosRepository.findAndCount({
    skip: (page - 1) * limit,
    take: limit,
  });
  
  return {
    items,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}
```

### Caché

- Utiliza Redis para cachear datos frecuentemente accedidos.
- Implementa estrategias de invalidación de caché.

```typescript
@Injectable()
export class ClasificacionesService {
  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async obtenerClasificacion(ligaId: string, categoria: string): Promise<Clasificacion[]> {
    const cacheKey = `clasificacion:${ligaId}:${categoria}`;
    const cachedData = await this.cacheManager.get<string>(cacheKey);
    
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    
    // Obtener datos y guardar en caché
  }
}
```

## Control de Versiones

### Convenciones de Commits

Seguimos el estándar de [Conventional Commits](https://www.conventionalcommits.org/):

```
<tipo>[ámbito opcional]: <descripción>

[cuerpo opcional]

[pie opcional]
```

Tipos comunes:
- **feat**: Nueva característica
- **fix**: Corrección de errores
- **docs**: Cambios en la documentación
- **style**: Cambios que no afectan al significado del código (espacios en blanco, formato, etc.)
- **refactor**: Cambio de código que no corrige un error ni añade una característica
- **test**: Añadir o corregir tests
- **chore**: Cambios en el proceso de build o herramientas auxiliares

### Flujo de Trabajo Git

Seguimos el flujo de trabajo [GitFlow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow):

- **main**: Rama de producción
- **develop**: Rama de desarrollo
- **feature/**: Ramas para nuevas características
- **bugfix/**: Ramas para correcciones de errores
- **release/**: Ramas para preparar releases
- **hotfix/**: Ramas para correcciones urgentes en producción

### Pull Requests

- Crea un PR para cada característica o corrección.
- Incluye una descripción clara de los cambios.
- Asegúrate de que todos los tests pasan.
- Solicita revisión de al menos un compañero.

## Documentación

### Documentación de Código

- Utiliza JSDoc para documentar clases, métodos y funciones.
- Incluye descripciones, parámetros, tipos de retorno y ejemplos cuando sea apropiado.

```typescript
/**
 * Busca un usuario por su ID.
 * 
 * @param id - El ID del usuario a buscar
 * @returns El usuario encontrado o null si no existe
 * @throws NotFoundException si el usuario no existe
 * 
 * @example
 * ```typescript
 * const usuario = await usuariosService.findOne('123');
 * ```
 */
async findOne(id: string): Promise<Usuario> {
  // ...
}
```

### Documentación de API

- Utiliza Swagger para documentar la API.
- Incluye descripciones, parámetros, respuestas y ejemplos.

```typescript
@ApiTags('usuarios')
@ApiOperation({ summary: 'Obtener un usuario por ID' })
@ApiParam({ name: 'id', description: 'ID del usuario' })
@ApiResponse({ status: 200, description: 'Usuario encontrado', type: Usuario })
@ApiResponse({ status: 404, description: 'Usuario no encontrado' })
@Get(':id')
findOne(@Param('id') id: string): Promise<Usuario> {
  return this.usuariosService.findOne(id);
}
```

### Documentación del Proyecto

- Mantén actualizado el README con instrucciones de instalación, configuración y uso.
- Documenta la arquitectura, decisiones de diseño y patrones utilizados.
- Incluye diagramas cuando sea necesario para explicar conceptos complejos.

## Referencias y Recursos

### Documentación Oficial

- [NestJS](https://docs.nestjs.com/)
- [TypeScript](https://www.typescriptlang.org/docs/)
- [TypeORM](https://typeorm.io/)
- [PostgreSQL](https://www.postgresql.org/docs/)
- [Redis](https://redis.io/documentation)
- [Jest](https://jestjs.io/docs/getting-started)

### Artículos y Tutoriales Recomendados

- [NestJS - Técnicas Avanzadas](https://trilon.io/blog/nestjs-advanced-techniques)
- [Mejores Prácticas de TypeScript](https://www.typescriptlang.org/docs/handbook/declaration-files/do-s-and-don-ts.html)
- [Patrones de Diseño en TypeScript](https://refactoring.guru/design-patterns/typescript)
- [Optimización de Rendimiento en NestJS](https://blog.logrocket.com/optimize-nestjs-application-performance/)

### Herramientas Recomendadas

- [Visual Studio Code](https://code.visualstudio.com/) con extensiones:
  - ESLint
  - Prettier
  - TypeScript Hero
  - NestJS Snippets
- [Postman](https://www.postman.com/) para probar la API
- [DBeaver](https://dbeaver.io/) para gestionar la base de datos
- [Redis Desktop Manager](https://rdm.dev/) para gestionar Redis

## Conclusión

Seguir estas buenas prácticas y estándares nos ayudará a mantener un código limpio, mantenible y de alta calidad. Recuerda que estas directrices están diseñadas para ayudar, no para limitar la creatividad. Si encuentras una mejor manera de hacer algo, compártela con el equipo para que podamos mejorar continuamente nuestras prácticas.

---

*Última actualización: Mayo 2025*
