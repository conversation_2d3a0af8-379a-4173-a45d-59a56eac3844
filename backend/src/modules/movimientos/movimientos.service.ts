import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Movimiento } from './entities/movimiento.entity';
import { CreateMovimientoDto } from './dto/create-movimiento.dto';
import { UpdateMovimientoDto } from './dto/update-movimiento.dto';

@Injectable()
export class MovimientosService {
  constructor(
    @InjectRepository(Movimiento)
    private movimientosRepository: Repository<Movimiento>,
  ) {}

  async create(createMovimientoDto: CreateMovimientoDto): Promise<Movimiento> {
    const movimiento = this.movimientosRepository.create(createMovimientoDto);
    return this.movimientosRepository.save(movimiento);
  }

  async findAll(): Promise<Movimiento[]> {
    return this.movimientosRepository.find();
  }

  async findOne(id: string): Promise<Movimiento> {
    const movimiento = await this.movimientosRepository.findOne({ where: { id } });
    if (!movimiento) {
      throw new NotFoundException(`Movimiento con ID ${id} no encontrado`);
    }
    return movimiento;
  }

  async update(id: string, updateMovimientoDto: UpdateMovimientoDto): Promise<Movimiento> {
    const movimiento = await this.findOne(id);
    Object.assign(movimiento, updateMovimientoDto);
    return this.movimientosRepository.save(movimiento);
  }

  async remove(id: string): Promise<void> {
    const result = await this.movimientosRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Movimiento con ID ${id} no encontrado`);
    }
  }
}
