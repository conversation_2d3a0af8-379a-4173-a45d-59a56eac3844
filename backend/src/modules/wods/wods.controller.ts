import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { WodsService } from './wods.service';
import { CreateWodDto } from './dto/create-wod.dto';
import { UpdateWodDto } from './dto/update-wod.dto';
import { WOD } from './entities/wod.entity';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RolUsuario } from '../usuarios/entities/usuario.entity';

@Controller('wods')
export class WodsController {
  constructor(private readonly wodsService: WodsService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  create(@Body() createWodDto: CreateWodDto): Promise<WOD> {
    return this.wodsService.create(createWodDto);
  }

  @Get()
  findAll(): Promise<WOD[]> {
    return this.wodsService.findAll();
  }

  @Get('activos')
  findActivos(): Promise<WOD[]> {
    return this.wodsService.findActivos();
  }

  @Get('liga/:ligaId')
  findByLiga(@Param('ligaId') ligaId: string): Promise<WOD[]> {
    return this.wodsService.findByLiga(ligaId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<WOD> {
    return this.wodsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  update(@Param('id') id: string, @Body() updateWodDto: UpdateWodDto): Promise<WOD> {
    return this.wodsService.update(id, updateWodDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  remove(@Param('id') id: string): Promise<void> {
    return this.wodsService.remove(id);
  }

  @Post(':id/publicar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  publicarWod(@Param('id') id: string): Promise<WOD> {
    return this.wodsService.publicarWod(id);
  }

  @Post(':id/cerrar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  cerrarWod(@Param('id') id: string): Promise<WOD> {
    return this.wodsService.cerrarWod(id);
  }
}
