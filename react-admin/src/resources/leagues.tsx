import { 
  List, 
  Datagrid, 
  TextField, 
  Edit, 
  SimpleForm, 
  TextInput,
  Create,
  DateInput,
  SelectInput,
  ImageInput,
  ImageField,
  ReferenceArrayField,
  SingleFieldList,
  ChipField,
  required,
  NumberInput,
  BooleanInput,
  DateField
} from 'react-admin';

// Import custom component for date adjustment
import { SixWeekDateInput } from '../components/SixWeekDateInput';

// Lista de Ligas
export const LeagueList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="nombre" />
      <DateField source="fechaInicio" />
      <DateField source="fechaFin" />
      <TextField source="estado" />
      <TextField source="precioInscripcion" />
      <TextField source="duracionSemanas" label="Duración (semanas)" />
    </Datagrid>
  </List>
);

// Componente de edición de ligas
export const LeagueEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput disabled source="id" />
      <TextInput source="nombre" validate={[required()]} fullWidth />
      <TextInput source="descripcion" multiline rows={5} fullWidth validate={[required()]} />
      <DateInput source="fechaInicio" validate={[required()]} />
      <SixWeekDateInput source="fechaFin" validate={[required()]} helperText="Fecha de finalización de la liga" />
      <NumberInput source="duracionSemanas" validate={[required()]} min={1} />
      <SelectInput source="estado" choices={[
        { id: 'Preparacion', name: 'Preparación' },
        { id: 'Activa', name: 'Activa' },
        { id: 'Finalizada', name: 'Finalizada' },
      ]} />
      <NumberInput source="precioInscripcion" validate={[required()]} min={0} />
      <BooleanInput source="tieneDescuentoEarly" />
      <NumberInput source="precioEarly" min={0} />
      
      {/* Categorías */}
      <BooleanInput source="categoriaRx" defaultValue={true} />
      <BooleanInput source="categoriaIntermedio" defaultValue={true} />
      <BooleanInput source="categoriaScaled" defaultValue={true} />
      
      {/* Género */}
      <BooleanInput source="generoMasculino" defaultValue={true} />
      <BooleanInput source="generoFemenino" defaultValue={true} />
    </SimpleForm>
  </Edit>
);

// Componente de creación de ligas
export const LeagueCreate = () => (
  <Create>
    <SimpleForm>
      <TextInput source="nombre" validate={[required()]} fullWidth />
      <TextInput source="descripcion" multiline rows={5} fullWidth validate={[required()]} />
      <DateInput source="fechaInicio" validate={[required()]} />
      <SixWeekDateInput source="fechaFin" validate={[required()]} helperText="Fecha de finalización de la liga" />
      <NumberInput source="duracionSemanas" validate={[required()]} min={1} defaultValue={8} />
      <SelectInput source="estado" choices={[
        { id: 'Preparacion', name: 'Preparación' },
        { id: 'Activa', name: 'Activa' },
        { id: 'Finalizada', name: 'Finalizada' },
      ]} defaultValue="Preparacion" />
      <NumberInput source="precioInscripcion" validate={[required()]} min={0} defaultValue={50} />
      <BooleanInput source="tieneDescuentoEarly" defaultValue={false} />
      <NumberInput source="precioEarly" min={0} defaultValue={40} />
      
      {/* Categorías */}
      <BooleanInput source="categoriaRx" defaultValue={true} />
      <BooleanInput source="categoriaIntermedio" defaultValue={true} />
      <BooleanInput source="categoriaScaled" defaultValue={true} />
      
      {/* Género */}
      <BooleanInput source="generoMasculino" defaultValue={true} />
      <BooleanInput source="generoFemenino" defaultValue={true} />
    </SimpleForm>
  </Create>
);
