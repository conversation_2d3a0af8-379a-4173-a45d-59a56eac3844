import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * Decorator to set required roles for a route
 */
export const ROLES_KEY = 'roles';
export const Roles = (...roles: string[]) => {
  return (target: any, key?: string, descriptor?: any) => {
    Reflect.defineMetadata(ROLES_KEY, roles, descriptor.value);
    return descriptor;
  };
};

/**
 * Guard to check if a user has the required roles to access a resource
 * This ensures role-based access control across the application
 */
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  /**
   * Determine if the current user has the required roles
   * @param context Execution context containing the request
   * @returns Boolean indicating if the user is authorized
   */
  canActivate(context: ExecutionContext): boolean {
    // Get required roles from metadata
    const requiredRoles = this.reflector.get<string[]>(
      ROLES_KEY,
      context.getHandler(),
    );

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    // Get user from request
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // If no user or no role, deny access
    if (!user || !user.rol) {
      throw new ForbiddenException('You are not authorized to access this resource');
    }

    // Check if user has any of the required roles
    const hasRequiredRole = requiredRoles.includes(user.rol);
    
    if (!hasRequiredRole) {
      throw new ForbiddenException(`Required role: ${requiredRoles.join(' or ')}. Your role: ${user.rol}`);
    }
    
    return true;
  }
}
