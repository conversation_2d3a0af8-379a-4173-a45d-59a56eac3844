import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Box } from './entities/box.entity';
import { CreateBoxDto } from './dto/create-box.dto';
import { UpdateBoxDto } from './dto/update-box.dto';

@Injectable()
export class BoxesService {
  constructor(
    @InjectRepository(Box)
    private boxesRepository: Repository<Box>,
  ) {}

  async create(createBoxDto: CreateBoxDto): Promise<Box> {
    // Verificar si el nombre ya existe
    const existingBox = await this.boxesRepository.findOne({
      where: { nombre: createBoxDto.nombre },
    });

    if (existingBox) {
      throw new ConflictException('El nombre del box ya está registrado');
    }

    // Crear el nuevo box
    const newBox = this.boxesRepository.create(createBoxDto);
    return this.boxesRepository.save(newBox);
  }

  async findAll(): Promise<Box[]> {
    return this.boxesRepository.find({
      relations: ['owner', 'miembros'],
    });
  }

  async findOne(id: string): Promise<Box> {
    const box = await this.boxesRepository.findOne({
      where: { id },
      relations: ['owner', 'miembros'],
    });

    if (!box) {
      throw new NotFoundException(`Box con ID ${id} no encontrado`);
    }

    return box;
  }

  async update(id: string, updateBoxDto: UpdateBoxDto): Promise<Box> {
    const box = await this.findOne(id);

    // Actualizar el box
    const updatedBox = this.boxesRepository.merge(box, updateBoxDto);
    return this.boxesRepository.save(updatedBox);
  }

  async remove(id: string): Promise<void> {
    const result = await this.boxesRepository.delete(id);
    
    if (result.affected === 0) {
      throw new NotFoundException(`Box con ID ${id} no encontrado`);
    }
  }

  async findByOwner(ownerId: string): Promise<Box[]> {
    return this.boxesRepository.find({
      where: { ownerId },
      relations: ['owner', 'miembros'],
    });
  }

  async findMiembros(id: string): Promise<Box> {
    const box = await this.boxesRepository.findOne({
      where: { id },
      relations: ['miembros'],
    });

    if (!box) {
      throw new NotFoundException(`Box con ID ${id} no encontrado`);
    }

    return box;
  }
}
