import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from '../../../modules/auth/auth.service';
import { UsuariosService } from '../../../modules/usuarios/usuarios.service';
import { EmailService } from '../../../modules/email/email.service';

/**
 * Helper especializado para pruebas de autenticación
 * 
 * Proporciona métodos para configurar módulos de testing
 * con los servicios necesarios para pruebas de autenticación
 */
export class AuthTestHelper {
  private static readonly logger = new Logger(AuthTestHelper.name);
  
  /**
   * Crea un módulo de testing configurado para pruebas de autenticación
   * 
   * @param usuariosService Instancia del servicio de usuarios ya configurado
   * @param jwtSecret Clave secreta para JWT (opcional)
   * @param configPath Ruta al archivo de configuración (opcional)
   * @returns Módulo de testing con AuthService configurado
   */
  static async createAuthTestingModule(
    usuariosService: UsuariosService,
    jwtSecret: string = 'test-jwt-secret',
    configPath: string = '.env.development'
  ): Promise<TestingModule> {
    try {
      const module = await Test.createTestingModule({
        imports: [
          // Configurar ConfigModule para proveer ConfigService
          ConfigModule.forRoot({
            isGlobal: true,
            envFilePath: configPath,
          }),
          // Configurar JwtModule con opciones de prueba
          JwtModule.register({
            secret: jwtSecret,
            signOptions: { expiresIn: '1h' }
          })
        ],
        providers: [
          // Usar la instancia existente de UsuariosService
          { 
            provide: UsuariosService, 
            useValue: usuariosService 
          },
          // Mock para EmailService
          {
            provide: EmailService,
            useValue: {
              sendVerificationEmail: jest.fn().mockResolvedValue(true),
              sendPasswordResetEmail: jest.fn().mockResolvedValue(true)
            }
          },
          // Proporcionar AuthService directamente
          AuthService
        ],
      }).compile();
      
      this.logger.log('Módulo de autenticación para testing creado correctamente');
      return module;
    } catch (error) {
      this.logger.error(`Error al crear módulo de testing para autenticación: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Obtiene un token JWT válido para testing
   * 
   * @param jwtService Servicio JWT configurado
   * @param payload Datos a incluir en el token
   * @returns Token JWT
   */
  static generateTestToken(
    jwtService: JwtService, 
    payload: { sub: string; email: string; rol: string }
  ): string {
    return jwtService.sign(payload);
  }
  
  /**
   * Decodifica un token JWT para testing
   * 
   * @param jwtService Servicio JWT configurado
   * @param token Token JWT a decodificar
   * @returns Payload decodificado del token
   */
  static decodeToken(jwtService: JwtService, token: string): any {
    return jwtService.decode(token);
  }
}
