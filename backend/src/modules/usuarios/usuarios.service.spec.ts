import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsuariosService } from './usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario, RolUsuario, EstadoUsuario } from './entities/usuario.entity';
import { ConflictException, NotFoundException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

// Mock de bcrypt
jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashedPassword'),
  compare: jest.fn().mockResolvedValue(true),
}));

describe('UsuariosService', () => {
  let service: UsuariosService;
  let repository: Repository<Usuario>;

  const mockUsuario = {
    id: '1',
    nombre: 'Test User',
    alias: 'testuser',
    email: '<EMAIL>',
    password: 'hashedPassword',
    nivel: NivelUsuario.RX,
    genero: GeneroUsuario.MASCULINO,
    fechaRegistro: new Date(),
    puntosPvp: 20,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.ACTIVO,
  };

  const mockUsuarioRepository = {
    create: jest.fn().mockReturnValue(mockUsuario),
    save: jest.fn().mockResolvedValue(mockUsuario),
    findOne: jest.fn().mockResolvedValue(mockUsuario),
    find: jest.fn().mockResolvedValue([mockUsuario]),
    update: jest.fn().mockResolvedValue({ affected: 1 }),
    delete: jest.fn().mockResolvedValue({ affected: 1 }),
    merge: jest.fn().mockReturnValue(mockUsuario),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsuariosService,
        {
          provide: getRepositoryToken(Usuario),
          useValue: mockUsuarioRepository,
        },
      ],
    }).compile();

    service = module.get<UsuariosService>(UsuariosService);
    repository = module.get<Repository<Usuario>>(getRepositoryToken(Usuario));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUsuarioDto = {
        nombre: 'Test User',
        alias: 'testuser',
        email: '<EMAIL>',
        password: 'password',
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
      };
      
      mockUsuarioRepository.findOne.mockResolvedValueOnce(null);
      
      // Act
      const result = await service.create(createUsuarioDto);
      
      // Assert
      expect(result).toEqual(mockUsuario);
      expect(bcrypt.hash).toHaveBeenCalledWith('password', 10);
      expect(mockUsuarioRepository.create).toHaveBeenCalledWith({
        ...createUsuarioDto,
        password: 'hashedPassword',
      });
      expect(mockUsuarioRepository.save).toHaveBeenCalledWith(mockUsuario);
    });

    it('should throw ConflictException if email already exists', async () => {
      // Arrange
      const createUsuarioDto = {
        nombre: 'Test User',
        alias: 'testuser',
        email: '<EMAIL>',
        password: 'password',
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
      };
      
      mockUsuarioRepository.findOne.mockResolvedValueOnce(mockUsuario);
      
      // Act & Assert
      await expect(service.create(createUsuarioDto)).rejects.toThrow(ConflictException);
    });
  });

  describe('findAll', () => {
    it('should return an array of users', async () => {
      // Act
      const result = await service.findAll();
      
      // Assert
      expect(result).toEqual([mockUsuario]);
      expect(mockUsuarioRepository.find).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      // Act
      const result = await service.findOne('1');
      
      // Assert
      expect(result).toEqual(mockUsuario);
      expect(mockUsuarioRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['box'],
      });
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      mockUsuarioRepository.findOne.mockResolvedValueOnce(null);
      
      // Act & Assert
      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByEmail', () => {
    it('should return a user by email', async () => {
      // Act
      const result = await service.findByEmail('<EMAIL>');
      
      // Assert
      expect(result).toEqual(mockUsuario);
      expect(mockUsuarioRepository.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
    });

    it('should return null if user not found', async () => {
      // Arrange
      mockUsuarioRepository.findOne.mockResolvedValueOnce(null);
      
      // Act
      const result = await service.findByEmail('<EMAIL>');
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a user', async () => {
      // Arrange
      const updateUsuarioDto = {
        nombre: 'Updated User',
      };
      
      // Act
      const result = await service.update('1', updateUsuarioDto);
      
      // Assert
      expect(result).toEqual(mockUsuario);
      expect(mockUsuarioRepository.merge).toHaveBeenCalledWith(mockUsuario, updateUsuarioDto);
      expect(mockUsuarioRepository.save).toHaveBeenCalledWith(mockUsuario);
    });

    it('should hash password if provided', async () => {
      // Arrange
      const updateUsuarioDto = {
        password: 'newpassword',
      };
      
      // Act
      await service.update('1', updateUsuarioDto);
      
      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword', 10);
    });
  });

  describe('remove', () => {
    it('should remove a user', async () => {
      // Act
      await service.remove('1');
      
      // Assert
      expect(mockUsuarioRepository.delete).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      mockUsuarioRepository.delete.mockResolvedValueOnce({ affected: 0 });
      
      // Act & Assert
      await expect(service.remove('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateLastLogin', () => {
    it('should update last login date', async () => {
      // Act
      await service.updateLastLogin('1');
      
      // Assert
      expect(mockUsuarioRepository.update).toHaveBeenCalledWith('1', {
        ultimaConexion: expect.any(Date),
      });
    });
  });
});
