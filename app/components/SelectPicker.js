import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

/**
 * Component for creating a custom dropdown selector
 * @param {Object} props - Component properties
 * @param {String} props.value - Current selected value
 * @param {Function} props.onValueChange - Function called when value changes
 * @param {Array} props.options - Array of options to display
 * @param {String} props.placeholder - Placeholder text when no value is selected
 * @param {String} props.label - Label text for the selector
 */
const SelectPicker = ({ value, onValueChange, options, placeholder, label }) => {
  const [isVisible, setIsVisible] = useState(false);
  
  const areValuesEqual = (val1, val2) => {
    if (!val1 && !val2) return true;
    if (!val1 || !val2) return false;
    
    // Normalizar a string y comparar sin importar mayúsculas/minúsculas
    return String(val1).toLowerCase().trim() === String(val2).toLowerCase().trim();
  };

  const toggleModal = () => {
    setIsVisible(!isVisible);
  };

  const selectOption = (option) => {
    onValueChange(option);
    toggleModal();
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity 
        style={styles.selector}
        onPress={toggleModal}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.valueText,
          !value && styles.placeholderText
        ]}>
          {value || placeholder || 'Seleccionar'}
        </Text>
        <Ionicons name="chevron-down" size={24} color="#e0fe10" />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={toggleModal}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={toggleModal}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label || 'Seleccionar opción'}</Text>
              <TouchableOpacity onPress={toggleModal}>
                <Ionicons name="close" size={24} color="#232323" />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={options}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity 
                  style={[
                    styles.optionItem,
                    areValuesEqual(value, item) && styles.selectedOption
                  ]} 
                  onPress={() => selectOption(item)}
                >
                  <Text style={[
                    styles.optionText,
                    value === item && styles.selectedOptionText
                  ]}>
                    {item}
                  </Text>
                  {areValuesEqual(value, item) && (
                    <Ionicons name="checkmark" size={24} color="#e0fe10" />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    marginBottom: 16
  },
  label: {
    color: '#232323',
    marginBottom: 8,
    fontSize: 16,
    fontWeight: '500'
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 5,
    padding: 12,
    borderWidth: 0,
  },
  valueText: {
    color: '#232323',
    fontSize: 16
  },
  placeholderText: {
    color: '#777'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    width: width * 0.85,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    maxHeight: '70%',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0fe10'
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#e0fe10',
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#232323'
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee'
  },
  selectedOption: {
    backgroundColor: '#f0ffd0'
  },
  optionText: {
    fontSize: 16,
    color: '#232323'
  },
  selectedOptionText: {
    fontWeight: 'bold',
    color: '#232323'
  }
});

export default SelectPicker;
