version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: wodleague-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: wodleague
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - wodleague-network

  redis:
    image: redis:7-alpine
    container_name: wodleague-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wodleague-network

networks:
  wodleague-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
