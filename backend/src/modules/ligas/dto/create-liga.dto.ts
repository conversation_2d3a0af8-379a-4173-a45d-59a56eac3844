import { IsBoolean, IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { EstadoLiga } from '../entities/liga.entity';

export class CreateLigaDto {
  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsNotEmpty()
  @IsString()
  descripcion: string;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaInicio: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaFin: Date;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  duracionSemanas: number;

  @IsOptional()
  @IsEnum(EstadoLiga)
  estado?: EstadoLiga;

  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  precioInscripcion: number;

  @IsOptional()
  @IsBoolean()
  tieneDescuentoEarly?: boolean;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  precioEarly?: number;

  @IsOptional()
  @IsBoolean()
  bonusConsistencia?: boolean;

  @IsOptional()
  @IsBoolean()
  categoriaRx?: boolean;

  @IsOptional()
  @IsBoolean()
  categoriaIntermedio?: boolean;

  @IsOptional()
  @IsBoolean()
  categoriaScaled?: boolean;

  @IsOptional()
  @IsBoolean()
  generoMasculino?: boolean;

  @IsOptional()
  @IsBoolean()
  generoFemenino?: boolean;
}
