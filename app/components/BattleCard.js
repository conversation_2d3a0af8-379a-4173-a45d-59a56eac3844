import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const BattleCard = ({ battle, onPress }) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.card}>
        <View style={styles.wodInfo}>
          <View style={styles.wodHeader}>
            <Text style={styles.wodTitle} numberOfLines={1}>
              {battle.wod?.title || 'WOD no asignado'}
            </Text>
            <View style={styles.levelContainer}>
              <Icon name="fitness-center" size={14} color="#4a90e2" />
              <Text style={styles.levelText}>{battle.wod?.level || 'Nivel'}</Text>
            </View>
          </View>
        </View>

        <View style={styles.vsContainer}>
          <Text style={styles.vsText}>VS</Text>
        </View>
        
        <View style={styles.content}>
          <View style={styles.playerSide}>
            <View style={styles.avatarContainer}>
              <Icon name="person" size={40} color="#4a90e2" />
            </View>
            <Text style={styles.playerName} numberOfLines={1}>
              {battle.creatorName || 'Player'}
            </Text>
          </View>

          <View style={styles.divider} />

          <View style={styles.playerSide}>
            <View style={styles.avatarContainer}>
              <Icon name="person" size={40} color="#4a90e2" />
            </View>
            <Text style={styles.playerName} numberOfLines={1}>
              {battle.participants?.[0]?.username || 'Waiting...'}
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <View style={styles.statItem}>
            <Icon name="group" size={20} color="#4a90e2" />
            <Text style={styles.statText}>
              {battle.participants?.length || 0}/2
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="alarm" size={20} color="#4a90e2" />
            <Text style={styles.statText}>
              {battle.wod?.duration || 0} min
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="timer" size={20} color="#4a90e2" />
            <Text style={styles.statText}>
              {(() => {
                switch (battle.battleStatus) {
                  case 'pending':
                    return 'Pendiente';
                  case 'in_progress':
                    return 'En curso';
                  case 'completed':
                    return 'Finalizada';
                  case 'canceled':
                    return 'Cancelada';
                  default:
                    return 'Pendiente';
                }
              })()}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 300,
    marginHorizontal: 6,
    marginVertical: 6,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  wodInfo: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  wodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 2,
  },
  wodTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 6,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 6,
  },
  levelText: {
    marginLeft: 3,
    fontSize: 11,
    color: '#666',
  },
  vsContainer: {
    position: 'absolute',
    top: '38%',
    left: 0,
    right: 0,
    zIndex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  vsText: {
    fontSize: 32,
    fontWeight: '900',
    color: '#4a90e2',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 12,
  },
  playerSide: {
    flex: 1,
    alignItems: 'center',
  },
  divider: {
    width: 1,
    height: '100%',
    backgroundColor: '#eee',
    marginHorizontal: 12,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  playerName: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
});

export default BattleCard;
