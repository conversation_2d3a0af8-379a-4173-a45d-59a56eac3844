import * as React from 'react';
import { useState } from 'react';
import { useLogin, useNotify, Form, TextInput, Button, LoginFormProps } from 'react-admin';
import { Box, Card, CardContent, Typography, Container } from '@mui/material';

export const CustomLoginPage = () => {
    const [loading, setLoading] = useState(false);
    const login = useLogin();
    const notify = useNotify();

    const handleSubmit = (params: any) => {
        setLoading(true);
        login(
            params,
            '/dashboard'
        ).catch((error: Error) => {
            setLoading(false);
            notify(
                'Error de autenticación: verifica tus credenciales',
                { type: 'error', autoHideDuration: 5000 }
            );
        });
    };

    return (
        <Container component="main" maxWidth="xs" sx={{ mt: 8 }}>
            <Card
                variant="outlined"
                sx={{
                    p: 2,
                    borderRadius: 2,
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                }}
            >
                <CardContent>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                        }}
                    >
                        <Typography component="h1" variant="h5" gutterBottom>
                            🏋️‍♀️ The WOD League 🏋️‍♂️
                        </Typography>
                        <Typography variant="subtitle1" color="textSecondary" sx={{ mb: 3 }}>
                            Panel de Administración
                        </Typography>
                        <Form onSubmit={handleSubmit}>
                            <Box sx={{ mb: 2 }}>
                                <TextInput
                                    autoComplete="email"
                                    source="username"
                                    label="Email"
                                    disabled={loading}
                                    fullWidth
                                    required
                                />
                            </Box>
                            <Box sx={{ mb: 2 }}>
                                <TextInput
                                    source="password"
                                    label="Contraseña"
                                    type="password"
                                    disabled={loading}
                                    fullWidth
                                    required
                                />
                            </Box>
                            <Box sx={{ mt: 2 }}>
                                <Button
                                    variant="contained"
                                    type="submit"
                                    color="primary"
                                    disabled={loading}
                                    fullWidth
                                >
                                    {loading ? 'Iniciando sesión...' : 'Iniciar sesión'}
                                </Button>
                            </Box>
                        </Form>
                    </Box>
                </CardContent>
            </Card>
        </Container>
    );
};
