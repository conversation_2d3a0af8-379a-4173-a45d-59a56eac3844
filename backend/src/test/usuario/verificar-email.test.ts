import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario, EstadoUsuario } from '../../modules/usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { JwtModule, JwtService } from '@nestjs/jwt';
import jwtConfig from '../../config/jwt.config';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { DatabaseTestHelper, UsuariosTestHelper, AuthTestHelper, usuariosFixtures } from '../helper';

/**
 * Test for Email Verification Process
 * 
 * This test verifies the complete email verification flow:
 * 1. User registration with pending verification
 * 2. Generation of verification token
 * 3. Validation of verification token
 * 4. Activation of user account
 */
describe('Email Verification Tests', () => {
  let usuariosService: UsuariosService;
  let jwtService: JwtService;
  let testUserId: string;

  // Mock email service for testing purposes
  const mockEmailService = {
    sendVerificationEmail: jest.fn().mockResolvedValue(true)
  };

  beforeAll(async () => {
    // Verificar conexión a la base de datos antes de iniciar las pruebas
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Crear primero el módulo base con servicios de usuarios usando el helper
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);
    
    // Configurar el JWT para pruebas de verificación de email
    const authModule = await Test.createTestingModule({
      imports: [
        JwtModule.registerAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => ({
            secret: configService.get('jwt.secret'),
            signOptions: { expiresIn: '24h' },
          }),
        }),
        ConfigModule.forRoot({
          isGlobal: true,
          load: [jwtConfig],
          envFilePath: '.env.development',
        }),
      ],
      providers: [
        {
          provide: 'EmailService',
          useValue: mockEmailService
        }
      ]
    }).compile();
    
    jwtService = authModule.get<JwtService>(JwtService);

    // Create a test user with initial INACTIVE status
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = {
      nombre: 'Usuario Verificación',
      alias: 'usuario_verificacion',
      email: '<EMAIL>',
      password: hashedPassword,
      nivel: NivelUsuario.INTERMEDIO,
      genero: GeneroUsuario.MASCULINO,
      estado: EstadoUsuario.INACTIVO, // Inactive until verified
    };

    // Clean up previous test data
    try {
      const existingUser = await usuariosService['usuariosRepository'].findOne({
        where: { email: testUser.email },
      });
      
      if (existingUser) {
        await usuariosService['usuariosRepository'].delete(existingUser.id);
      }
    } catch (error) {
      // Ignore errors, user possibly doesn't exist
    }

    // Create new user for tests
    const createdUser = await usuariosService['usuariosRepository'].save(
      usuariosService['usuariosRepository'].create(testUser)
    );
    
    testUserId = createdUser.id;
  });

  afterAll(async () => {
    // Clean up: remove test user
    try {
      await usuariosService['usuariosRepository'].delete(testUserId);
    } catch (error) {
      // Ignore errors during cleanup
    }
  });

  // Simulated function to generate verification token
  function generateVerificationToken(userId: string, email: string): string {
    const payload = {
      sub: userId,
      email,
      type: 'email-verification'
    };
    
    return jwtService.sign(payload, { expiresIn: '24h' });
  }

  // Simulated function to send verification email
  async function sendVerificationEmail(userId: string, email: string): Promise<boolean> {
    // Generate verification token
    const token = generateVerificationToken(userId, email);
    
    // Send verification email with token
    await mockEmailService.sendVerificationEmail(email, token);
    
    return true;
  }

  // Simulated function to verify email token
  async function verifyEmailToken(token: string): Promise<{ success: boolean, message: string }> {
    try {
      // Verify token
      const payload = jwtService.verify(token);
      
      // Check token type
      if (payload.type !== 'email-verification') {
        return { success: false, message: 'Invalid token type' };
      }
      
      // Find user
      const user = await usuariosService.findOne(payload.sub);
      
      // Check if user exists
      if (!user) {
        return { success: false, message: 'User not found' };
      }
      
      // Check if email matches
      if (user.email !== payload.email) {
        return { success: false, message: 'Email mismatch' };
      }
      
      // Check if already verified
      if (user.estado === EstadoUsuario.ACTIVO) {
        return { success: false, message: 'Email already verified' };
      }
      
      // Activate user
      await usuariosService['usuariosRepository'].update(user.id, {
        estado: EstadoUsuario.ACTIVO
      });
      
      return { success: true, message: 'Email verified successfully' };
    } catch (error) {
      return { success: false, message: 'Invalid or expired token' };
    }
  }

  // Test the entire email verification flow
  it('should complete the email verification flow successfully', async () => {
    // Step 1: Send verification email
    const emailSent = await sendVerificationEmail(testUserId, '<EMAIL>');
    expect(emailSent).toBe(true);
    expect(mockEmailService.sendVerificationEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.any(String)
    );
    
    // Get verification token from mock call
    const verificationToken = mockEmailService.sendVerificationEmail.mock.calls[0][1];
    
    // Step 2: Verify token
    const verificationResult = await verifyEmailToken(verificationToken);
    expect(verificationResult.success).toBe(true);
    expect(verificationResult.message).toBe('Email verified successfully');
    
    // Step 3: Check that user status was updated to ACTIVE
    const user = await usuariosService.findOne(testUserId);
    expect(user.estado).toBe(EstadoUsuario.ACTIVO);
  });

  // Test verification with invalid token
  it('should reject verification with invalid token', async () => {
    const invalidToken = 'invalid.jwt.token';
    
    const verificationResult = await verifyEmailToken(invalidToken);
    expect(verificationResult.success).toBe(false);
    expect(verificationResult.message).toBe('Invalid or expired token');
  });

  // Test verification with expired token
  it('should reject verification with expired token', async () => {
    // Construir un token manualmente con fecha de expiración en el pasado
    // para evitar problemas con la biblioteca jsonwebtoken
    const header = { alg: 'HS256', typ: 'JWT' };
    const nowInSeconds = Math.floor(Date.now() / 1000);
    const expirationTime = nowInSeconds - 3600; // Expirado hace 1 hora
    
    const payload = {
      sub: testUserId,
      email: '<EMAIL>',
      type: 'email-verification',
      iat: nowInSeconds - 7200, // Emitido hace 2 horas
      exp: expirationTime // Expirado hace 1 hora
    };
    
    // Convertir a base64
    const headerStr = Buffer.from(JSON.stringify(header)).toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
    const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
    
    // Crear una firma ficticia (no importa para probar expiración)
    const signature = 'invalid_signature_for_testing_expiration';
    
    // Formar el token JWT expirado
    const expiredToken = `${headerStr}.${payloadStr}.${signature}`;
    
    const verificationResult = await verifyEmailToken(expiredToken);
    expect(verificationResult.success).toBe(false);
    expect(verificationResult.message).toBe('Invalid or expired token');
  });

  // Test verification for already verified user
  it('should handle verification for already verified user', async () => {
    // Generate new token
    const token = generateVerificationToken(testUserId, '<EMAIL>');
    
    // Verify token (user was already activated in first test)
    const verificationResult = await verifyEmailToken(token);
    expect(verificationResult.success).toBe(false);
    expect(verificationResult.message).toBe('Email already verified');
  });

  // Test verification with wrong email
  it('should reject verification with email mismatch', async () => {
    // Generate token with wrong email
    const payload = {
      sub: testUserId,
      email: '<EMAIL>',
      type: 'email-verification'
    };
    
    const tokenWithWrongEmail = jwtService.sign(payload);
    
    const verificationResult = await verifyEmailToken(tokenWithWrongEmail);
    expect(verificationResult.success).toBe(false);
    expect(verificationResult.message).toBe('Email mismatch');
  });
});
