{"name": "thewodleague", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-picker/picker": "^2.9.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.0", "@react-navigation/native-stack": "^7.0.0", "@react-navigation/stack": "^7.0.0", "axios": "^1.7.7", "expo": "~52.0.0", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "^4.0.20", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "firebase": "^11.0.1", "jwt-decode": "^4.0.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg-charts": "^5.4.0", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^18.0.0", "date-fns": "^4.1.0"}, "private": true}