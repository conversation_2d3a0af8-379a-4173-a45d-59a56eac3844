import { registerAs } from '@nestjs/config';

export default registerAs('database', () => {
  const dbType = process.env.DB_TYPE || 'postgres';

  if (dbType === 'sqlite') {
    return {
      type: 'sqlite',
      database: process.env.DB_DATABASE || ':memory:',
      synchronize: false, // Desactivado para prevenir cambios accidentales en la estructura de la DB
      logging: process.env.DB_LOGGING === 'true',
      entities: ['dist/**/*.entity{.ts,.js}'],
      migrations: ['dist/migrations/*{.ts,.js}'],
      cli: {
        migrationsDir: 'src/migrations',
      },
    };
  }

  return {
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'wodleague',
    synchronize: false, // Desactivado para prevenir cambios accidentales en la estructura de la DB
    logging: process.env.DB_LOGGING === 'true',
    entities: ['dist/**/*.entity{.ts,.js}'],
    migrations: ['dist/migrations/*{.ts,.js}'],
    cli: {
      migrationsDir: 'src/migrations',
    },
  };
});
