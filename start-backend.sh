#!/bin/bash

# ======================================================
# 🚀 The WOD League - Backend Startup Script
# ======================================================
# This script helps developers to start the NestJS backend
# environment with proper configurations and checks.
# ======================================================

# Set colors for better visual feedback
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}🏋️ THE WOD LEAGUE - NESTJS BACKEND STARTUP 🏋️${NC}"
echo -e "${BLUE}==================================================${NC}"

# Navigate to backend directory
cd "$(dirname "$0")/backend" || { 
  echo -e "${RED}❌ Error: Could not navigate to backend directory${NC}"; 
  exit 1; 
}

# Check if node modules are installed
if [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}📦 Node modules not found. Installing dependencies...${NC}"
  npm install || { 
    echo -e "${RED}❌ Error installing dependencies${NC}"; 
    exit 1; 
  }
  echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
  echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Check for environment variables
if [ ! -f ".env" ] && [ -f ".env.development" ]; then
  echo -e "${YELLOW}⚙️ .env file not found. Creating from .env.development...${NC}"
  cp .env.development .env
  echo -e "${GREEN}✅ .env file created from example${NC}"
  echo -e "${YELLOW}⚠️ Please update .env with your actual settings${NC}"
elif [ ! -f ".env" ] && [ ! -f ".env.development" ]; then
  echo -e "${RED}❌ Neither .env nor .env.development files found${NC}"
  echo -e "${YELLOW}⚠️ You may need to create an .env file manually${NC}"
else
  echo -e "${GREEN}✅ Environment file found${NC}"
fi

# Determine development mode
MODE="start:dev"
if [ "$1" == "--production" ]; then
  MODE="start:prod"
  echo -e "${YELLOW}🌐 Starting in PRODUCTION mode${NC}"
elif [ "$1" == "--build" ]; then
  echo -e "${YELLOW}🏗️ Building NestJS before starting...${NC}"
  npm run build || {
    echo -e "${RED}❌ Build failed${NC}";
    exit 1;
  }
  echo -e "${GREEN}✅ Build completed successfully${NC}"
  MODE="start:prod"
else
  echo -e "${YELLOW}🛠️ Starting in DEVELOPMENT mode${NC}"
fi

# Check for Docker services
echo -e "${YELLOW}🔍 Checking Docker services...${NC}"
if docker ps | grep -q 'postgres'; then
  echo -e "${GREEN}✅ PostgreSQL service is running in Docker${NC}"
else
  echo -e "${RED}❌ PostgreSQL service is NOT running in Docker${NC}"
  echo -e "${YELLOW}⚠️ You must run the database before starting the server${NC}"
  echo -e "${BLUE}💻 Run this command to start the database:${NC}"
  echo -e "${GREEN}   docker-compose up -d postgres${NC}"
  echo -e "${RED}⛔ Stopping execution. Database is required to run the server.${NC}"
  exit 1
fi

if docker ps | grep -q 'redis'; then
  echo -e "${GREEN}✅ Redis service is running in Docker${NC}"
else
  echo -e "${YELLOW}⚠️ Redis service might not be running in Docker${NC}"
  echo -e "${YELLOW}⚠️ Make sure to run docker-compose up before starting the server${NC}"
fi

# Starting the NestJS server
echo -e "${GREEN}🚀 Starting NestJS server...${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "${YELLOW}👉 Server will be available at:${NC}"
echo -e "${YELLOW}   http://localhost:3000/api${NC}"
echo -e "${BLUE}==================================================${NC}"

# Start NestJS in the appropriate mode
npm run $MODE
