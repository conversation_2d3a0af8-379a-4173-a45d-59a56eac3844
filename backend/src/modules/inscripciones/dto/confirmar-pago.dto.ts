import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { MetodoPago } from '../entities/inscripcion.entity';

export class ConfirmarPagoDto {
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaPago: Date;

  @IsNotEmpty()
  @IsEnum(MetodoPago)
  metodoPago: MetodoPago;

  @IsNotEmpty()
  @IsString()
  referenciaPago: string;

  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  montoPagado: number;

  @IsOptional()
  @IsString()
  comentarios?: string;
}
