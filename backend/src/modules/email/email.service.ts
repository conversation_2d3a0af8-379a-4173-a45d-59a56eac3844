import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { EmailConfig, EmailOptions } from './email.interfaces';

/**
 * Email Service
 * 
 * This service handles all email communications using Nodemailer
 * with support for transactional emails and templates.
 */
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;
  private emailConfig: EmailConfig;

  constructor(private configService: ConfigService) {
    try {
      // Log environment variables for debugging (not passwords)
      this.logger.log(`EMAIL_HOST: ${process.env.EMAIL_HOST}`);
      this.logger.log(`EMAIL_PORT: ${process.env.EMAIL_PORT}`);
      this.logger.log(`EMAIL_USER: ${process.env.EMAIL_USER}`);
      this.logger.log(`Using EMAIL_FROM: ${process.env.EMAIL_FROM}`);
      
      // Initialize email config from environment variables
      this.emailConfig = {
        host: process.env.EMAIL_HOST || 'smtp.hostinger.com',
        port: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT) : 587,
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER || '',
          pass: process.env.EMAIL_PASSWORD || '',
        },
        from: process.env.EMAIL_FROM || 'The WOD League <<EMAIL>>',
      };
      
      this.logger.log('Email configuration loaded successfully');
      
      // Create the Nodemailer transporter
      this.initializeTransporter();
    } catch (error) {
      this.logger.error(`Error initializing email service: ${error.message}`);
    }
  }

  /**
   * Initialize the Nodemailer transporter with configuration
   */
  private initializeTransporter(): void {
    try {
      // Log transport configuration (excluding sensitive data)
      this.logger.log(`Creating transport with: ${this.emailConfig.host}:${this.emailConfig.port}, secure: ${this.emailConfig.secure}`);
      
      const transportConfig = {
        host: this.emailConfig.host,
        port: this.emailConfig.port,
        secure: this.emailConfig.secure,
        auth: {
          user: this.emailConfig.auth.user,
          pass: this.emailConfig.auth.pass,
        },
        // Agregar capa adicional de seguridad y compatibilidad
        tls: {
          rejectUnauthorized: false // Útil para desarrollo
        },
        debug: true // Habilitar logs detallados
      };
      
      this.transporter = nodemailer.createTransport(transportConfig);
      
      // Verificar la conexión
      this.transporter.verify((error, success) => {
        if (error) {
          this.logger.error(`Email verification failed: ${error.message}`);
        } else {
          this.logger.log('Email server connection verified successfully');
        }
      });
      
      this.logger.log('Email service initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize email service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send an email using the configured transporter
   * @param options Email options including recipients, subject and content
   * @returns Promise with the sending result
   */
  async sendEmail(options: EmailOptions): Promise<nodemailer.SentMessageInfo> {
    try {
      this.logger.log(`🚀 INICIANDO ENVÍO DE EMAIL --> Destinatario: ${options.to} | Asunto: ${options.subject}`);
      
      const mailOptions = {
        from: options.from || this.emailConfig.from,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      this.logger.log(`📧 Intentando enviar email a través de ${this.emailConfig.host}...`);
      const result = await this.transporter.sendMail(mailOptions);
      
      this.logger.log(`✅ EMAIL ENVIADO CORRECTAMENTE ✅`);
      this.logger.log(`📧 Destinatario: ${options.to}`);
      this.logger.log(`📧 Asunto: ${options.subject}`);
      this.logger.log(`📧 ID del mensaje: ${result.messageId}`);
      
      return result;
    } catch (error) {
      this.logger.error(`❌ ERROR AL ENVIAR EMAIL ❌`);
      this.logger.error(`📧 Destinatario: ${options.to}`);
      this.logger.error(`📧 Asunto: ${options.subject}`);
      this.logger.error(`📧 Error: ${error.message}`);
      this.logger.error(`📧 Stack: ${error.stack}`);
      throw error;
    }
  }

  /**
   * Send a verification email with token
   * @param email Recipient email address
   * @param token Verification token
   * @param username Optional username for personalization
   */
  async sendVerificationEmail(email: string, token: string, username?: string): Promise<void> {
    // El registro empieza aquí con información clara y visible
    this.logger.log(`🔔 INICIANDO PROCESO DE ENVÍO DE EMAIL DE VERIFICACIÓN 🔔`);
    this.logger.log(`📧 Destinatario: ${email}`);
    this.logger.log(`👤 Usuario: ${username || 'No especificado'}`); 
    this.logger.log(`🔑 Token generado (primeros 10 caracteres): ${token.substring(0, 10)}...`);
    
    // The app scheme for deep linking
    const appScheme = process.env.APP_SCHEME || 'thewodleague';
    this.logger.log(`🔗 Esquema de app para deep linking: ${appScheme}`);
    
    // Construct the verification URL for deep linking
    const verificationUrl = `${appScheme}://verify-email?token=${token}`;
    this.logger.log(`🌐 URL de verificación generada: ${verificationUrl}`);
    
    // Friendly display name (use username if available)
    const displayName = username || email.split('@')[0];
    
    // HTML content with branding for The WOD League
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #e63946; margin: 0;">The WOD League</h1>
          <p style="color: #457b9d; font-style: italic;">Transforma tus entrenamientos en competición</p>
        </div>
        
        <h2 style="color: #1d3557;">¡Hola, ${displayName}!</h2>
        
        <p>Gracias por unirte a The WOD League. Para completar tu registro y comenzar a participar en nuestras ligas, necesitamos verificar tu dirección de correo electrónico.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #e63946; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            VERIFICAR MI CUENTA
          </a>
        </div>
        
        <p>Si el botón no funciona, copia y pega el siguiente enlace en tu dispositivo móvil:</p>
        <p style="background-color: #f1faee; padding: 10px; border-radius: 4px; word-break: break-all;">
          ${verificationUrl}
        </p>
        
        <p>Este enlace caducará en 24 horas por razones de seguridad.</p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eaeaea; font-size: 12px; color: #777;">
          <p>Si no solicitaste esta verificación, puedes ignorar este correo electrónico.</p>
          <p>© ${new Date().getFullYear()} The WOD League - Todos los derechos reservados</p>
        </div>
      </div>
    `;

    this.logger.log(`📧 PREPARANDO EMAIL DE VERIFICACIÓN para: ${email}`);
    
    try {
      await this.sendEmail({
        to: email,
        subject: '✅ Verifica tu cuenta en The WOD League',
        html: htmlContent,
        text: `Hola ${displayName}, verifica tu cuenta en The WOD League usando este enlace: ${verificationUrl}`,
      });
      
      this.logger.log(`✅ PROCESO DE VERIFICACIÓN COMPLETADO EXITOSAMENTE para: ${email}`);
    } catch (error) {
      this.logger.error(`❌ ERROR EN PROCESO DE VERIFICACIÓN para: ${email} - ${error.message}`);
      throw error;
    }
  }

  /**
   * Envía un email de restablecimiento de contraseña al usuario
   * @param email - Dirección de correo del destinatario
   * @param token - Token de restablecimiento de contraseña
   * @param username - Nombre de usuario opcional para personalización
   * @returns Promesa que resuelve al resultado de la operación de envío
   */
  async sendPasswordResetEmail(
    email: string,
    token: string, 
    username?: string
  ): Promise<any> {
    this.logger.log(`🔔 INICIANDO PROCESO DE ENVÍO DE EMAIL DE RECUPERACIÓN DE CONTRASEÑA 🔔`);
    this.logger.log(`📧 Destinatario: ${email}`);
    this.logger.log(`👤 Usuario: ${username || 'No especificado'}`);
    this.logger.log(`🔑 Token generado (primeros 10 caracteres): ${token.substring(0, 10)}...`);

    // Obtener esquema de app para deep linking
    const appScheme = this.configService.get('APP_SCHEME') || 'thewodleague';
    this.logger.log(`🔗 Esquema de app para deep linking: ${appScheme}`);

    // Construir URL de restablecimiento para deep linking
    const resetUrl = `${appScheme}://reset-password?token=${token}`;
    this.logger.log(`🌐 URL de restablecimiento generada: ${resetUrl}`);

    // Nombre para mostrar amigable (usar username si está disponible)
    const displayName = username || email.split('@')[0];

    // Contenido HTML con branding para The WOD League
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #e63946; margin: 0;">The WOD League</h1>
          <p style="color: #457b9d; font-style: italic;">Transforma tus entrenamientos en competición</p>
        </div>
        
        <h2 style="color: #1d3557;">¡Hola, ${displayName}!</h2>
        
        <p>Has solicitado restablecer tu contraseña en The WOD League. Haz clic en el botón a continuación para crear una nueva contraseña:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #e63946; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            RESTABLECER MI CONTRASEÑA
          </a>
        </div>
        
        <p>Si el botón no funciona, copia y pega el siguiente enlace en tu dispositivo móvil:</p>
        <p style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all;">
          ${resetUrl}
        </p>
        
        <p>Si no has solicitado restablecer tu contraseña, puedes ignorar este mensaje. Tu cuenta está segura.</p>
        
        <p>Por razones de seguridad, este enlace expirará en 2 horas.</p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eaeaea; color: #777777; font-size: 12px;">
          <p>Este es un mensaje automático, por favor no respondas a este email.</p>
          <p>© ${new Date().getFullYear()} The WOD League - Todos los derechos reservados</p>
        </div>
      </div>
    `;

    this.logger.log(`📧 PREPARANDO EMAIL DE RECUPERACIÓN DE CONTRASEÑA para: ${email}`);

    try {
      // Enviar el email
      await this.sendEmail({
        to: email,
        subject: '🔑 Recuperación de contraseña en The WOD League',
        html: htmlContent,
        text: `Hola ${displayName}, has solicitado restablecer tu contraseña en The WOD League. Usa este enlace: ${resetUrl}. Si no has solicitado este cambio, ignora este mensaje.`,
      });

      this.logger.log(`✅ PROCESO DE RECUPERACIÓN DE CONTRASEÑA COMPLETADO para: ${email}`);
    } catch (error) {
      this.logger.error(`❌ ERROR EN PROCESO DE RECUPERACIÓN DE CONTRASEÑA para: ${email} - ${error.message}`);
      throw error;
    }
  }
}
