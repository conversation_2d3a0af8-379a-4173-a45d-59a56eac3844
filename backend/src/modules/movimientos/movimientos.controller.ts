import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { MovimientosService } from './movimientos.service';
import { CreateMovimientoDto } from './dto/create-movimiento.dto';
import { UpdateMovimientoDto } from './dto/update-movimiento.dto';
import { Movimiento } from './entities/movimiento.entity';

@Controller('movimientos')
export class MovimientosController {
  constructor(private readonly movimientosService: MovimientosService) {}

  @Post()
  create(@Body() createMovimientoDto: CreateMovimientoDto): Promise<Movimiento> {
    return this.movimientosService.create(createMovimientoDto);
  }

  @Get()
  findAll(@Query() query): Promise<Movimiento[]> {
    return this.movimientosService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Movimiento> {
    return this.movimientosService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string, 
    @Body() updateMovimientoDto: UpdateMovimientoDto
  ): Promise<Movimiento> {
    return this.movimientosService.update(id, updateMovimientoDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.movimientosService.remove(id);
  }
}
