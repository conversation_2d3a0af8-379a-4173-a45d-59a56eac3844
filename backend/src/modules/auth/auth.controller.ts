import { Controller, Post, Body, Get, Req, UseGuards, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { CreateUsuarioDto } from '../usuarios/dto/create-usuario.dto';
import { VerificationEmailDto } from './dto/verification-email.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { Request } from 'express';

/**
 * Controlador para la autenticación y operaciones relacionadas con credenciales
 * Gestiona el registro, inicio de sesión, verificación de email y cambio de contraseña
 */
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}
  
  /**
   * Verifica el estado del servicio de autenticación
   * @returns Estado del servicio
   */
  @Get()
  async getStatus() {
    return { status: 'Auth service running' };
  }

  /**
   * Inicia sesión con email y contraseña
   * @param loginDto Credenciales de inicio de sesión
   * @returns Token JWT y datos básicos del usuario
   */
  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  /**
   * Registra un nuevo usuario
   * @param createUsuarioDto Datos del nuevo usuario
   * @returns Datos del usuario creado y token JWT
   */
  @Post('register')
  async register(@Body() createUsuarioDto: CreateUsuarioDto) {
    return this.authService.register(createUsuarioDto);
  }
  
  /**
   * Send verification email
   */
  @Post('send-verification-email')
  sendVerificationEmail(@Body() verificationEmailDto: VerificationEmailDto) {
    return this.authService.sendVerificationEmail(verificationEmailDto);
  }
  
  /**
   * Verify email with token
   */
  @Post('verify-email')
  verifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(verifyEmailDto);
  }
  
  /**
   * Verifica si el email del usuario está verificado
   * @param req Solicitud con datos del usuario autenticado
   * @returns Estado de verificación del email y datos adicionales del perfil
   */
  @UseGuards(JwtAuthGuard)
  @Get('email-verification-status')
  async checkEmailVerification(@Req() req: Request) {
    // Verificar que req.user exista antes de intentar acceder a 'sub'
    if (!req.user) {
      throw new UnauthorizedException('Usuario no autenticado');
    }
    
    const userId = req.user['sub']; // Get user ID from JWT token
    return this.authService.checkEmailVerification(userId);
  }
  
  /**
   * Permite al usuario cambiar su contraseña
   * Requiere la contraseña actual y la nueva contraseña con confirmación
   * @param req Solicitud con datos del usuario autenticado
   * @param changePasswordDto Datos para el cambio de contraseña
   * @returns Confirmación del cambio exitoso
   */
  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(
    @Req() req: Request,
    @Body() changePasswordDto: ChangePasswordDto
  ) {
    // Verificar que req.user exista
    if (!req.user) {
      throw new UnauthorizedException('Usuario no autenticado');
    }
    
    // Verificar que las contraseñas coincidan
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new UnauthorizedException('Las contraseñas no coinciden');
    }
    
    const userId = req.user['sub']; // Obtener ID del usuario del token JWT
    return this.authService.changePassword(
      userId,
      changePasswordDto.currentPassword,
      changePasswordDto.newPassword
    );
  }
  
  /**
   * Endpoint alternativo para verificación de email (para compatibilidad)
   */
  @UseGuards(JwtAuthGuard)
  @Get('verify-status')
  async getEmailVerificationStatus(@Req() req: Request) {
    if (!req.user) {
      throw new UnauthorizedException('Usuario no autenticado');
    }
    
    const userId = req.user['sub'];
    return this.authService.checkEmailVerification(userId);
  }

  /**
   * Solicita un email de recuperación de contraseña
   * @param forgotPasswordDto DTO con el email del usuario
   * @returns Mensaje de confirmación
   */
  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  /**
   * Restablece la contraseña utilizando el token enviado por email
   * @param resetPasswordDto DTO con el token y la nueva contraseña
   * @returns Mensaje de confirmación
   */
  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }
}
