import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useAuth } from '../context/AuthContextNest';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/colors';
import authApiNest from '../api/authApiNest';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { setupVerificationTokenHandler, checkInitialURL } from '../utils/verificationTokenHandler';

const EmailVerificationScreen = ({ navigation }) => {
  const { user, refreshUser } = useAuth();
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    // Verificar el estado de verificación al cargar la pantalla
    checkVerificationStatus();

    // Configurar el manejador de tokens por URL
    const removeListener = setupVerificationTokenHandler(navigation);

    // Verificar si la app se abrió con un enlace de verificación
    checkInitialURL(navigation);

    // Implementación de backoff exponencial para verificaciones
    let checkInterval = 10000; // 10 segundos iniciales
    const maxInterval = 60000;  // Máximo 1 minuto entre verificaciones
    let timeoutId;

    const scheduleNextCheck = () => {
      timeoutId = setTimeout(() => {
        checkVerificationStatus(true); // Verificación silenciosa (sin UI)
        // Incrementar gradualmente el tiempo entre verificaciones
        checkInterval = Math.min(checkInterval * 1.5, maxInterval);
        scheduleNextCheck();
      }, checkInterval);
    };

    // Iniciar el patrón de verificación
    scheduleNextCheck();

    // Limpieza al desmontar el componente
    return () => {
      clearTimeout(timeoutId);
      removeListener();
    };
  }, []);

  useEffect(() => {
    // Manejar el contador para el botón de reenvío
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setResendDisabled(false);
    }
  }, [countdown]);

  const checkVerificationStatus = async (silent = false) => {
    try {
      if (!silent) setIsVerifying(true);

      // Obtener el email del usuario almacenado
      const userEmail = await AsyncStorage.getItem('userEmail');

      if (!userEmail) {
        console.warn('No se encontró el email del usuario en almacenamiento local');
        return;
      }

      // Verificar si el email está verificado usando la API de NestJS
      const verificationStatus = await authApiNest.checkEmailVerification();

      // Log simplificado de estado de verificación (solo si no es silencioso)
      if (!silent && verificationStatus?.verified === true) {
        console.log('✅ Email verificado correctamente');
      }

      // Si el email está verificado
      if (verificationStatus?.verified === true) {
        // Actualizar el estado del usuario en el contexto
        await refreshUser();

        if (!silent) {
          Toast.show({
            type: 'success',
            text1: '¡Email verificado!',
            text2: 'Tu cuenta ha sido verificada correctamente.',
          });
        }

        // Navegar a la pantalla principal
        navigation.replace('Main');
      }
    } catch (error) {
      console.error('Error al verificar el estado del email:', error);

      // Si es una verificación silenciosa, no mostrar errores en la UI
      if (silent) return;

      let errorMessage = 'Hubo un problema al verificar el estado de tu email. Por favor, inténtalo de nuevo.';

      // Analizar error según la respuesta del servidor
      if (error.response) {
        const status = error.response.status;

        if (status === 401) {
          errorMessage = 'Tu sesión ha expirado. Por favor, inicia sesión de nuevo.';

          // Si la sesión expiró, podemos redirigir al login
          setTimeout(() => {
            navigation.reset({
              index: 0,
              routes: [{ name: 'Login' }],
            });
          }, 1500); // Pequeño retraso para mostrar el mensaje

        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        }
      }

    } finally {
      if (!silent) setIsVerifying(false);
    }
  };

  const handleResendEmail = async () => {
    try {
      setIsVerifying(true);

      // Obtener el email del usuario almacenado o usar del contexto
      const userEmail = user?.email || await AsyncStorage.getItem('userEmail');

      if (!userEmail) {
        throw new Error('No se encontró el email del usuario');
      }

      // Enviar email de verificación usando la API de NestJS
      await authApiNest.sendVerificationEmail(userEmail);

      // Deshabilitar el botón por 60 segundos para evitar spam
      setResendDisabled(true);
      setCountdown(60);

      Toast.show({
        type: 'success',
        text1: 'Email enviado',
        text2: 'Hemos enviado un nuevo correo de verificación. Revisa tu bandeja de entrada (y carpeta de spam).',
        visibilityTime: 5000,
      });

      // Programar verificación automática después de un tiempo prudencial
      setTimeout(() => checkVerificationStatus(true), 10000);
    } catch (error) {
      console.error('Error al reenviar email de verificación:', error);

      let errorMessage = 'No se pudo enviar el email de verificación.';

      // Analizar error según la respuesta del servidor
      if (error.response) {
        const status = error.response.status;

        if (status === 429) {
          errorMessage = 'Has enviado demasiadas solicitudes. Inténtalo más tarde.';
          // Forzar deshabilitación del botón
          setResendDisabled(true);
          setCountdown(120); // Mayor tiempo de espera
        } else if (status === 401) {
          errorMessage = 'Tu sesión ha expirado. Por favor, inicia sesión de nuevo.';
          // Redirigir al login
          setTimeout(() => {
            navigation.reset({
              index: 0,
              routes: [{ name: 'Login' }],
            });
          }, 1500);
        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: errorMessage,
        visibilityTime: 4000,
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleBack = () => {
    // En lugar de usar goBack(), que puede fallar si no hay una pantalla anterior,
    // navegamos explícitamente a Login
    navigation.navigate('Login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Verificación de email</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="mail" size={50} color={Colors.SECONDARY} />
        </View>

        <Text style={styles.title}>Verifica tu correo electrónico</Text>

        <Text style={styles.description}>
          Hemos enviado un correo de verificación a:
        </Text>

        <Text style={styles.email}>{user?.email}</Text>

        <Text style={styles.instructions}>
          Por favor, revisa tu bandeja de entrada y carpeta de spam y haz clic en el enlace de verificación para activar tu cuenta.
        </Text>

        <Text style={styles.noteText}>
          Una vez que hagas clic en el enlace, regresa aquí para continuar.
        </Text>

        <TouchableOpacity
          style={[
            styles.resendButton,
            resendDisabled && styles.disabledButton
          ]}
          onPress={handleResendEmail}
          disabled={resendDisabled || isVerifying}
        >
          {isVerifying ? (
            <ActivityIndicator color={Colors.WHITE} />
          ) : (
            <Text style={styles.resendButtonText}>
              {resendDisabled
                ? `Reenviar email (${countdown}s)`
                : 'Reenviar email de verificación'}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBack}
        >
          <Text style={styles.backButtonText}>Atrás</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  noteText: {
    fontSize: 14,
    color: '#CACACA',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 18,
    fontStyle: 'italic',
  },
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.WHITE,
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 24,
    backgroundColor: Colors.PRIMARY,
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.WHITE,
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#CACACA',
    marginBottom: 8,
    textAlign: 'center',
  },
  email: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    marginBottom: 16,
    textAlign: 'center',
  },
  instructions: {
    fontSize: 16,
    color: '#CACACA',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 24,
  },
  resendButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 20,
    width: '100%',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#5a5a5a',
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  resendButtonText: {
    color: Colors.SECONDARY,
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    paddingVertical: 15,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
    width: '100%',
    alignItems: 'center',
  },
  backButtonText: {
    color: Colors.PRIMARY,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EmailVerificationScreen;
