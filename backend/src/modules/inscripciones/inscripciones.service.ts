import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Inscripcion, EstadoInscripcion, MetodoPago } from './entities/inscripcion.entity';
import { CreateInscripcionDto } from './dto/create-inscripcion.dto';
import { UpdateInscripcionDto } from './dto/update-inscripcion.dto';
import { ConfirmarPagoDto } from './dto/confirmar-pago.dto';
import { LigasService } from '../ligas/ligas.service';
import { UsuariosService } from '../usuarios/usuarios.service';
import { NivelUsuario, GeneroUsuario } from '../usuarios/entities/usuario.entity';
import { EstadoLiga } from '../ligas/entities/liga.entity';

@Injectable()
export class InscripcionesService {
  constructor(
    @InjectRepository(Inscripcion)
    private inscripcionesRepository: Repository<Inscripcion>,
    private ligasService: LigasService,
    private usuariosService: UsuariosService,
  ) {}

  async create(createInscripcionDto: CreateInscripcionDto): Promise<Inscripcion> {
    // Verificar que la liga existe y está en estado de preparación o activa
    const liga = await this.ligasService.findOne(createInscripcionDto.ligaId);
    
    if (liga.estado === EstadoLiga.FINALIZADA) {
      throw new ConflictException('No se pueden realizar inscripciones a una liga finalizada');
    }
    
    // Verificar que el usuario existe
    const usuario = await this.usuariosService.findOne(createInscripcionDto.usuarioId);
    
    // Verificar si ya existe una inscripción para este usuario y liga
    const existingInscripcion = await this.inscripcionesRepository.findOne({
      where: {
        usuarioId: createInscripcionDto.usuarioId,
        ligaId: createInscripcionDto.ligaId,
      },
    });
    
    if (existingInscripcion) {
      throw new ConflictException('Ya existe una inscripción para este usuario en esta liga');
    }
    
    // Validar la categoría
    const [nivel, genero] = createInscripcionDto.categoria.split('-');
    
    // Verificar que la liga tiene habilitada la categoría
    if (
      (nivel === 'RX' && !liga.categoriaRx) ||
      (nivel === 'Intermedio' && !liga.categoriaIntermedio) ||
      (nivel === 'Scaled' && !liga.categoriaScaled)
    ) {
      throw new BadRequestException(`La categoría ${nivel} no está habilitada para esta liga`);
    }
    
    if (
      (genero === 'Masculino' && !liga.generoMasculino) ||
      (genero === 'Femenino' && !liga.generoFemenino)
    ) {
      throw new BadRequestException(`El género ${genero} no está habilitado para esta liga`);
    }
    
    // Verificar que el usuario pertenece a la categoría
    if (
      (nivel === 'RX' && usuario.nivel !== NivelUsuario.RX) ||
      (nivel === 'Intermedio' && usuario.nivel !== NivelUsuario.INTERMEDIO) ||
      (nivel === 'Scaled' && usuario.nivel !== NivelUsuario.SCALED)
    ) {
      throw new BadRequestException(`El usuario no pertenece a la categoría ${nivel}`);
    }
    
    if (
      (genero === 'Masculino' && usuario.genero !== GeneroUsuario.MASCULINO) ||
      (genero === 'Femenino' && usuario.genero !== GeneroUsuario.FEMENINO)
    ) {
      throw new BadRequestException(`El usuario no pertenece al género ${genero}`);
    }
    
    // Verificar si aplica descuento early
    const now = new Date();
    const fechaInicio = new Date(liga.fechaInicio);
    const unMesAntes = new Date(fechaInicio);
    unMesAntes.setMonth(unMesAntes.getMonth() - 1);
    
    const esEarly = liga.tieneDescuentoEarly && now < unMesAntes;
    
    // Crear la nueva inscripción
    const newInscripcion = this.inscripcionesRepository.create({
      ...createInscripcionDto,
      fechaInscripcion: new Date(),
      esEarly,
      montoPagado: 0,
      estado: EstadoInscripcion.PENDIENTE,
    });
    
    return this.inscripcionesRepository.save(newInscripcion);
  }

  async findAll(): Promise<Inscripcion[]> {
    return this.inscripcionesRepository.find({
      relations: ['usuario', 'liga'],
    });
  }

  async findOne(id: string): Promise<Inscripcion> {
    const inscripcion = await this.inscripcionesRepository.findOne({
      where: { id },
      relations: ['usuario', 'liga'],
    });

    if (!inscripcion) {
      throw new NotFoundException(`Inscripción con ID ${id} no encontrada`);
    }

    return inscripcion;
  }

  async update(id: string, updateInscripcionDto: UpdateInscripcionDto): Promise<Inscripcion> {
    const inscripcion = await this.findOne(id);
    
    // Actualizar la inscripción
    const updatedInscripcion = this.inscripcionesRepository.merge(inscripcion, updateInscripcionDto);
    return this.inscripcionesRepository.save(updatedInscripcion);
  }

  async remove(id: string): Promise<void> {
    const result = await this.inscripcionesRepository.delete(id);
    
    if (result.affected === 0) {
      throw new NotFoundException(`Inscripción con ID ${id} no encontrada`);
    }
  }

  async findByUsuario(usuarioId: string): Promise<Inscripcion[]> {
    return this.inscripcionesRepository.find({
      where: { usuarioId },
      relations: ['liga'],
    });
  }

  async findByLiga(ligaId: string): Promise<Inscripcion[]> {
    return this.inscripcionesRepository.find({
      where: { ligaId },
      relations: ['usuario'],
    });
  }

  async findByUsuarioAndLiga(usuarioId: string, ligaId: string): Promise<Inscripcion> {
    const inscripcion = await this.inscripcionesRepository.findOne({
      where: { usuarioId, ligaId },
      relations: ['usuario', 'liga'],
    });

    if (!inscripcion) {
      throw new NotFoundException(`Inscripción no encontrada para el usuario ${usuarioId} y liga ${ligaId}`);
    }

    return inscripcion;
  }

  async confirmarPago(id: string, confirmarPagoDto: ConfirmarPagoDto): Promise<Inscripcion> {
    const inscripcion = await this.findOne(id);
    
    if (inscripcion.estado === EstadoInscripcion.PAGADA) {
      throw new ConflictException('La inscripción ya ha sido pagada');
    }
    
    if (inscripcion.estado === EstadoInscripcion.CANCELADA || inscripcion.estado === EstadoInscripcion.RECHAZADA) {
      throw new ConflictException('No se puede confirmar el pago de una inscripción cancelada o rechazada');
    }
    
    // Verificar el monto pagado
    const liga = await this.ligasService.findOne(inscripcion.ligaId);
    const montoCorrecto = inscripcion.esEarly ? liga.precioEarly : liga.precioInscripcion;
    
    if (confirmarPagoDto.montoPagado < montoCorrecto) {
      throw new BadRequestException(`El monto pagado (${confirmarPagoDto.montoPagado}) es menor al precio de la inscripción (${montoCorrecto})`);
    }
    
    // Actualizar la inscripción
    inscripcion.estado = EstadoInscripcion.PAGADA;
    inscripcion.fechaPago = confirmarPagoDto.fechaPago;
    inscripcion.metodoPago = confirmarPagoDto.metodoPago;
    inscripcion.referenciaPago = confirmarPagoDto.referenciaPago;
    inscripcion.montoPagado = confirmarPagoDto.montoPagado;
    
    if (confirmarPagoDto.comentarios) {
      inscripcion.comentarios = confirmarPagoDto.comentarios;
    }
    
    return this.inscripcionesRepository.save(inscripcion);
  }

  async cancelarInscripcion(id: string, comentarios?: string): Promise<Inscripcion> {
    const inscripcion = await this.findOne(id);
    
    if (inscripcion.estado === EstadoInscripcion.PAGADA) {
      throw new ConflictException('No se puede cancelar una inscripción pagada');
    }
    
    if (inscripcion.estado === EstadoInscripcion.CANCELADA || inscripcion.estado === EstadoInscripcion.RECHAZADA) {
      throw new ConflictException('La inscripción ya ha sido cancelada o rechazada');
    }
    
    // Actualizar la inscripción
    inscripcion.estado = EstadoInscripcion.CANCELADA;
    
    if (comentarios) {
      inscripcion.comentarios = comentarios;
    }
    
    return this.inscripcionesRepository.save(inscripcion);
  }

  async rechazarInscripcion(id: string, comentarios: string): Promise<Inscripcion> {
    const inscripcion = await this.findOne(id);
    
    if (inscripcion.estado === EstadoInscripcion.PAGADA) {
      throw new ConflictException('No se puede rechazar una inscripción pagada');
    }
    
    if (inscripcion.estado === EstadoInscripcion.CANCELADA || inscripcion.estado === EstadoInscripcion.RECHAZADA) {
      throw new ConflictException('La inscripción ya ha sido cancelada o rechazada');
    }
    
    // Actualizar la inscripción
    inscripcion.estado = EstadoInscripcion.RECHAZADA;
    inscripcion.comentarios = comentarios;
    
    return this.inscripcionesRepository.save(inscripcion);
  }

  async findByEstado(estado: EstadoInscripcion): Promise<Inscripcion[]> {
    return this.inscripcionesRepository.find({
      where: { estado },
      relations: ['usuario', 'liga'],
    });
  }

  async findPendientesPago(): Promise<Inscripcion[]> {
    return this.inscripcionesRepository.find({
      where: { estado: EstadoInscripcion.PENDIENTE },
      relations: ['usuario', 'liga'],
    });
  }

  async findRecientes(dias: number = 7): Promise<Inscripcion[]> {
    const fechaLimite = new Date();
    fechaLimite.setDate(fechaLimite.getDate() - dias);
    
    return this.inscripcionesRepository.find({
      where: {
        fechaInscripcion: Between(fechaLimite, new Date()),
      },
      relations: ['usuario', 'liga'],
      order: {
        fechaInscripcion: 'DESC',
      },
    });
  }

  async contarInscripcionesPorLiga(ligaId: string): Promise<{ total: number, pagadas: number, pendientes: number, canceladas: number, rechazadas: number }> {
    const inscripciones = await this.findByLiga(ligaId);
    
    const total = inscripciones.length;
    const pagadas = inscripciones.filter(i => i.estado === EstadoInscripcion.PAGADA).length;
    const pendientes = inscripciones.filter(i => i.estado === EstadoInscripcion.PENDIENTE).length;
    const canceladas = inscripciones.filter(i => i.estado === EstadoInscripcion.CANCELADA).length;
    const rechazadas = inscripciones.filter(i => i.estado === EstadoInscripcion.RECHAZADA).length;
    
    return { total, pagadas, pendientes, canceladas, rechazadas };
  }

  async contarInscripcionesPorCategoria(ligaId: string): Promise<{ categoria: string, total: number, pagadas: number }[]> {
    const inscripciones = await this.findByLiga(ligaId);
    const categorias = [...new Set(inscripciones.map(i => i.categoria))];
    
    return categorias.map(categoria => {
      const inscripcionesCategoria = inscripciones.filter(i => i.categoria === categoria);
      const total = inscripcionesCategoria.length;
      const pagadas = inscripcionesCategoria.filter(i => i.estado === EstadoInscripcion.PAGADA).length;
      
      return { categoria, total, pagadas };
    });
  }
}
