import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { BoxesService } from './boxes.service';
import { CreateBoxDto } from './dto/create-box.dto';
import { UpdateBoxDto } from './dto/update-box.dto';
import { Box } from './entities/box.entity';

@Controller('boxes')
export class BoxesController {
  constructor(private readonly boxesService: BoxesService) {}

  @Post()
  create(@Body() createBoxDto: CreateBoxDto): Promise<Box> {
    return this.boxesService.create(createBoxDto);
  }

  @Get()
  findAll(): Promise<Box[]> {
    return this.boxesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Box> {
    return this.boxesService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateBoxDto: UpdateBoxDto): Promise<Box> {
    return this.boxesService.update(id, updateBoxDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.boxesService.remove(id);
  }

  @Get('owner/:ownerId')
  findByOwner(@Param('ownerId') ownerId: string): Promise<Box[]> {
    return this.boxesService.findByOwner(ownerId);
  }

  @Get(':id/miembros')
  findMiembros(@Param('id') id: string): Promise<Box> {
    return this.boxesService.findMiembros(id);
  }
}
