import React, { useState, useEffect } from 'react';
import { useRecordContext, useNotify, useRefresh, useDataProvider } from 'react-admin';
import { 
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  FormHelperText
} from '@mui/material';
import SecurityIcon from '@mui/icons-material/Security';
import { updateUserRole } from '../../dataProvider';

/**
 * Custom button to change user role
 * Uses the protected endpoint /usuarios/:id/rol
 */
const ChangeRoleButton = () => {
  const record = useRecordContext();
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();
  
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newRole, setNewRole] = useState(record?.rol || 'Usuario');
  const [boxId, setBoxId] = useState(record?.boxId || '');
  const [error, setError] = useState('');
  const [boxes, setBoxes] = useState<Array<{id: string, nombre: string}>>([]);
  const [loadingBoxes, setLoadingBoxes] = useState(false);
  
  // Cargar la lista de boxes cuando se abre el modal
  useEffect(() => {
    if (open && record) {
      setLoadingBoxes(true);
      dataProvider.getList('boxes', { 
        pagination: { page: 1, perPage: 100 },
        sort: { field: 'nombre', order: 'ASC' },
        filter: {}
      })
      .then(response => {
        setBoxes(response.data || []);
      })
      .catch(error => {
        console.error('Error al cargar los boxes:', error);
        setError('No se pudieron cargar los boxes disponibles');
      })
      .finally(() => {
        setLoadingBoxes(false);
      });
    }
  }, [open, dataProvider, record]);
  
  // Can't render button if no record is available
  if (!record) return null;
  
  const handleClickOpen = () => {
    setOpen(true);
    setNewRole(record.rol || 'Usuario');
    setBoxId(record.boxId || '');
  };

  const handleClose = () => {
    setOpen(false);
    setError('');
  };

  const handleRoleChange = (event: SelectChangeEvent) => {
    setNewRole(event.target.value);
    // Reset error when role changes
    setError('');
  };

  const handleBoxChange = (value: any) => {
    setBoxId(value);
    // Reset error when box changes
    setError('');
  };

  const handleSubmit = async () => {
    // Validate that BoxOwner has a box assigned
    if (newRole === 'BoxOwner' && !boxId) {
      setError('Un BoxOwner debe tener un box asignado');
      return;
    }

    setLoading(true);
    
    try {
      // Aseguramos que el id sea una cadena (string)
      const userId = record.id?.toString() || '';
      
      if (!userId) {
        setError('ID de usuario no válido');
        setLoading(false);
        return;
      }
      
      const result = await updateUserRole(userId, newRole, boxId);
      
      if (result.success) {
        notify('Rol actualizado correctamente', { type: 'success' });
        handleClose();
        refresh(); // Refresh data to see the updated role
      } else {
        setError(result.error || 'Error al actualizar el rol');
      }
    } catch (err: any) {
      setError(err.message || 'Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        onClick={handleClickOpen}
        size="small"
        startIcon={<SecurityIcon />}
        color="primary"
        variant="outlined"
        sx={{ marginLeft: 1 }}
      >
        Cambiar Rol
      </Button>
      
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Cambiar rol de usuario: {record.nombre}</DialogTitle>
        
        <DialogContent>
          <DialogContentText sx={{ marginBottom: 2 }}>
            Selecciona el nuevo rol para el usuario. Recuerda que un BoxOwner requiere tener un box asignado.
          </DialogContentText>
          
          <FormControl fullWidth sx={{ marginTop: 2, marginBottom: 2 }}>
            <InputLabel id="role-select-label">Rol</InputLabel>
            <Select
              labelId="role-select-label"
              value={newRole}
              label="Rol"
              onChange={handleRoleChange}
              disabled={loading}
            >
              <MenuItem value="Usuario">Usuario</MenuItem>
              <MenuItem value="BoxOwner">Dueño de Box</MenuItem>
              <MenuItem value="Admin">Administrador</MenuItem>
            </Select>
          </FormControl>
          
          {newRole === 'BoxOwner' && (
            <FormControl fullWidth sx={{ marginBottom: 2 }}>
              <InputLabel>Box</InputLabel>
              <Select
                value={boxId}
                label="Box"
                onChange={(e) => handleBoxChange(e.target.value)}
                disabled={loading || loadingBoxes}
                error={newRole === 'BoxOwner' && !boxId}
              >
                <MenuItem value="">
                  <em>Selecciona un box</em>
                </MenuItem>
                {loadingBoxes ? (
                  <MenuItem disabled>
                    <em>Cargando boxes...</em>
                  </MenuItem>
                ) : (
                  boxes.map(box => (
                    <MenuItem 
                      key={box.id} 
                      value={box.id}
                      selected={box.id === record.boxId}
                    >
                      {box.nombre} {box.id === record.boxId ? '(Box actual)' : ''}
                    </MenuItem>
                  ))
                )}
              </Select>
              {newRole === 'BoxOwner' && !boxId && (
                <FormHelperText error>Un BoxOwner debe tener un box asignado</FormHelperText>
              )}
            </FormControl>
          )}
          
          {error && (
            <DialogContentText color="error" sx={{ marginTop: 2 }}>
              {error}
            </DialogContentText>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} color="primary" disabled={loading}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSubmit} 
            color="primary" 
            variant="contained" 
            disabled={loading || (newRole === 'BoxOwner' && !boxId)}
          >
            {loading ? 'Actualizando...' : 'Guardar Cambios'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ChangeRoleButton;
