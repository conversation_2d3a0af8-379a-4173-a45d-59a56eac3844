import React from 'react';
import {
  Autocomplete,
  TextField,
  CircularProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { ApiMovimiento, MovimientoSeleccionado } from './tipos';

interface MovimientoBuscadorProps {
  movimientos: ApiMovimiento[];
  isLoading: boolean;
  value: MovimientoSeleccionado | null;
  onChange: (movimiento: MovimientoSeleccionado | null) => void;
}

const MovimientoBuscador: React.FC<MovimientoBuscadorProps> = ({
  movimientos,
  isLoading,
  value,
  onChange
}) => {
  return (
    <Autocomplete
      value={value ? value.movimiento : null}
      onChange={(event, newValue) => {
        if (newValue) {
          onChange({
            id: newValue.id,
            nombre: newValue.nombre,
            movimiento: newValue,
            categorias: {
              rx: true,
              int: true,
              sc: true
            },
            generos: {
              masculino: true,
              femenino: true
            },
            repeticiones: {
              rx: {
                masculino: 1,
                femenino: 1
              },
              int: {
                masculino: 1,
                femenino: 1
              },
              sc: {
                masculino: 1,
                femenino: 1
              }
            },
            pesos: {
              rx: {
                masculino: undefined,
                femenino: undefined
              },
              int: {
                masculino: undefined,
                femenino: undefined
              },
              sc: {
                masculino: undefined,
                femenino: undefined
              }
            }
          });
        } else {
          onChange(null);
        }
      }}
      filterOptions={(options, params) => {
        const filtered = options.filter(option => 
          option.nombre.toLowerCase().includes(params.inputValue.toLowerCase())
        );
        return filtered;
      }}
      options={movimientos || []}
      getOptionLabel={(option) => option.nombre}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Buscar movimiento"
          variant="outlined"
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <>
                <SearchIcon color="action" sx={{ mr: 1 }} />
                {params.InputProps.startAdornment}
              </>
            ),
            endAdornment: (
              <>
                {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
    />
  );
};

export default MovimientoBuscador;
