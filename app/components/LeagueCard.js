import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

/**
 * Componente para mostrar una tarjeta de liga
 * @param {Object} props - Props
 * @param {Object} props.liga - Datos de la liga
 * @param {Function} props.onPress - Función a ejecutar al presionar la tarjeta
 */
const LeagueCard = ({ liga, onPress }) => {
  // Asegurarse de que liga sea un objeto válido
  if (!liga) {
    console.warn('Liga data is undefined or null');
    return null;
  }

  // Comprobar si estamos recibiendo la estructura directa o con atributos
  // En Strapi v4+, los datos vienen en un objeto con attributes
  const ligaData = liga.attributes || liga;
  
  // Formatear fecha de inicio y fin
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'short'
    });
  };

  // Fecha de inicio
  const fechaInicio = formatDate(ligaData.fecha_inicio);
  
  // Obtener información de categorías
  const getCategorias = () => {
    const categorias = [];
    if (ligaData.categoria_rx) categorias.push('RX');
    if (ligaData.categoria_intermedio) categorias.push('Intermedio');
    if (ligaData.categoria_scaled) categorias.push('Scaled');
    return categorias.join(', ');
  };

  // Imagen de fondo predeterminada
  const backgroundImage = 
    ligaData.imagen?.url || 
    'https://images.unsplash.com/photo-1599058917765-a780eda07a3e?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80';

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <ImageBackground
        source={{ uri: backgroundImage }}
        style={styles.image}
        imageStyle={styles.imageStyle}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        >
          <View style={styles.content}>
            <Text style={styles.title} numberOfLines={2}>
              {ligaData.nombre || 'Liga sin nombre'}
            </Text>
            <View style={styles.details}>
              {ligaData.duracion_semanas && (
                <View style={styles.detailItem}>
                  <Icon name="event" size={16} color="#e0fe10" />
                  <Text style={styles.detailText}>
                    {ligaData.duracion_semanas} semanas
                  </Text>
                </View>
              )}
              {ligaData.categoria_rx || ligaData.categoria_intermedio || ligaData.categoria_scaled ? (
                <View style={styles.detailItem}>
                  <Icon name="fitness-center" size={16} color="#e0fe10" />
                  <Text style={styles.detailText}>
                    {getCategorias()}
                  </Text>
                </View>
              ) : null}
            </View>
            <View style={styles.footer}>
              {ligaData.fecha_inicio && (
                <Text style={styles.footerText}>Inicio: {fechaInicio}</Text>
              )}
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 180,
    width: 300,
    borderRadius: 15,
    overflow: 'hidden',
    marginRight: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    backgroundColor: '#232323',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageStyle: {
    borderRadius: 15,
  },
  gradient: {
    flex: 1,
    justifyContent: 'flex-end',
    padding: 15,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  title: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  details: {
    flexDirection: 'column',
    marginBottom: 5,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 3,
  },
  detailText: {
    color: '#ffffff',
    fontSize: 14,
    marginLeft: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  footer: {
    marginTop: 5,
  },
  footerText: {
    color: '#e0fe10',
    fontSize: 14,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default LeagueCard;
