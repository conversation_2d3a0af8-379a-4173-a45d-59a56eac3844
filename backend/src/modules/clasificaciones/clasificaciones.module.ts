import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClasificacionesService } from './clasificaciones.service';
import { ClasificacionesController } from './clasificaciones.controller';
import { Clasificacion } from './entities/clasificacion.entity';
import { ResultadosModule } from '../resultados/resultados.module';
import { LigasModule } from '../ligas/ligas.module';
import { WodsModule } from '../wods/wods.module';
import { UsuariosModule } from '../usuarios/usuarios.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Clasificacion]),
    ResultadosModule,
    LigasModule,
    WodsModule,
    UsuariosModule,
  ],
  controllers: [ClasificacionesController],
  providers: [ClasificacionesService],
  exports: [ClasificacionesService],
})
export class ClasificacionesModule {}
