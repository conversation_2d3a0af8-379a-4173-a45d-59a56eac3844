import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// Colores corporativos
const COLORS = {
  primary: '#e0fe10',    // Verde fluorescente
  secondary: '#2e2e2e',  // Gris oscuro
  white: '#ffffff',      // Blanco
  dark: '#232323',       // Negro oscuro
  accent: '#598392'      // Azul acento
};

const LogoutModal = ({ visible, onClose, onConfirm }) => {
  const slideAnim = new Animated.Value(0);
  const { height, width } = Dimensions.get('window');

  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 15,
        friction: 9
      }).start();
    } else {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 15,
        friction: 9
      }).start();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <View style={[styles.container, { width }]}>
      <TouchableOpacity
        style={[styles.backdrop, { width }]}
        activeOpacity={1}
        onPress={onClose}
      />
      <Animated.View
        style={[
          styles.content,
          {
            width,
            transform: [{
              translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [height, 0]
              })
            }]
          }
        ]}
      >
        <LinearGradient
          colors={[COLORS.secondary, COLORS.dark]}
          style={styles.header}
        >
          <Text style={styles.title}>Cerrar Sesión</Text>
        </LinearGradient>

        <View style={styles.body}>
          <Text style={styles.message}>
            ¿Estás seguro que deseas cerrar sesión?
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={onConfirm}
            >
              <LinearGradient
                colors={[COLORS.primary, COLORS.accent]}
                style={styles.gradientButton}
              >
                <Text style={[styles.confirmButtonText, { color: COLORS.dark }]}>Sí, cerrar sesión</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    alignItems: 'center',
    zIndex: 1000,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  content: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  header: {
    padding: 15,
    alignItems: 'center',
    width: '100%',
  },
  title: {
    color: COLORS.primary,
    fontSize: 18,
    fontWeight: 'bold',
  },
  body: {
    padding: 50,
    width: '100%',
  },
  message: {
    fontSize: 16,
    color: COLORS.secondary,
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
    width: '100%',
  },
  button: {
    borderRadius: 25,
    minWidth: 120,
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
  },
  confirmButton: {
    overflow: 'hidden',
  },
  gradientButton: {
    padding: 10,
    alignItems: 'center',
    borderRadius: 25,
  },
  cancelButtonText: {
    color: COLORS.secondary,
    fontWeight: '600',
    textAlign: 'center',
  },
  confirmButtonText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default LogoutModal;
