#!/bin/bash

# ======================================================
# 🚀 The WOD League - App Startup Script
# ======================================================
# This script helps developers to start the mobile app
# environment with proper configurations and checks.
# ======================================================

# Set colors for better visual feedback
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}🏋️ THE WOD LEAGUE - MOBILE APP STARTUP 🏋️${NC}"
echo -e "${BLUE}==================================================${NC}"

# Navigate to app directory
cd "$(dirname "$0")/app" || { 
  echo -e "${RED}❌ Error: Could not navigate to app directory${NC}"; 
  exit 1; 
}

# Check if node modules are installed
if [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}📦 Node modules not found. Installing dependencies...${NC}"
  npm install || { 
    echo -e "${RED}❌ Error installing dependencies${NC}"; 
    exit 1; 
  }
  echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
  echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Check for environment configuration
if [ ! -f "config.js" ]; then
  echo -e "${YELLOW}⚙️ Configuration file not found. Creating sample config...${NC}"
  echo "export default { apiUrl: 'http://localhost:1337' };" > config.js
  echo -e "${GREEN}✅ Sample config.js created${NC}"
  echo -e "${YELLOW}⚠️ Please update config.js with your actual settings${NC}"
else
  echo -e "${GREEN}✅ Configuration file found${NC}"
fi

# Always clean watchman watches for stability
echo -e "${YELLOW}🧹 Resetting Watchman watches...${NC}"
watchman watch-del-all 2>/dev/null || echo -e "${YELLOW}⚠️ Watchman not installed, skipping...${NC}"
echo -e "${GREEN}✅ Watchman reset completed${NC}"

# Clear Metro bundler cache if requested
if [ "$1" == "--clear-cache" ]; then
  echo -e "${YELLOW}🧹 Clearing Metro bundler cache...${NC}"
  rm -rf node_modules/.cache
  echo -e "${GREEN}✅ Cache cleared${NC}"
fi

# Starting the app
echo -e "${GREEN}🚀 Starting the app...${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "${YELLOW}👉 Available commands:${NC}"
echo -e "${YELLOW}   Press 'i' for iOS simulator${NC}"
echo -e "${YELLOW}   Press 'a' for Android simulator${NC}"
echo -e "${YELLOW}   Press 'w' for web${NC}"
echo -e "${BLUE}==================================================${NC}"

# Start Expo development server
npx expo start
