import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const ScoreSubmissionForm = ({ event, onSubmit, onCancel }) => {
  const [rounds, setRounds] = useState('');
  const [reps, setReps] = useState('');
  const [time, setTime] = useState('');
  const [weight, setWeight] = useState('');
  const [distance, setDistance] = useState('');

  const validateTime = (timeStr) => {
    // Validar formato MM:SS
    const timeRegex = /^([0-5]?[0-9]):([0-5][0-9])$/;
    return timeRegex.test(timeStr);
  };

  const handleSubmit = () => {
    const scoring_method = event.data?.attributes?.scoring_method || event.scoring_method;
    let scoreData = {};

    switch (scoring_method) {
      case 'Time':
        if (!validateTime(time)) {
          alert('Please enter a valid time in MM:SS format');
          return;
        }
        scoreData = { time };
        break;

      case 'Rounds + Reps':
        const roundsNum = parseInt(rounds, 10);
        const repsNum = parseInt(reps, 10);
        if (isNaN(roundsNum) || isNaN(repsNum)) {
          alert('Please enter valid numbers for rounds and reps');
          return;
        }
        scoreData = { rounds: roundsNum, reps: repsNum };
        break;

      case 'Total Reps':
        const totalReps = parseInt(reps, 10);
        if (isNaN(totalReps)) {
          alert('Please enter a valid number of reps');
          return;
        }
        scoreData = { reps: totalReps };
        break;

      case 'Weight':
        const weightNum = parseFloat(weight);
        if (isNaN(weightNum)) {
          alert('Please enter a valid weight');
          return;
        }
        scoreData = { weight: weightNum };
        break;

      case 'Distance':
        const distanceNum = parseInt(distance, 10);
        if (isNaN(distanceNum)) {
          alert('Please enter a valid distance');
          return;
        }
        scoreData = { distance: distanceNum };
        break;
    }

    onSubmit(scoreData);
  };

  const renderScoreInput = () => {
    const scoring_method = event.data?.attributes?.scoring_method || event.scoring_method;

    switch (scoring_method) {
      case 'Time':
        return (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Time (MM:SS)</Text>
            <TextInput
              style={styles.input}
              value={time}
              onChangeText={setTime}
              placeholder="Enter time (e.g., 12:34)"
              keyboardType="numbers-and-punctuation"
            />
          </View>
        );

      case 'Rounds + Reps':
        return (
          <>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Rounds Completed</Text>
              <TextInput
                style={styles.input}
                value={rounds}
                onChangeText={setRounds}
                keyboardType="numeric"
                placeholder="Enter number of rounds"
              />
            </View>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Additional Reps</Text>
              <TextInput
                style={styles.input}
                value={reps}
                onChangeText={setReps}
                keyboardType="numeric"
                placeholder="Enter additional reps"
              />
            </View>
          </>
        );

      case 'Total Reps':
        return (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Total Reps</Text>
            <TextInput
              style={styles.input}
              value={reps}
              onChangeText={setReps}
              keyboardType="numeric"
              placeholder="Enter total reps completed"
            />
          </View>
        );

      case 'Weight':
        return (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Weight (kg)</Text>
            <TextInput
              style={styles.input}
              value={weight}
              onChangeText={setWeight}
              keyboardType="decimal-pad"
              placeholder="Enter weight in kg"
            />
          </View>
        );

      case 'Distance':
        return (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Distance (meters)</Text>
            <TextInput
              style={styles.input}
              value={distance}
              onChangeText={setDistance}
              keyboardType="numeric"
              placeholder="Enter distance in meters"
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Submit Score</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onCancel}>
            <Icon name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        <View style={styles.wodInfo}>
          <Text style={styles.wodName}>{event.data?.attributes?.wod_name || event.wod_name}</Text>
          <Text style={styles.wodDescription}>
            {event.data?.attributes?.wod_description || event.wod_description}
          </Text>
        </View>

        <View style={styles.form}>
          {renderScoreInput()}
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <Text style={styles.submitButtonText}>Submit Score</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  wodInfo: {
    marginBottom: 24,
  },
  wodName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  wodDescription: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    gap: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#f8f9fa',
  },
  submitButton: {
    backgroundColor: '#eb8d28',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ScoreSubmissionForm;
