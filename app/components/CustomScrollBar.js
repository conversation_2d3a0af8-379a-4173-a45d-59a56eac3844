import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';

/**
 * Componente de barra de desplazamiento personalizada que permanece visible
 */
const CustomScrollBar = ({ scrollViewHeight, contentHeight, scrollY }) => {
  // Si no hay desplazamiento necesario, no mostramos la barra
  if (scrollViewHeight >= contentHeight || !scrollViewHeight || !contentHeight) {
    return null;
  }

  // Calculamos el tamaño y la posición de la barra
  const scrollBarHeight = (scrollViewHeight / contentHeight) * scrollViewHeight;
  const scrollBarPosition = scrollY.interpolate({
    inputRange: [0, contentHeight - scrollViewHeight],
    outputRange: [0, scrollViewHeight - scrollBarHeight],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.scrollBarContainer}>
      <Animated.View
        style={[
          styles.scrollBar,
          {
            height: scrollBarHeight,
            transform: [{ translateY: scrollBarPosition }],
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  scrollBarContainer: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: 6,
    backgroundColor: 'transparent',
  },
  scrollBar: {
    width: 4,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.4)', // Color gris claro semi-transparente
  },
});

export default CustomScrollBar;
