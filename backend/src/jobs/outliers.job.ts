import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ResultadosService } from '../modules/resultados/resultados.service';

@Injectable()
export class OutliersJob {
  private readonly logger = new Logger(OutliersJob.name);

  constructor(private resultadosService: ResultadosService) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async detectarOutliers() {
    this.logger.log('Ejecutando tarea programada: Detección de outliers');
    
    try {
      const outliers = await this.resultadosService.detectarOutliers();
      this.logger.log(`Se detectaron ${outliers.length} outliers`);
    } catch (error) {
      this.logger.error(`Error al detectar outliers: ${error.message}`);
    }
  }
}
