import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Clasificacion } from './entities/clasificacion.entity';
import { ClasificacionDto } from './dto/clasificacion.dto';
import { ResultadosService } from '../resultados/resultados.service';
import { LigasService } from '../ligas/ligas.service';
import { WodsService } from '../wods/wods.service';
import { UsuariosService } from '../usuarios/usuarios.service';
import { NivelUsuario, GeneroUsuario } from '../usuarios/entities/usuario.entity';
import { TipoWOD } from '../wods/entities/wod.entity';

@Injectable()
export class ClasificacionesService {
  constructor(
    @InjectRepository(Clasificacion)
    private clasificacionesRepository: Repository<Clasificacion>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private resultadosService: ResultadosService,
    private ligasService: LigasService,
    private wodsService: WodsService,
    private usuariosService: UsuariosService,
  ) { }

  async obtenerClasificacion(ligaId: string, categoria: string): Promise<ClasificacionDto[]> {
    // Intentar obtener la clasificación desde Redis
    const cacheKey = `clasificacion:${ligaId}:${categoria}`;
    const cachedData = await this.cacheManager.get<string>(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Si no hay datos en caché, calcular y guardar
    await this.actualizarClasificacion(ligaId, categoria);
    const newCachedData = await this.cacheManager.get<string>(cacheKey);

    if (newCachedData) {
      return JSON.parse(newCachedData);
    }

    return [];
  }

  async actualizarClasificacion(ligaId: string, categoria: string): Promise<void> {
    // Obtener la liga
    const liga = await this.ligasService.findOne(ligaId);

    // Obtener todos los WODs de la liga
    const wods = await this.wodsService.findByLiga(ligaId);

    // Obtener todos los resultados validados para estos WODs
    const resultadosPorWod: Array<{ wod: any, resultados: any[] }> = [];
    for (const wod of wods) {
      const resultados = await this.resultadosService.findByWod(wod.id);
      const resultadosValidados = resultados.filter(r => r.validado);
      resultadosPorWod.push({ wod, resultados: resultadosValidados });
    }

    // Obtener todos los usuarios que han participado
    const usuariosIds = new Set<string>();
    resultadosPorWod.forEach(({ resultados }) => {
      resultados.forEach(r => usuariosIds.add(r.usuarioId));
    });

    // Filtrar usuarios por categoría
    const [nivel, genero] = categoria.split('-');
    const usuariosCategoria: any[] = [];

    for (const usuarioId of usuariosIds) {
      const usuario = await this.usuariosService.findOne(usuarioId);

      if (
        (nivel === 'RX' && usuario.nivel === NivelUsuario.RX) ||
        (nivel === 'Intermedio' && usuario.nivel === NivelUsuario.INTERMEDIO) ||
        (nivel === 'Scaled' && usuario.nivel === NivelUsuario.SCALED)
      ) {
        if (
          (genero === 'Masculino' && usuario.genero === GeneroUsuario.MASCULINO) ||
          (genero === 'Femenino' && usuario.genero === GeneroUsuario.FEMENINO)
        ) {
          usuariosCategoria.push(usuario);
        }
      }
    }

    // Calcular puntuaciones
    const clasificacion: ClasificacionDto[] = [];

    for (const usuario of usuariosCategoria) {
      let puntuacionTotal = 0;
      let wodsCompletados = 0;

      // Calcular puntuación por cada WOD
      for (const { wod, resultados } of resultadosPorWod) {
        const resultado = resultados.find(r => r.usuarioId === usuario.id);

        if (resultado) {
          wodsCompletados++;

          // Calcular puntuación para este resultado
          const mejorResultado = this.obtenerMejorResultado(resultados, wod.tipo);
          const puntuacion = this.calcularPuntuacion(resultado, mejorResultado, wod.tipo);

          puntuacionTotal += puntuacion;
        }
      }

      // Añadir a la clasificación
      clasificacion.push({
        usuarioId: usuario.id,
        nombre: usuario.nombre,
        alias: usuario.alias,
        box: usuario.box?.nombre || 'Sin box',
        puntuacionTotal: puntuacionTotal,
        bonusConsistencia: 0, // Eliminado el cálculo de bonusConsistencia
        wodsCompletados,
        wodsTotales: wods.length,
        posicion: 0, // Se calculará después
        posicionAnterior: 0, // Se obtendrá de la BD
      });
    }

    // Ordenar por puntuación total (de mayor a menor)
    clasificacion.sort((a, b) => b.puntuacionTotal - a.puntuacionTotal);

    // Asignar posiciones
    for (let i = 0; i < clasificacion.length; i++) {
      clasificacion[i].posicion = i + 1;

      // Obtener posición anterior de la BD
      const clasificacionBD = await this.clasificacionesRepository.findOne({
        where: {
          ligaId,
          usuarioId: clasificacion[i].usuarioId,
          categoria,
        },
      });

      if (clasificacionBD) {
        clasificacion[i].posicionAnterior = clasificacionBD.posicion;
      }
    }

    // Guardar en Redis
    const cacheKey = `clasificacion:${ligaId}:${categoria}`;
    await this.cacheManager.set(cacheKey, JSON.stringify(clasificacion), 3600000); // 1 hora

    // Guardar en BD
    for (const item of clasificacion) {
      await this.guardarClasificacionBD(ligaId, categoria, item);
    }
  }

  private obtenerMejorResultado(resultados: any[], tipoWOD: TipoWOD): any {
    if (resultados.length === 0) {
      return null;
    }

    // Ordenar según el tipo de WOD
    if (tipoWOD === TipoWOD.FORTIME) {
      // Menor tiempo es mejor
      return resultados.reduce((prev, current) => (prev.valor < current.valor) ? prev : current);
    } else {
      // Mayor valor es mejor para los demás tipos
      return resultados.reduce((prev, current) => (prev.valor > current.valor) ? prev : current);
    }
  }

  private calcularPuntuacion(resultado: any, mejorResultado: any, tipoWOD: TipoWOD): number {
    if (!mejorResultado) {
      return 0;
    }

    let puntuacion = 0;

    switch (tipoWOD) {
      case TipoWOD.FORTIME:
        // Menor tiempo es mejor
        puntuacion = 100 * (mejorResultado.valor / resultado.valor);
        break;
      case TipoWOD.AMRAP:
      case TipoWOD.MAXREPS:
      case TipoWOD.MAXWEIGHT:
      case TipoWOD.EMOM:
        // Mayor valor es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      default:
        throw new Error(`Tipo de WOD no soportado: ${tipoWOD}`);
    }

    // Redondear a 2 decimales
    return Math.round(puntuacion * 100) / 100;
  }

  // El método calcularBonusConsistencia ha sido eliminado

  private async guardarClasificacionBD(ligaId: string, categoria: string, item: ClasificacionDto): Promise<void> {
    // Buscar si ya existe una clasificación para este usuario y liga
    let clasificacion = await this.clasificacionesRepository.findOne({
      where: {
        ligaId,
        usuarioId: item.usuarioId,
        categoria,
      },
    });

    if (!clasificacion) {
      // Crear nueva clasificación
      clasificacion = this.clasificacionesRepository.create({
        ligaId,
        usuarioId: item.usuarioId,
        categoria,
        puntuacionTotal: item.puntuacionTotal,
        bonusConsistencia: 0, // Valor fijo para mantener compatibilidad
        wodsCompletados: item.wodsCompletados,
        wodsTotales: item.wodsTotales,
        posicion: item.posicion,
        posicionAnterior: 0,
        semana: 1, // TODO: Calcular semana actual
        updatedAt: new Date(),
      });
    } else {
      // Actualizar clasificación existente
      clasificacion.posicionAnterior = clasificacion.posicion;
      clasificacion.posicion = item.posicion;
      clasificacion.puntuacionTotal = item.puntuacionTotal;
      clasificacion.bonusConsistencia = 0; // Valor fijo para mantener compatibilidad
      clasificacion.wodsCompletados = item.wodsCompletados;
      clasificacion.wodsTotales = item.wodsTotales;
      clasificacion.updatedAt = new Date();
    }

    await this.clasificacionesRepository.save(clasificacion);
  }

  async obtenerClasificacionHistorica(ligaId: string, categoria: string, semana?: number): Promise<Clasificacion[]> {
    const query = this.clasificacionesRepository
      .createQueryBuilder('clasificacion')
      .leftJoinAndSelect('clasificacion.usuario', 'usuario')
      .where('clasificacion.ligaId = :ligaId', { ligaId })
      .andWhere('clasificacion.categoria = :categoria', { categoria });

    if (semana) {
      query.andWhere('clasificacion.semana = :semana', { semana });
    }

    query.orderBy('clasificacion.posicion', 'ASC');

    return query.getMany();
  }

  async obtenerClasificacionUsuario(usuarioId: string): Promise<Clasificacion[]> {
    return this.clasificacionesRepository.find({
      where: { usuarioId },
      relations: ['liga'],
      order: { updatedAt: 'DESC' },
    });
  }

  async actualizarTodasLasClasificaciones(): Promise<void> {
    // Obtener todas las ligas activas
    const ligas = await this.ligasService.findActivas();

    for (const liga of ligas) {
      // Actualizar clasificaciones para cada categoría
      if (liga.categoriaRx && liga.generoMasculino) {
        await this.actualizarClasificacion(liga.id, 'RX-Masculino');
      }
      if (liga.categoriaRx && liga.generoFemenino) {
        await this.actualizarClasificacion(liga.id, 'RX-Femenino');
      }
      if (liga.categoriaIntermedio && liga.generoMasculino) {
        await this.actualizarClasificacion(liga.id, 'Intermedio-Masculino');
      }
      if (liga.categoriaIntermedio && liga.generoFemenino) {
        await this.actualizarClasificacion(liga.id, 'Intermedio-Femenino');
      }
      if (liga.categoriaScaled && liga.generoMasculino) {
        await this.actualizarClasificacion(liga.id, 'Scaled-Masculino');
      }
      if (liga.categoriaScaled && liga.generoFemenino) {
        await this.actualizarClasificacion(liga.id, 'Scaled-Femenino');
      }
    }
  }
}
