import { 
  List, 
  Datagrid, 
  TextField, 
  ReferenceField,
  Edit, 
  SimpleForm, 
  Create,
  ReferenceInput,
  AutocompleteInput,
  NumberInput,
  required
} from 'react-admin';

// Lista de Clasificaciones
export const RankingList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="posicion" />
      <ReferenceField source="usuarioId" reference="users" label="Atleta">
        <TextField source="nombre" />
      </ReferenceField>
      <ReferenceField source="ligaId" reference="leagues" label="Liga">
        <TextField source="nombre" />
      </ReferenceField>
      <TextField source="puntuacionTotal" />
    </Datagrid>
  </List>
);

// Componente de edición de clasificaciones
export const RankingEdit = () => (
  <Edit>
    <SimpleForm>
      <TextField source="id" />
      <NumberInput source="posicion" validate={[required()]} />
      <ReferenceInput source="usuarioId" reference="users" label="Atleta">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="ligaId" reference="leagues" label="Liga">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <NumberInput source="puntuacionTotal" validate={[required()]} />
    </SimpleForm>
  </Edit>
);

// Componente de creación de clasificaciones
export const RankingCreate = () => (
  <Create>
    <SimpleForm>
      <NumberInput source="posicion" validate={[required()]} />
      <ReferenceInput source="usuarioId" reference="users" label="Atleta">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="ligaId" reference="leagues" label="Liga">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <NumberInput source="puntuacionTotal" validate={[required()]} />
    </SimpleForm>
  </Create>
);
