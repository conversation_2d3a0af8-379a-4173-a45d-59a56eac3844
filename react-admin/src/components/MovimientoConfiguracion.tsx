import React from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControlLabel,
  Checkbox,
  Button,
  TextField,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import InfoIcon from '@mui/icons-material/Info';
import { MovimientoSeleccionado } from './tipos';

interface MovimientoConfiguracionProps {
  movimientoSeleccionado: MovimientoSeleccionado;
  onChange: (movimiento: MovimientoSeleccionado) => void;
  onAgregar: () => void;
}

const MovimientoConfiguracion: React.FC<MovimientoConfiguracionProps> = ({
  movimientoSeleccionado,
  onChange,
  onAgregar
}) => {
  // Función para actualizar una categoría
  const actualizarCategoria = (categoria: 'rx' | 'int' | 'sc', checked: boolean) => {
    onChange({
      ...movimientoSeleccionado,
      categorias: {
        ...movimientoSeleccionado.categorias,
        [categoria]: checked
      }
    });
  };

  // Función para actualizar un género
  const actualizarGenero = (genero: 'masculino' | 'femenino', checked: boolean) => {
    onChange({
      ...movimientoSeleccionado,
      generos: {
        ...movimientoSeleccionado.generos,
        [genero]: checked
      }
    });
  };

  // Función para actualizar repeticiones
  const actualizarRepeticiones = (categoria: 'rx' | 'int' | 'sc', genero: 'masculino' | 'femenino', valor: string | number) => {
    onChange({
      ...movimientoSeleccionado,
      repeticiones: {
        ...movimientoSeleccionado.repeticiones,
        [categoria]: {
          ...movimientoSeleccionado.repeticiones[categoria],
          [genero]: valor
        }
      }
    });
  };

  // Función para establecer "max" como valor
  const establecerMax = (categoria: 'rx' | 'int' | 'sc', genero: 'masculino' | 'femenino') => {
    actualizarRepeticiones(categoria, genero, 'max');
  };

  // Verificar si se ha seleccionado al menos una categoría y un género
  const estaCompleto = 
    (movimientoSeleccionado.categorias.rx || 
     movimientoSeleccionado.categorias.int || 
     movimientoSeleccionado.categorias.sc) &&
    (movimientoSeleccionado.generos.masculino || 
     movimientoSeleccionado.generos.femenino);

  // Función para renderizar campo de repeticiones
  const renderCampoRepeticiones = (categoria: 'rx' | 'int' | 'sc', genero: 'masculino' | 'femenino', label: string) => {
    const valor = movimientoSeleccionado.repeticiones[categoria]?.[genero] || '';
    const categoriaActiva = movimientoSeleccionado.categorias[categoria];
    const generoActivo = movimientoSeleccionado.generos[genero];

    if (!categoriaActiva || !generoActivo) return null;

    return (
      <Box key={`${categoria}-${genero}`} sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <Typography variant="body2" sx={{ minWidth: 60, fontSize: '0.875rem' }}>
          {label}:
        </Typography>
        <TextField
          size="small"
          value={valor}
          onChange={(e) => {
            const newValue = e.target.value;
            if (newValue === '' || newValue === 'max' || (!isNaN(Number(newValue)) && Number(newValue) >= 0)) {
              actualizarRepeticiones(categoria, genero, newValue === '' ? '' : (newValue === 'max' ? 'max' : Number(newValue)));
            }
          }}
          placeholder="Reps"
          sx={{ width: 70 }}
          inputProps={{ style: { textAlign: 'center', fontSize: '0.875rem' } }}
        />
        <Chip
          label="MAX"
          size="small"
          clickable
          color={valor === 'max' ? 'primary' : 'default'}
          onClick={() => establecerMax(categoria, genero)}
          sx={{ fontSize: '0.75rem', height: 24 }}
        />
        <Typography variant="body2" sx={{ fontSize: '0.75rem', color: 'text.secondary', ml: 1 }}>
          reps
        </Typography>
      </Box>
    );
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
      <Typography variant="subtitle1" gutterBottom sx={{ fontSize: '1rem' }}>
        Configurar: {movimientoSeleccionado.nombre}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        {/* Selección de categorías y géneros */}
        <Box sx={{ flex: '1 1 300px' }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontSize: '0.875rem' }}>
            Categorías:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            {[
              { key: 'rx', label: 'RX' },
              { key: 'int', label: 'INT' },
              { key: 'sc', label: 'SC' }
            ].map(({ key, label }) => (
              <FormControlLabel
                key={key}
                control={
                  <Checkbox
                    size="small"
                    checked={movimientoSeleccionado.categorias[key as 'rx' | 'int' | 'sc']}
                    onChange={(e) => actualizarCategoria(key as 'rx' | 'int' | 'sc', e.target.checked)}
                  />
                }
                label={label}
                sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
              />
            ))}
          </Box>

          <Typography variant="subtitle2" sx={{ mb: 1, fontSize: '0.875rem' }}>
            Géneros:
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {[
              { key: 'masculino', label: 'M' },
              { key: 'femenino', label: 'F' }
            ].map(({ key, label }) => (
              <FormControlLabel
                key={key}
                control={
                  <Checkbox
                    size="small"
                    checked={movimientoSeleccionado.generos[key as 'masculino' | 'femenino']}
                    onChange={(e) => actualizarGenero(key as 'masculino' | 'femenino', e.target.checked)}
                  />
                }
                label={label}
                sx={{ '& .MuiFormControlLabel-label': { fontSize: '0.875rem' } }}
              />
            ))}
          </Box>
        </Box>

        {/* Repeticiones */}
        <Box sx={{ flex: '1 1 300px' }}>
          <Typography variant="subtitle2" sx={{ mb: 1, fontSize: '0.875rem' }}>
            Repeticiones:
          </Typography>
          <Box>
            {renderCampoRepeticiones('rx', 'masculino', 'RX M')}
            {renderCampoRepeticiones('rx', 'femenino', 'RX F')}
            {renderCampoRepeticiones('int', 'masculino', 'INT M')}
            {renderCampoRepeticiones('int', 'femenino', 'INT F')}
            {renderCampoRepeticiones('sc', 'masculino', 'SC M')}
            {renderCampoRepeticiones('sc', 'femenino', 'SC F')}
          </Box>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={onAgregar}
          disabled={!estaCompleto}
          size="small"
        >
          Añadir
        </Button>
      </Box>
    </Paper>
  );
};

export default MovimientoConfiguracion;
