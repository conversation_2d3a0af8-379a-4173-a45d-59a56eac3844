import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { OwnershipGuard } from './ownership.guard';
import { RolUsuario } from '../../usuarios/entities/usuario.entity';

/**
 * Tests para el OwnershipGuard
 * Verifica que el guard permita o deniegue acceso basado en propiedad del recurso
 */
describe('OwnershipGuard', () => {
  let guard: OwnershipGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OwnershipGuard],
    }).compile();

    guard = module.get<OwnershipGuard>(OwnershipGuard);
  });

  it('debe estar definido', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('debe permitir acceso cuando el usuario es admin', () => {
      // Arrange
      const mockContext = createMockExecutionContext(
        '1', 
        RolUsuario.ADMIN, 
        { id: '2' }  // ID del recurso diferente al ID del usuario
      );

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
    });

    it('debe permitir acceso cuando el usuario es dueño del recurso', () => {
      // Arrange
      const userId = '1';
      const mockContext = createMockExecutionContext(
        userId, 
        RolUsuario.USUARIO, 
        { id: userId }  // El ID del recurso coincide con el usuario
      );

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
    });

    it('debe denegar acceso cuando el usuario no es dueño del recurso ni admin', () => {
      // Arrange
      const mockContext = createMockExecutionContext(
        '1', 
        RolUsuario.USUARIO, 
        { id: '2' }  // ID del recurso diferente al ID del usuario
      );

      // Act & Assert
      expect(() => guard.canActivate(mockContext as any)).toThrow(UnauthorizedException);
    });

    it('debe usar el parámetro id cuando está disponible', () => {
      // Arrange
      const mockContext = createMockExecutionContext(
        '1', 
        RolUsuario.USUARIO, 
        null,  // Sin cuerpo de solicitud
        { id: '1' }  // ID en los parámetros de ruta
      );

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
    });

    it('debe denegar acceso cuando no hay id en el cuerpo ni en los parámetros', () => {
      // Arrange
      const mockContext = createMockExecutionContext(
        '1', 
        RolUsuario.USUARIO, 
        null,  // Sin cuerpo de solicitud
        null   // Sin parámetros
      );

      // Act & Assert
      expect(() => guard.canActivate(mockContext as any)).toThrow(UnauthorizedException);
    });
  });
});

/**
 * Crea un contexto de ejecución simulado para los tests
 */
function createMockExecutionContext(
  userId: string, 
  role: RolUsuario, 
  body: any = null,
  params: any = null
) {
  return {
    switchToHttp: () => ({
      getRequest: () => ({
        user: { 
          sub: userId,
          rol: role 
        },
        body: body,
        params: params
      })
    }),
    getHandler: () => ({}),
    getClass: () => ({})
  };
}
