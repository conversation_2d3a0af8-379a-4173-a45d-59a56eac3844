# Guía de Inicio Rápido: Backend de The WOD League

Esta guía proporciona instrucciones paso a paso para configurar y ejecutar el backend de The WOD League.

## Requisitos Previos

Antes de comenzar, asegúrate de tener instalado:

- **Node.js** (v16 o superior)
- **npm** (v7 o superior)
- **PostgreSQL** (v13 o superior)
- **Redis** (v6 o superior)

## Configuración Inicial

### 1. Clonar el Repositorio

```bash
git clone <url-del-repositorio>
cd <directorio-del-repositorio>/backend
```

### 2. Instalar Dependencias

```bash
npm install
```

### 3. Configurar Variables de Entorno

Crea un archivo `.env` en la raíz del directorio `backend` con la siguiente configuración:

```env
# Configuración de la aplicación
NODE_ENV=development
PORT=3000
API_PREFIX=api

# Configuración de la base de datos
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=tu_contraseña
DB_DATABASE=wodleague
DB_SYNCHRONIZE=true
DB_LOGGING=false

# Configuración de Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_TTL=3600

# Configuración de JWT
JWT_SECRET=tu_secreto_jwt
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=tu_secreto_refresh_jwt
JWT_REFRESH_EXPIRES_IN=7d

# Configuración de AWS S3 (opcional)
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET_NAME=wodleague
AWS_S3_PUBLIC_URL=
```

Asegúrate de reemplazar los valores de ejemplo con tus propias credenciales.

### 4. Crear la Base de Datos

Crea una base de datos PostgreSQL con el nombre especificado en la variable `DB_DATABASE`:

```bash
psql -U postgres
```

```sql
CREATE DATABASE wodleague;
```

## Iniciar el Servidor

### Modo Desarrollo

Para iniciar el servidor en modo desarrollo con recarga automática:

```bash
npm run start:dev
```

### Modo Producción

Para compilar y ejecutar el servidor en modo producción:

```bash
npm run build
npm run start:prod
```

## Verificar la Instalación

Una vez que el servidor esté en funcionamiento, puedes verificar que todo está configurado correctamente accediendo a:

```
http://localhost:3000/api
```

Deberías ver un mensaje "Hello World!" o la documentación de la API si Swagger está configurado.

## Ejecutar Tests

### Tests Unitarios

Para ejecutar todos los tests unitarios:

```bash
npm test
```

Para ejecutar un test específico:

```bash
npm test -- src/test/usuario.service.spec.ts
```

## Estructura del Proyecto

El backend está organizado en módulos:

- **Usuarios**: Gestión de usuarios y perfiles
- **Auth**: Autenticación y autorización
- **Boxes**: Gestión de boxes de CrossFit
- **Ligas**: Gestión de ligas y competiciones
- **WODs**: Gestión de WODs (Workout of the Day)
- **Resultados**: Registro y validación de resultados
- **Clasificaciones**: Cálculo de clasificaciones en tiempo real
- **Inscripciones**: Gestión de inscripciones a ligas

## Endpoints Principales

### Autenticación

- `POST /api/auth/register`: Registro de usuario
- `POST /api/auth/login`: Inicio de sesión

### Usuarios

- `GET /api/usuarios`: Obtener todos los usuarios (admin)
- `GET /api/usuarios/:id`: Obtener un usuario por ID
- `PATCH /api/usuarios/:id`: Actualizar un usuario
- `DELETE /api/usuarios/:id`: Eliminar un usuario (admin)

### Ligas

- `GET /api/ligas`: Obtener todas las ligas
- `GET /api/ligas/activas`: Obtener ligas activas
- `GET /api/ligas/:id`: Obtener una liga por ID
- `POST /api/ligas`: Crear una nueva liga (admin)
- `PATCH /api/ligas/:id`: Actualizar una liga (admin)
- `DELETE /api/ligas/:id`: Eliminar una liga (admin)

### WODs

- `GET /api/wods`: Obtener todos los WODs
- `GET /api/wods/liga/:ligaId`: Obtener WODs de una liga
- `GET /api/wods/:id`: Obtener un WOD por ID
- `POST /api/wods`: Crear un nuevo WOD (admin)
- `PATCH /api/wods/:id`: Actualizar un WOD (admin)
- `DELETE /api/wods/:id`: Eliminar un WOD (admin)

### Resultados

- `POST /api/resultados`: Registrar un resultado
- `GET /api/resultados/usuario/:usuarioId`: Obtener resultados de un usuario
- `GET /api/resultados/wod/:wodId`: Obtener resultados de un WOD
- `POST /api/resultados/:id/validar`: Validar un resultado (admin/box owner)

### Clasificaciones

- `GET /api/clasificaciones/liga/:ligaId/categoria/:categoria`: Obtener clasificación
- `GET /api/clasificaciones/usuario/:usuarioId`: Obtener clasificaciones de un usuario

### Inscripciones

- `POST /api/inscripciones`: Crear una inscripción
- `GET /api/inscripciones/usuario/:usuarioId`: Obtener inscripciones de un usuario
- `POST /api/inscripciones/:id/confirmar-pago`: Confirmar pago (admin)

## WebSockets

El backend utiliza WebSockets para proporcionar actualizaciones en tiempo real de las clasificaciones. Los clientes pueden suscribirse a:

```javascript
// Ejemplo de cliente
const socket = io('http://localhost:3000/clasificaciones');
socket.emit('suscribirClasificacion', { ligaId: 'id-liga', categoria: 'RX-Masculino' });
socket.on('clasificacionActualizada', (data) => {
  console.log('Clasificación actualizada:', data);
});
```

## Solución de Problemas

### Error de Conexión a la Base de Datos

Si encuentras errores de conexión a la base de datos:

1. Verifica que PostgreSQL esté en ejecución
2. Comprueba las credenciales en el archivo `.env`
3. Asegúrate de que la base de datos existe

### Error de Conexión a Redis

Si encuentras errores de conexión a Redis:

1. Verifica que Redis esté en ejecución
2. Comprueba la configuración en el archivo `.env`

### Otros Problemas

Para otros problemas, consulta los logs del servidor o ejecuta con el modo de depuración:

```bash
npm run start:debug
```

## Recursos Adicionales

- [Documentación de NestJS](https://docs.nestjs.com/)
- [Documentación de TypeORM](https://typeorm.io/)
- [Documentación de Redis](https://redis.io/documentation)
