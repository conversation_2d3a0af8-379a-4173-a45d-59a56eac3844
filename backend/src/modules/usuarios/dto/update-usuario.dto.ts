import { PartialType } from '@nestjs/mapped-types';
import { CreateUsuarioDto } from './create-usuario.dto';
import { IsEnum, IsOptional, IsString, IsBoolean, IsDate, IsInt, Min, Max } from 'class-validator';
import { EstadoUsuario, GeneroUsuario, NivelUsuario } from '../entities/usuario.entity';

export class UpdateUsuarioDto extends PartialType(CreateUsuarioDto) {
  /**
   * Género del usuario
   * @example 'Masculino'
   */
  @IsOptional()
  @IsEnum(GeneroUsuario)
  genero?: GeneroUsuario;

  /**
   * Nivel del usuario
   * @example 'Intermedio'
   */
  @IsOptional()
  @IsEnum(NivelUsuario)
  nivel?: NivelUsuario;

  /**
   * Edad del usuario
   * @example 28
   */
  @IsOptional()
  @IsInt()
  @Min(16)
  @Max(99)
  edad?: number;
  @IsOptional()
  @IsEnum(EstadoUsuario)
  estado?: EstadoUsuario;

  @IsOptional()
  @IsBoolean()
  emailVerificado?: boolean;

  @IsOptional()
  @IsString()
  tokenVerificacion?: string;

  @IsOptional()
  tokenVerificacionExpira?: Date;
  
  /**
   * Indica si el usuario ha completado el proceso de configuración inicial
   * @example true
   */
  @IsOptional()
  @IsBoolean()
  setupCompleted?: boolean;
  
  /**
   * Indica si el usuario ha completado el proceso de onboarding
   * @example true
   */
  @IsOptional()
  @IsBoolean()
  onBoarding?: boolean;

  /**
   * Token para restablecer la contraseña
   * @example 'abc123def456'
   */
  @IsOptional()
  @IsString()
  tokenResetPassword?: string;

  /**
   * Fecha de expiración del token de restablecimiento de contraseña
   * @example '2023-01-01T12:00:00Z'
   */
  @IsOptional()
  tokenResetPasswordExpira?: Date;
}
