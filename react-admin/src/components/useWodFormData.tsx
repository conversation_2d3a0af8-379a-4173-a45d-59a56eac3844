import { MovimientoWod } from './tipos';

interface WodFormData {
  titulo?: string;
  descripcion?: string;
  tipo?: string;
  movimientos?: MovimientoWod[];
  semana?: number;
  esBonus?: boolean;
}

/**
 * Hook personalizado para obtener los datos del formulario de WOD en tiempo real
 * Versión simplificada que recibe los datos como props
 */
export const useWodFormData = (props?: Partial<WodFormData>): WodFormData => {
  return {
    titulo: props?.titulo || '',
    descripcion: props?.descripcion || '',
    tipo: props?.tipo || '',
    movimientos: props?.movimientos || [],
    semana: props?.semana || 0,
    esBonus: props?.esBonus || false
  };
};

export default useWodFormData;
