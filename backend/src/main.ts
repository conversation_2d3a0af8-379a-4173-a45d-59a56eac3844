import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module.js';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { json, urlencoded, static as expressStatic } from 'express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Aumentar el límite de tamaño de las solicitudes a 10MB
  app.use(json({ limit: '10mb' }));
  app.use(urlencoded({ limit: '10mb', extended: true }));

  // Configuración global de validación
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Configuración de CORS
  app.enableCors();

  // Servir archivos estáticos
  // Configurar la carpeta uploads para servir archivos estáticos
  // Usamos una alternativa compatible con Node.js para la ruta de uploads
  // que funciona tanto en commonjs como en esm
  const uploadsPath = join(process.cwd(), 'uploads');
  console.log('Serving static files from:', uploadsPath);
  app.use('/uploads', expressStatic(uploadsPath));

  // Prefijo global para la API
  const apiPrefix = configService.get('app.apiPrefix');
  app.setGlobalPrefix(apiPrefix, {
    exclude: ['/uploads'], // Excluir la ruta de archivos estáticos del prefijo de la API
  });

  // Puerto de la aplicación
  const port = configService.get('app.port');
  await app.listen(port);

  console.log(`Application is running on: http://localhost:${port}/${apiPrefix}`);
  console.log(`Static files are served at: http://localhost:${port}/uploads`);
}
bootstrap();
