import React from 'react';
import { View, Text, StyleSheet, useWindowDimensions } from 'react-native';
import Colors from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

/**
 * Componente para mostrar un item individual del onboarding
 * @param {Object} item - Datos del item de onboarding (título, descripción, imagen)
 */
const OnboardingItem = ({ item }) => {
  const { width, height } = useWindowDimensions();

  const renderIcon = () => {
    if (item.id === '2' || item.id === '4') {
      return (
        <View style={styles.iconContainer}>
          <Ionicons name="fitness-outline" size={32} color={Colors.PRIMARY} />
        </View>
      );
    } else if (item.id === '3') {
      return (
        <View style={styles.iconContainer}>
          <Ionicons name="people-outline" size={32} color={Colors.PRIMARY} />
        </View>
      );
    }
    return null;
  };

  // Renderizamos el contenido de cada slide 
  const renderContent = () => {
    if (item.id === '1') {
      // Primera pantalla (diseño especial con logo grande)
      return (
        <View style={styles.firstSlideContent}>
          <Text style={styles.welcomeText}>{item.titleTopText}</Text>
          <Text style={styles.titleFirstSlide}>{item.title}</Text>
          <Text style={styles.description}>{item.description}</Text>
        </View>
      );
    } else {
      // Resto de pantallas con iconos y contenido centrado
      return (
        <View style={styles.content}>
          {renderIcon()}
          <Text style={styles.title}>{item.title}</Text>
          <Text style={styles.description}>{item.description}</Text>
        </View>
      );
    }
  };

  // Determinamos el gradiente a usar según la pantalla
  let gradientColors;
  switch(item.id) {
    case '1':
      gradientColors = ['#232323', '#1a1a1a'];
      break;
    case '2':
      gradientColors = ['#232323', '#1c1c1c'];
      break;
    case '3':
      gradientColors = ['#232323', '#1e1e1e'];
      break;
    case '4':
      gradientColors = ['#232323', '#202020'];
      break;
    default:
      gradientColors = ['#232323', '#1a1a1a'];
  }

  // Para simular mejor el diseño de la imagen compartida, usamos un fondo negro con gradiente
  return (
    <View style={[styles.container, { width, height }]}>
      <LinearGradient
        colors={gradientColors}
        style={styles.gradientBackground}
      />
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    position: 'relative',
  },
  gradientBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  firstSlideContent: {
    paddingHorizontal: 30,
    paddingBottom: 200,
    alignItems: 'flex-start',
  },
  content: {
    paddingHorizontal: 30,
    paddingBottom: 200,
    alignItems: 'center',
  },
  welcomeText: {
    color: Colors.PRIMARY,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  titleFirstSlide: {
    color: Colors.WHITE,
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 20,
    fontFamily: 'System',
  },
  title: {
    color: Colors.WHITE,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    color: Colors.WHITE,
    lineHeight: 24,
  },
  iconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
});

export default OnboardingItem;
