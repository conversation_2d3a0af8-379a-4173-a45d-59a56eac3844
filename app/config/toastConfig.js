import React from 'react';
import { StyleSheet } from 'react-native';
import { BaseToast, ErrorToast } from 'react-native-toast-message';

export const toastConfig = {
  success: (props) => (
    <BaseToast
      {...props}
      style={[styles.toast, { borderLeftColor: '#69C779' }]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text1NumberOfLines={2}
      text2Style={styles.text2}
      text2NumberOfLines={0} // 0 significa sin límite de líneas
    />
  ),
  error: (props) => (
    <ErrorToast
      {...props}
      style={[styles.toast, { borderLeftColor: '#FE6301' }]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text1NumberOfLines={2}
      text2Style={styles.text2}
      text2NumberOfLines={0} // 0 significa sin límite de líneas
    />
  ),
  info: (props) => (
    <BaseToast
      {...props}
      style={[styles.toast, { borderLeftColor: '#1E88E5' }]}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text1NumberOfLines={2}
      text2Style={styles.text2}
      text2NumberOfLines={0} // 0 significa sin límite de líneas
    />
  ),
};

const styles = StyleSheet.create({
  toast: {
    height: 'auto', // Altura automática basada en el contenido
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderLeftWidth: 5,
  },
  contentContainer: {
    paddingHorizontal: 15,
  },
  text1: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  text2: {
    fontSize: 14,
    color: '#333333',
  },
});
