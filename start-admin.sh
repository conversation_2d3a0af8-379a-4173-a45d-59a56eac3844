#!/bin/bash

# Set colors for better visual feedback
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==================================================${NC}"
echo -e "${GREEN}🖥️  REACT ADMIN FRONTEND - THE WOD LEAGUE 🖥️${NC}"
echo -e "${BLUE}==================================================${NC}"

# URL del backend
BACKEND_URL="http://localhost:3000/api"

echo -e "${YELLOW}🔍 Comprobando si el backend está arrancado...${NC}"
# Comprobar si el backend está arrancado
if curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL" | grep -q "\(200\|404\|401\)"; then
  echo -e "${GREEN}✅ Backend detectado en $BACKEND_URL${NC}"
else
  echo -e "${RED}❌ El backend no parece estar disponible en $BACKEND_URL${NC}"
  echo -e "${YELLOW}⚠️  Es posible que necesites arrancar primero el backend con start-backend.sh${NC}"
  
  read -p "¿Quieres continuar de todos modos? (s/n): " respuesta
  if [[ "$respuesta" != "s" && "$respuesta" != "S" ]]; then
    echo -e "${YELLOW}👋 Saliendo. Primero arranca el backend con start-backend.sh${NC}"
    exit 0
  fi
  
  echo -e "${YELLOW}⚠️ Continuando sin backend. Algunas funciones pueden no estar disponibles.${NC}"
fi

# Iniciar la aplicación React Admin
echo -e "${GREEN}🚀 Iniciando React Admin...${NC}"
echo -e "${BLUE}==================================================${NC}"
cd react-admin && npm start
