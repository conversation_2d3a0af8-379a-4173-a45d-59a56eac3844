// Configurar variables de entorno para testing
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.API_PREFIX = 'api';

// Base de datos - Usar SQLite en memoria para tests
process.env.DB_TYPE = 'sqlite';
process.env.DB_DATABASE = ':memory:';
process.env.DB_SYNCHRONIZE = 'true';
process.env.DB_LOGGING = 'false';

// Redis
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.REDIS_PASSWORD = '';
process.env.REDIS_TTL = '3600';

// JWT
process.env.JWT_SECRET = 'wodleague-test-secret-key';
process.env.JWT_EXPIRES_IN = '1d';
process.env.JWT_REFRESH_SECRET = 'wodleague-test-refresh-secret-key';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
