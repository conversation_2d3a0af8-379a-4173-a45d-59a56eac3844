import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import axios from 'axios';
import { STRAPI_URL } from '../config';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BattlesScreen = ({ navigation }) => {
  const [availableWods, setAvailableWods] = useState([]);
  const [activeBattles, setActiveBattles] = useState([]);

  useEffect(() => {
    fetchWods();
    fetchActiveBattles();
  }, []);

  const fetchWods = async () => {
    try {
      const response = await axios.get(`${STRAPI_URL}/api/wods?filters[isActive][$eq]=true`);
      setAvailableWods(response.data.data);
    } catch (error) {
      console.error('Error fetching WODs:', error);
    }
  };

  const fetchActiveBattles = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      const response = await axios.get(
        `${STRAPI_URL}/api/battles?filters[participants][id][$eq]=${userId}&filters[status][$eq]=active`
      );
      setActiveBattles(response.data.data);
    } catch (error) {
      console.error('Error fetching battles:', error);
    }
  };

  const renderWodItem = ({ item }) => (
    <TouchableOpacity
      style={styles.wodCard}
      onPress={() => navigation.navigate('CreateBattle', { wod: item })}
    >
      <LinearGradient
        colors={['#4a90e2', '#357abd']}
        style={styles.gradientCard}
      >
        <Text style={styles.wodTitle}>{item.attributes.title}</Text>
        <Text style={styles.wodDescription}>{item.attributes.description}</Text>
        <View style={styles.wodDetails}>
          <Text style={styles.wodInfo}>Nivel: {item.attributes.level}</Text>
          <Text style={styles.wodInfo}>Duración: {item.attributes.duration} min</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderBattleItem = ({ item }) => (
    <TouchableOpacity
      style={styles.battleCard}
      onPress={() => navigation.navigate('BattleDetails', { battle: item })}
    >
      <LinearGradient
        colors={['#f7b733', '#fc4a1a']}
        style={styles.gradientCard}
      >
        <Text style={styles.battleTitle}>Batalla vs {item.attributes.opponentName}</Text>
        <Text style={styles.battleWod}>{item.attributes.wodTitle}</Text>
        <Text style={styles.battleDeadline}>
          Termina en: {item.attributes.deadline}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Batallas Activas</Text>
      <FlatList
        data={activeBattles}
        renderItem={renderBattleItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.battlesList}
      />

      <Text style={styles.sectionTitle}>WODs Disponibles</Text>
      <FlatList
        data={availableWods}
        renderItem={renderWodItem}
        keyExtractor={(item) => item.id}
        style={styles.wodsList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginVertical: 12,
    color: '#333',
  },
  wodCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  battleCard: {
    width: 280,
    marginRight: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gradientCard: {
    padding: 16,
    borderRadius: 12,
  },
  wodTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  wodDescription: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 12,
  },
  wodDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wodInfo: {
    color: '#fff',
    fontSize: 12,
  },
  battleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  battleWod: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 8,
  },
  battleDeadline: {
    fontSize: 12,
    color: '#fff',
  },
  battlesList: {
    marginBottom: 16,
  },
  wodsList: {
    flex: 1,
  },
});

export default BattlesScreen;
