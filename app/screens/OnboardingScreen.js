import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Animated,
  TouchableOpacity,
  useWindowDimensions,
  ImageBackground
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import OnboardingItem from '../components/OnboardingItem';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';

/**
 * OnboardingScreen component
 * Displays a series of onboarding screens to new users
 * and allows them to navigate through the content
 */
const OnboardingScreen = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;
  const slidesRef = useRef(null);
  const navigation = useNavigation();
  const { width } = useWindowDimensions();

  // Onboarding data based on the image design
  const slides = [
    {
      id: '1',
      title: 'THEWODLEAGUE',
      titleTopText: 'Bienvenido a',
      description: 'Descubre tu potencial, entrena con constancia y vive la experiencia de una comunidad que te impulsa a mejorar.',
      // TODO: Añadir imagen cuando esté disponible
      // backgroundImage: require('../assets/images/onboarding/slide1.jpg'),
      hideNextButton: false,
      showSkipButton: true,
      actionButtonText: 'Siguiente',
    },
    {
      id: '2',
      title: 'Desafíate',
      description: 'Completa WODs Semanales, Desarrolla Tu Fuerza Y Resistencia, Y Alcanza Nuevas Metas En Cada Entrenamiento.',
      // TODO: Añadir imagen cuando esté disponible
      // backgroundImage: require('../assets/images/onboarding/slide2.jpg'),
      hideNextButton: false,
      showSkipButton: true,
      actionButtonText: 'Siguiente',
      icon: 'fitness',
    },
    {
      id: '3',
      title: 'Compite Y Reta A Tus Amigos',
      description: 'Participa En Ligas Y Desafíos, Mide Tu Progreso Con Cada WOD Y Anima A Tus Compañeros A Unirse A La Competencia.',
      // TODO: Añadir imagen cuando esté disponible
      // backgroundImage: require('../assets/images/onboarding/slide3.jpg'),
      hideNextButton: false,
      showSkipButton: true,
      actionButtonText: 'Siguiente',
      icon: 'people',
    },
    {
      id: '4',
      title: 'Únete A Nuestra Comunidad',
      description: 'Conecta Con Otros Atletas, Comparte Tus Logros Y Vive La Emoción De Entrenar Juntos, Estés Donde Estés.',
      // TODO: Añadir imagen cuando esté disponible
      // backgroundImage: require('../assets/images/onboarding/slide4.jpg'),
      hideNextButton: true,
      showSkipButton: false,
      actionButtonText: 'Empezar',
      icon: 'fitness',
    },
  ];

  const viewableItemsChanged = useRef(({ viewableItems }) => {
    setCurrentIndex(viewableItems[0]?.index || 0);
  }).current;

  const viewConfig = useRef({ viewAreaCoveragePercentThreshold: 50 }).current;

  // Function to handle "Skip" button press
  const handleSkip = async () => {
    await AsyncStorage.setItem('onboardingCompleted', 'true');
    navigation.replace('Auth');
  };

  // Function to handle "Next" button press
  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      slidesRef.current.scrollToIndex({ index: currentIndex + 1 });
    } else {
      handleSkip(); // If on last slide, finish onboarding
    }
  };

  // Pagination dots
  const Paginator = () => {
    return (
      <View style={styles.paginationContainer}>
        {slides.map((_, index) => {
          const inputRange = [
            (index - 1) * width,
            index * width,
            (index + 1) * width,
          ];

          const dotWidth = scrollX.interpolate({
            inputRange,
            outputRange: [10, 20, 10],
            extrapolate: 'clamp',
          });

          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.3, 1, 0.3],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              style={[
                styles.dot,
                {
                  width: dotWidth,
                  opacity,
                  backgroundColor: index === currentIndex ? Colors.PRIMARY : Colors.WHITE,
                }
              ]}
              key={index}
            />
          );
        })}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.topActions}>
        {slides[currentIndex].showSkipButton && (
          <TouchableOpacity onPress={handleSkip} style={styles.skipButtonContainer}>
            <Text style={styles.skipButton}>Skip</Text>
            <Ionicons name="chevron-forward" size={16} color={Colors.PRIMARY} />
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={slides}
        renderItem={({ item }) => <OnboardingItem item={item} />}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        bounces={false}
        keyExtractor={(item) => item.id}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        onViewableItemsChanged={viewableItemsChanged}
        viewabilityConfig={viewConfig}
        scrollEventThrottle={32}
        ref={slidesRef}
      />

      <View style={styles.bottomContainer}>
        <Paginator />

        {!slides[currentIndex].hideNextButton && (
          <TouchableOpacity
            style={styles.button}
            onPress={handleNext}
          >
            <Text style={styles.buttonText}>
              {slides[currentIndex].actionButtonText}
            </Text>
          </TouchableOpacity>
        )}

        {slides[currentIndex].hideNextButton && (
          <TouchableOpacity
            style={[styles.button, styles.startButton]}
            onPress={handleSkip}
          >
            <Text style={styles.startButtonText}>
              {slides[currentIndex].actionButtonText}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.DARK,
  },
  topActions: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  skipButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  skipButton: {
    fontSize: 16,
    color: Colors.PRIMARY,
    fontWeight: '600',
    marginRight: 5,
  },
  paginationContainer: {
    flexDirection: 'row',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.WHITE,
    marginHorizontal: 8,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  button: {
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
    marginTop: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    textAlign: 'center',
  },
  startButton: {
    backgroundColor: Colors.PRIMARY,
    borderWidth: 0,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.DARK,
    textAlign: 'center',
  },
});

export default OnboardingScreen;
