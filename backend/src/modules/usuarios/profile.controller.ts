import { Controller, Get, Body, Patch, Post, Delete, UseGuards, Request, BadRequestException, UseInterceptors, UploadedFile } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UsuariosService } from './usuarios.service';
import { UpdateUsuarioDto } from './dto/update-usuario.dto';
import { ProfilePhotoDto } from './dto/profile-photo.dto';
import { Usuario } from './entities/usuario.entity';


/**
 * Controlador específico para gestionar operaciones del perfil de usuario
 * Proporciona endpoints para visualizar y actualizar el perfil del usuario autenticado
 */
@Controller('perfil')
export class ProfileController {
  constructor(private readonly usuariosService: UsuariosService) {}

  /**
   * Obtiene el perfil del usuario autenticado
   * @param req Objeto de solicitud con la información del usuario autenticado
   * @returns Datos del perfil del usuario
   */
  @UseGuards(JwtAuthGuard)
  @Get()
  async getProfile(@Request() req: any): Promise<Usuario> {
    const userId = req.user.sub;
    console.log('Obteniendo perfil para usuario:', userId);
    return this.usuariosService.findOne(userId);
  }

  /**
   * Actualiza el perfil del usuario autenticado
   * @param req Objeto de solicitud con la información del usuario autenticado
   * @param updateUsuarioDto Datos para actualizar el perfil
   * @returns Perfil actualizado
   */
  @UseGuards(JwtAuthGuard)
  @Patch()
  async updateProfile(
    @Request() req: any,
    @Body() updateUsuarioDto: UpdateUsuarioDto
  ): Promise<Usuario> {
    const userId = req.user.sub;
    console.log('Actualizando perfil para usuario:', userId);
    console.log('Datos de actualización:', updateUsuarioDto);
    return this.usuariosService.update(userId, updateUsuarioDto);
  }

  /**
   * Actualiza la foto de perfil del usuario autenticado
   * @param req Objeto de solicitud con la información del usuario autenticado
   * @param file Archivo de imagen subido
   * @param profilePhotoDto Metadatos de la foto de perfil
   * @returns Perfil actualizado con la nueva foto
   */
  @UseGuards(JwtAuthGuard)
  @Post('photo')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        // Set the destination where the file will be saved
        destination: './uploads/profiles',
        // Generate filename based only on userId to allow overwriting previous images
        filename: (req: any, file, callback) => {
          // Verificamos que req.user exista antes de acceder a sub
          const userId = req.user && req.user.sub ? req.user.sub : 'unknown';
          const extension = extname(file.originalname);
          // Usar solo el ID del usuario para permitir sobrescritura
          callback(null, `profile-${userId}${extension}`);
        },
      }),
      // Limit file types
      fileFilter: (_req, file, callback) => {
        // Only accept images
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return callback(
            new BadRequestException('Only image files are allowed!'),
            false
          );
        }
        callback(null, true);
      },
      // Limit file size (5MB)
      limits: {
        fileSize: 5 * 1024 * 1024,
      },
    })
  )
  async updateProfilePhoto(
    @Request() req: any,
    @UploadedFile() file: Express.Multer.File,
    @Body() _profilePhotoDto: ProfilePhotoDto
  ): Promise<any> {
    const userId = req.user.sub;
    console.log('Actualizando foto de perfil para usuario:', userId);
    console.log('Archivo recibido:', file);

    if (!file) {
      throw new BadRequestException('No se ha proporcionado ninguna imagen');
    }

    // Use our specialized service method for updating profile photos
    const updatedUser = await this.usuariosService.updateProfilePhoto(userId, {
      filename: file.filename,
      path: file.path,
      mimetype: file.mimetype,
      size: file.size
    });

    // Devolver un objeto con la URL de la foto para que el frontend pueda usarla directamente
    console.log('Usuario actualizado con foto:', updatedUser);
    console.log('URL de foto devuelta al cliente:', updatedUser.foto);

    return {
      ...updatedUser,
      photoUrl: updatedUser.foto, // Añadir campo photoUrl para compatibilidad con el frontend
      message: 'Foto de perfil actualizada correctamente',
      // Incluir información adicional para depuración
      debug: {
        photoPath: updatedUser.foto,
        fullPhotoUrl: updatedUser.foto // La URL completa se construye en el cliente
      }
    };
  }

  /**
   * Elimina la cuenta del usuario autenticado
   * @param req Objeto de solicitud con la información del usuario autenticado
   * @returns Void
   */
  @UseGuards(JwtAuthGuard)
  @Delete('me')
  async deleteOwnAccount(@Request() req: any): Promise<any> {
    const userId = req.user.sub;
    console.log('Eliminando cuenta para usuario:', userId);
    
    // Utilizamos el mismo método de servicio que usa el admin
    await this.usuariosService.remove(userId);
    
    return {
      success: true,
      message: 'Cuenta eliminada correctamente'
    };
  }
}
