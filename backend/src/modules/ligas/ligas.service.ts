import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Liga, EstadoLiga } from './entities/liga.entity';
import { CreateLigaDto } from './dto/create-liga.dto';
import { UpdateLigaDto } from './dto/update-liga.dto';

@Injectable()
export class LigasService {
  constructor(
    @InjectRepository(Liga)
    private ligasRepository: Repository<Liga>,
  ) {}

  async create(createLigaDto: CreateLigaDto): Promise<Liga> {
    // Verificar si ya existe una liga con el mismo nombre
    const existingLiga = await this.ligasRepository.findOne({
      where: { nombre: createLigaDto.nombre },
    });

    if (existingLiga) {
      throw new ConflictException('Ya existe una liga con ese nombre');
    }

    // Verificar que la fecha de fin sea posterior a la fecha de inicio
    if (createLigaDto.fechaFin <= createLigaDto.fechaInicio) {
      throw new ConflictException('La fecha de fin debe ser posterior a la fecha de inicio');
    }

    // Crear la nueva liga
    const newLiga = this.ligasRepository.create(createLigaDto);
    return this.ligasRepository.save(newLiga);
  }

  async findAll(): Promise<Liga[]> {
    return this.ligasRepository.find();
  }

  async findOne(id: string): Promise<Liga> {
    const liga = await this.ligasRepository.findOne({
      where: { id },
    });

    if (!liga) {
      throw new NotFoundException(`Liga con ID ${id} no encontrada`);
    }

    return liga;
  }

  async update(id: string, updateLigaDto: UpdateLigaDto): Promise<Liga> {
    const liga = await this.findOne(id);

    // Verificar que la fecha de fin sea posterior a la fecha de inicio si se están actualizando
    if (updateLigaDto.fechaInicio && updateLigaDto.fechaFin) {
      if (updateLigaDto.fechaFin <= updateLigaDto.fechaInicio) {
        throw new ConflictException('La fecha de fin debe ser posterior a la fecha de inicio');
      }
    } else if (updateLigaDto.fechaInicio && liga.fechaFin <= updateLigaDto.fechaInicio) {
      throw new ConflictException('La fecha de fin debe ser posterior a la fecha de inicio');
    } else if (updateLigaDto.fechaFin && updateLigaDto.fechaFin <= liga.fechaInicio) {
      throw new ConflictException('La fecha de fin debe ser posterior a la fecha de inicio');
    }

    // Actualizar la liga
    const updatedLiga = this.ligasRepository.merge(liga, updateLigaDto);
    return this.ligasRepository.save(updatedLiga);
  }

  async remove(id: string): Promise<void> {
    const result = await this.ligasRepository.delete(id);
    
    if (result.affected === 0) {
      throw new NotFoundException(`Liga con ID ${id} no encontrada`);
    }
  }

  async findActivas(): Promise<Liga[]> {
    return this.ligasRepository.find({
      where: { estado: EstadoLiga.ACTIVA },
    });
  }

  async findProximas(): Promise<Liga[]> {
    const today = new Date();
    
    return this.ligasRepository.find({
      where: {
        fechaInicio: Between(today, new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)), // Próximos 30 días
        estado: EstadoLiga.PREPARACION,
      },
    });
  }

  async activarLiga(id: string): Promise<Liga> {
    const liga = await this.findOne(id);
    
    if (liga.estado !== EstadoLiga.PREPARACION) {
      throw new ConflictException('Solo se pueden activar ligas en estado de preparación');
    }
    
    liga.estado = EstadoLiga.ACTIVA;
    return this.ligasRepository.save(liga);
  }

  async finalizarLiga(id: string): Promise<Liga> {
    const liga = await this.findOne(id);
    
    if (liga.estado !== EstadoLiga.ACTIVA) {
      throw new ConflictException('Solo se pueden finalizar ligas activas');
    }
    
    liga.estado = EstadoLiga.FINALIZADA;
    return this.ligasRepository.save(liga);
  }
}
