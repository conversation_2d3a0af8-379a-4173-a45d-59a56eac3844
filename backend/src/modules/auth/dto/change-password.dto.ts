import { IsNotEmpty, IsString, MinLength, Matches } from 'class-validator';

/**
 * DTO para el cambio de contraseña
 * Valida que la contraseña actual sea proporcionada y que la nueva cumpla con los requisitos
 */
export class ChangePasswordDto {
  /**
   * Contraseña actual del usuario
   * @example "ContraseñaActual123"
   */
  @IsNotEmpty({ message: 'La contraseña actual es requerida' })
  @IsString({ message: 'La contraseña actual debe ser una cadena de texto' })
  currentPassword: string;

  /**
   * Nueva contraseña para el usuario
   * Debe tener al menos 8 caracteres, incluir una letra mayúscula, una minúscula y un número
   * @example "NuevaContraseña123"
   */
  @IsNotEmpty({ message: 'La nueva contraseña es requerida' })
  @IsString({ message: 'La nueva contraseña debe ser una cadena de texto' })
  @MinLength(8, { message: 'La contraseña debe tener al menos 8 caracteres' })
  @Matches(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'La contraseña debe incluir al menos una letra mayúscula, una minúscula y un número',
  })
  newPassword: string;

  /**
   * Confirmación de la nueva contraseña
   * Debe coincidir exactamente con la nueva contraseña
   * @example "NuevaContraseña123"
   */
  @IsNotEmpty({ message: 'La confirmación de contraseña es requerida' })
  @IsString({ message: 'La confirmación de contraseña debe ser una cadena de texto' })
  confirmPassword: string;
}
