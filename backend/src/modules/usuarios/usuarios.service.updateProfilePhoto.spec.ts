import { Test, TestingModule } from '@nestjs/testing';
import { UsuariosService } from './usuarios.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Usuario } from './entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { ConfigModule } from '@nestjs/config';
import { DatabaseTestHelper } from '../../utils/database-test.helper';

/**
 * Test para el método updateProfilePhoto del UsuariosService
 * Este test verifica la funcionalidad de actualización de fotos de perfil
 * siguiendo el patrón AAA (Arrange-Act-Assert)
 */
describe('UsuariosService - updateProfilePhoto', () => {
  let service: UsuariosService;
  let repository: Repository<Usuario>;
  let dbHelper: DatabaseTestHelper;
  let testUser: Usuario;

  // Datos de prueba para la foto
  const testPhotoData = {
    filename: 'test-photo.jpg',
    path: '/uploads/profiles/test-photo.jpg',
    mimetype: 'image/jpeg',
    size: 12345
  };

  /**
   * Configuración del módulo de prueba antes de cada test
   * Utilizamos una base de datos real para seguir las normas del proyecto
   */
  beforeAll(async () => {
    // Crear una configuración de base de datos de prueba
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
      ],
      providers: [
        UsuariosService,
        {
          provide: getRepositoryToken(Usuario),
          useClass: Repository,
        },
        {
          provide: DataSource,
          useFactory: () => ({
            createEntityManager: jest.fn(),
          }),
        },
      ],
    }).compile();

    service = module.get<UsuariosService>(UsuariosService);
    repository = module.get<Repository<Usuario>>(getRepositoryToken(Usuario));

    // Inicializar el helper de base de datos
    dbHelper = new DatabaseTestHelper();
    await dbHelper.initializeTestDatabase();

    // Crear un usuario de prueba en la base de datos
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = await repository.save({
      nombre: 'Test User',
      alias: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
    });
  });

  /**
   * Limpieza después de todos los tests
   */
  afterAll(async () => {
    // Eliminar los datos de prueba
    await repository.delete({ email: '<EMAIL>' });
    // Cerrar la conexión a la base de datos
    await dbHelper.closeTestDatabase();
  });

  /**
   * Test principal: Actualizar foto de perfil
   */
  it('should update the user profile photo', async () => {
    // Arrange - Verificar que el usuario no tenga foto al inicio
    const userBeforeUpdate = await repository.findOne({
      where: { id: testUser.id }
    });
    expect(userBeforeUpdate.foto).toBeNull();

    // Act - Llamar al método para actualizar la foto de perfil
    const updatedUser = await service.updateProfilePhoto(testUser.id, testPhotoData);

    // Assert - Verificar que la foto se haya actualizado correctamente
    expect(updatedUser).toBeDefined();
    expect(updatedUser.foto).toBe(`/uploads/profiles/${testPhotoData.filename}`);

    // Verificar en la base de datos que el cambio persistió
    const userAfterUpdate = await repository.findOne({
      where: { id: testUser.id }
    });
    expect(userAfterUpdate.foto).toBe(`/uploads/profiles/${testPhotoData.filename}`);
  });

  /**
   * Test de error: Actualizar foto para un usuario que no existe
   */
  it('should throw error when user is not found', async () => {
    // Arrange - ID de usuario que no existe
    const nonExistentUserId = '00000000-0000-0000-0000-000000000000';

    // Act & Assert - Verificar que se lance un error
    await expect(
      service.updateProfilePhoto(nonExistentUserId, testPhotoData)
    ).rejects.toThrow();
  });
});
