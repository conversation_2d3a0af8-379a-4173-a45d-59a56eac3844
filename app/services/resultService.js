/**
 * Result Service
 * Gestiona todas las operaciones relacionadas con resultados de WODs
 * Implementa optimizaciones específicas para validación de resultados y sistema de puntuación
 */
import apiClient from '../api/apiClient';
import cacheManager from '../utils/cacheManager';
import leagueService from './leagueService';

// Claves de caché para resultados
const CACHE_KEYS = {
  MY_RESULT: (wodId) => `wod_${wodId}_my_result`,
  WOD_LEADERBOARD: (wodId) => `wod_${wodId}_leaderboard`,
  USER_RESULTS_HISTORY: 'user_results_history'
};

const resultService = {
  /**
   * Registra un resultado para un WOD
   * @param {Object} resultData - Datos del resultado
   * @param {string} resultData.wodId - ID del WOD
   * @param {number|string} resultData.value - Valor del resultado (tiempo, reps, peso, etc.)
   * @param {boolean} resultData.timeCap - Si se alcanzó el tiempo máximo (para WODs ForTime)
   * @param {string} resultData.comment - Comentario opcional
   * @returns {Promise<Object>} - Resultado registrado
   */
  async submitResult(resultData) {
    try {
      // Optimización local: Validar rango antes de enviar al servidor
      if (!this.validateResultRangeLocally(resultData.wodId, resultData.value, resultData.wodType)) {
        console.warn('Resultado fuera del rango típico, solicitando confirmación');
        return {
          warning: true,
          message: 'Este resultado parece estar fuera del rango habitual. ¿Estás seguro?',
          resultData
        };
      }
      
      // Enviar al servidor
      const result = await apiClient.post('/api/results', resultData);
      
      // Invalidar cachés afectadas
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.MY_RESULT(resultData.wodId)),
        cacheManager.invalidate(CACHE_KEYS.WOD_LEADERBOARD(resultData.wodId)),
        cacheManager.invalidate(CACHE_KEYS.USER_RESULTS_HISTORY)
      ]);
      
      // Si conocemos la liga, invalidar también su clasificación
      if (resultData.leagueId) {
        try {
          await leagueService.invalidateLeagueCache(resultData.leagueId);
        } catch (leagueError) {
          console.warn('Error invalidando caché de liga:', leagueError);
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error enviando resultado:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Actualiza un resultado existente
   * @param {string} resultId - ID del resultado a actualizar
   * @param {Object} resultData - Nuevos datos del resultado
   * @returns {Promise<Object>} - Resultado actualizado
   */
  async updateResult(resultId, resultData) {
    try {
      const result = await apiClient.patch(`/api/results/${resultId}`, resultData);
      
      // Invalidar cachés afectadas
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.MY_RESULT(resultData.wodId)),
        cacheManager.invalidate(CACHE_KEYS.WOD_LEADERBOARD(resultData.wodId)),
        cacheManager.invalidate(CACHE_KEYS.USER_RESULTS_HISTORY)
      ]);
      
      // Si conocemos la liga, invalidar también su clasificación
      if (resultData.leagueId) {
        try {
          await leagueService.invalidateLeagueCache(resultData.leagueId);
        } catch (leagueError) {
          console.warn('Error invalidando caché de liga:', leagueError);
        }
      }
      
      return result;
    } catch (error) {
      console.error(`Error actualizando resultado ${resultId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene mi resultado para un WOD específico
   * @param {string} wodId - ID del WOD
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Mi resultado
   */
  async getMyResult(wodId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.MY_RESULT(wodId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedResult = await cacheManager.get(cacheKey);
        if (cachedResult) {
          return cachedResult;
        }
      }
      
      // Obtener datos frescos
      const result = await apiClient.get(`/api/wods/${wodId}/my-result`);
      
      // Guardar en caché (15 minutos)
      await cacheManager.set(cacheKey, result, 15 * 60 * 1000);
      
      return result;
    } catch (error) {
      console.error(`Error obteniendo mi resultado para WOD ${wodId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene la clasificación específica de un WOD
   * @param {string} wodId - ID del WOD
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Clasificación del WOD
   */
  async getWodLeaderboard(wodId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.WOD_LEADERBOARD(wodId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeaderboard = await cacheManager.get(cacheKey);
        if (cachedLeaderboard) {
          return cachedLeaderboard;
        }
      }
      
      // Obtener datos frescos
      const leaderboard = await apiClient.get(`/api/wods/${wodId}/leaderboard`);
      
      // Guardar en caché (5 minutos)
      await cacheManager.set(cacheKey, leaderboard, 5 * 60 * 1000);
      
      return leaderboard;
    } catch (error) {
      console.error(`Error obteniendo clasificación para WOD ${wodId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Reporta un resultado como sospechoso
   * @param {string} resultId - ID del resultado
   * @param {string} reason - Razón del reporte
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async flagSuspiciousResult(resultId, reason) {
    try {
      return await apiClient.post(`/api/results/${resultId}/flag`, { reason });
    } catch (error) {
      console.error(`Error reportando resultado ${resultId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene el historial de resultados del usuario
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de resultados
   */
  async getUserResultsHistory(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedResults = await cacheManager.get(CACHE_KEYS.USER_RESULTS_HISTORY);
        if (cachedResults) {
          return cachedResults;
        }
      }
      
      // Obtener datos frescos
      const results = await apiClient.get('/api/results/user');
      
      // Guardar en caché (30 minutos)
      await cacheManager.set(CACHE_KEYS.USER_RESULTS_HISTORY, results, 30 * 60 * 1000);
      
      return results;
    } catch (error) {
      console.error('Error obteniendo historial de resultados:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Valida localmente si un resultado está dentro de un rango aceptable
   * @param {string} wodId - ID del WOD
   * @param {number|string} value - Valor del resultado
   * @param {string} wodType - Tipo de WOD (AMRAP, FORTIME, etc.)
   * @returns {boolean} - True si el resultado es válido
   * @private
   */
  validateResultRangeLocally(wodId, value, wodType) {
    // Esta función puede mejorar mucho con datos históricos almacenados
    // Por ahora implementamos validación básica por tipo
    
    try {
      if (wodType === 'AMRAP') {
        // Para AMRAP, verificar que las repeticiones sean un número razonable
        const reps = parseInt(value, 10);
        return reps >= 1 && reps <= 1000; // Máximo 1000 reps
      } 
      else if (wodType === 'FORTIME') {
        // Para FORTIME, verificar formato de tiempo hh:mm:ss o mm:ss
        const timeRegex = /^(?:(\d+):)?([0-5]?\d):([0-5]?\d)$/;
        if (!timeRegex.test(value)) return false;
        
        // Convertir a segundos y validar rango
        const parts = value.split(':');
        let seconds = 0;
        
        if (parts.length === 3) {
          // formato hh:mm:ss
          seconds = parseInt(parts[0], 10) * 3600 + parseInt(parts[1], 10) * 60 + parseInt(parts[2], 10);
        } else {
          // formato mm:ss
          seconds = parseInt(parts[0], 10) * 60 + parseInt(parts[1], 10);
        }
        
        return seconds > 0 && seconds <= 7200; // Máximo 2 horas
      }
      else if (wodType === 'MAXWEIGHT') {
        // Para MAXWEIGHT, verificar que el peso sea razonable
        const weight = parseFloat(value);
        return weight > 0 && weight <= 500; // Máximo 500kg
      }
      else if (wodType === 'MAXREPS') {
        // Para MAXREPS, verificar que las repeticiones sean razonables
        const reps = parseInt(value, 10);
        return reps > 0 && reps <= 1000; // Máximo 1000 reps
      }
      else if (wodType === 'EMOM') {
        // Para EMOM, verificar que las rondas sean razonables
        const rounds = parseInt(value, 10);
        return rounds > 0 && rounds <= 100; // Máximo 100 rondas
      }
      
      // Si no es ninguno de los tipos conocidos, asumir válido
      return true;
    } catch (error) {
      console.warn('Error en validación local de resultado:', error);
      return true; // En caso de error, permitir que el servidor valide
    }
  },
  
  /**
   * Calcula localmente los puntos estimados para un resultado
   * @param {Array} leaderboard - Clasificación del WOD
   * @param {number|string} myValue - Mi valor
   * @param {string} wodType - Tipo de WOD (AMRAP, FORTIME, etc.)
   * @returns {number} - Puntos estimados
   */
  calculateEstimatedPoints(leaderboard, myValue, wodType) {
    try {
      if (!leaderboard || !leaderboard.length || !myValue) return 0;
      
      // Encontrar mejor resultado
      let bestValue, myValueNormalized;
      
      if (wodType === 'AMRAP' || wodType === 'MAXWEIGHT' || wodType === 'MAXREPS' || wodType === 'EMOM') {
        // Para estos tipos, mayor es mejor
        bestValue = Math.max(...leaderboard.map(entry => parseFloat(entry.value)));
        myValueNormalized = parseFloat(myValue);
        
        // Fórmula: 100 × (Tus repeticiones ÷ Mejores repeticiones)
        return Math.round(100 * (myValueNormalized / bestValue));
      } 
      else if (wodType === 'FORTIME') {
        // Para FORTIME, menor es mejor
        
        // Convertir todos los tiempos a segundos
        const timeToSeconds = (timeStr) => {
          const parts = timeStr.split(':');
          if (parts.length === 3) {
            return parseInt(parts[0], 10) * 3600 + parseInt(parts[1], 10) * 60 + parseInt(parts[2], 10);
          } else if (parts.length === 2) {
            return parseInt(parts[0], 10) * 60 + parseInt(parts[1], 10);
          }
          return 0;
        };
        
        const valueInSeconds = timeToSeconds(myValue);
        const leaderboardInSeconds = leaderboard.map(entry => timeToSeconds(entry.value));
        bestValue = Math.min(...leaderboardInSeconds);
        
        // Fórmula: 100 × (Mejor tiempo ÷ Tu tiempo)
        return Math.round(100 * (bestValue / valueInSeconds));
      }
      
      return 0;
    } catch (error) {
      console.warn('Error calculando puntos estimados:', error);
      return 0;
    }
  }
};

export default resultService;
