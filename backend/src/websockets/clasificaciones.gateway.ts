import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { ClasificacionesService } from '../modules/clasificaciones/clasificaciones.service';

@WebSocketGateway({ namespace: 'clasificaciones', cors: true })
export class ClasificacionesGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private logger: Logger = new Logger('ClasificacionesGateway');

  constructor(private clasificacionesService: ClasificacionesService) {}

  afterInit(server: Server) {
    this.logger.log('Clasificaciones WebSocket Gateway initialized');
  }

  handleConnection(client: Socket, ...args: any[]) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('suscribirClasificacion')
  handleSuscripcion(client: Socket, payload: { ligaId: string, categoria: string }): void {
    const room = `clasificacion:${payload.ligaId}:${payload.categoria}`;
    client.join(room);
    this.logger.log(`Client ${client.id} joined room ${room}`);
    
    // Enviar clasificación actual
    this.clasificacionesService.obtenerClasificacion(payload.ligaId, payload.categoria)
      .then(clasificacion => {
        client.emit('clasificacionActualizada', {
          ligaId: payload.ligaId,
          categoria: payload.categoria,
          clasificacion,
          timestamp: new Date().toISOString(),
        });
      })
      .catch(error => {
        this.logger.error(`Error al obtener clasificación: ${error.message}`);
      });
  }

  emitirActualizacionClasificacion(ligaId: string, categoria: string): void {
    const room = `clasificacion:${ligaId}:${categoria}`;
    
    this.clasificacionesService.obtenerClasificacion(ligaId, categoria)
      .then(clasificacion => {
        this.server.to(room).emit('clasificacionActualizada', {
          ligaId,
          categoria,
          clasificacion,
          timestamp: new Date().toISOString(),
        });
        this.logger.log(`Clasificación actualizada emitida a ${room}`);
      })
      .catch(error => {
        this.logger.error(`Error al emitir actualización: ${error.message}`);
      });
  }
}
