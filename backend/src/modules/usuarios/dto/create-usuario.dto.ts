import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { GeneroUsuario, NivelUsuario } from '../entities/usuario.entity';

export class CreateUsuarioDto {
  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsNotEmpty()
  @IsString()
  alias: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;

  @IsOptional()
  @IsString()
  foto?: string;

  @IsOptional()
  @IsEnum(NivelUsuario)
  nivel?: NivelUsuario;

  @IsOptional()
  @IsEnum(GeneroUsuario)
  genero?: GeneroUsuario;

  @IsOptional()
  @IsString()
  boxId?: string;
}
