import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

/**
 * Componente para mostrar una tarjeta de batalla en la pantalla Home
 * @param {Object} props - Props
 * @param {Object} props.batalla - Datos de la batalla
 * @param {Function} props.onPress - Función a ejecutar al presionar la tarjeta
 */
const BatallaCard = ({ batalla = {}, onPress }) => {
  // Si no hay batalla, usamos datos de prueba
  const titulo = batalla.titulo || '';
  const imagen = batalla.imagen || 'https://images.unsplash.com/photo-1603233720024-4ee0953d2416?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80';

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <ImageBackground
        source={{ uri: imagen }}
        style={styles.background}
        imageStyle={styles.imageStyle}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.title} numberOfLines={2}>{titulo}</Text>
          </View>
        </LinearGradient>
      </ImageBackground>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 120,
    width: 160,
    borderRadius: 15,
    overflow: 'hidden',
    marginRight: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    backgroundColor: '#232323',
  },
  background: {
    width: '100%',
    height: '100%',
  },
  imageStyle: {
    borderRadius: 15,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    padding: 15,
  },
  contentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default BatallaCard;
