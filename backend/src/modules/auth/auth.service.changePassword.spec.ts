import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsuariosService } from '../usuarios/usuarios.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Usuario } from '../usuarios/entities/usuario.entity';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { DatabaseTestHelper } from '../../utils/database-test.helper';
import { EmailService } from '../email/email.service';

/**
 * Test for the changePassword method in AuthService
 * This test verifies the functionality of the password change feature
 * following the AAA pattern (Arrange-Act-Assert)
 */
describe('AuthService - changePassword', () => {
  let authService: AuthService;
  let usuariosService: UsuariosService;
  let repository: Repository<Usuario>;
  let dbHelper: DatabaseTestHelper;
  let testUser: Usuario;

  // Test data
  const originalPassword = 'password123';
  const newPassword = 'newPassword456';

  /**
   * Setup test module before all tests
   * Using real database connection as per project requirements
   */
  beforeAll(async () => {
    // Create test module with real dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        UsuariosService,
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn().mockReturnValue('test-token'),
            verify: jest.fn().mockReturnValue({ sub: '1' }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              if (key === 'JWT_SECRET') return 'test-secret';
              if (key === 'JWT_EXPIRATION_TIME') return '1h';
              return null;
            }),
          },
        },
        {
          provide: getRepositoryToken(Usuario),
          useClass: Repository,
        },
        {
          provide: DataSource,
          useFactory: () => ({
            createEntityManager: jest.fn(),
          }),
        },
        {
          provide: EmailService,
          useValue: {
            sendVerificationEmail: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    usuariosService = module.get<UsuariosService>(UsuariosService);
    repository = module.get<Repository<Usuario>>(getRepositoryToken(Usuario));
    
    // Initialize database helper
    dbHelper = new DatabaseTestHelper();
    await dbHelper.initializeTestDatabase();
    
    // Create a test user in the database with known password
    const hashedPassword = await bcrypt.hash(originalPassword, 10);
    testUser = await repository.save({
      nombre: 'Test Password Change',
      alias: 'testpassword',
      email: '<EMAIL>',
      password: hashedPassword,
    });
  });

  /**
   * Cleanup after all tests
   */
  afterAll(async () => {
    // Delete test user
    await repository.delete({ email: '<EMAIL>' });
    // Close database connection
    await dbHelper.closeTestDatabase();
  });

  /**
   * Main test: Successful password change
   */
  it('should successfully change user password when current password is correct', async () => {
    // Arrange - Ensure we have a valid user with known password
    const user = await repository.findOne({ 
      where: { email: '<EMAIL>' } 
    });

    // Act - Call the changePassword method
    const result = await authService.changePassword(
      user.id,
      originalPassword,
      newPassword
    );

    // Assert - Verify response and password change
    expect(result).toBeDefined();
    expect(result.message).toBe('Contraseña actualizada correctamente');

    // Verify that the password was actually changed in database
    const updatedUser = await repository.findOne({ 
      where: { id: user.id } 
    });
    const isNewPasswordValid = await bcrypt.compare(
      newPassword, 
      updatedUser.password
    );
    expect(isNewPasswordValid).toBe(true);
  });

  /**
   * Error test: Incorrect current password
   */
  it('should throw UnauthorizedException when current password is incorrect', async () => {
    // Arrange - Get test user
    const user = await repository.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    const wrongPassword = 'wrongPassword';

    // Act & Assert - Verify error handling
    await expect(
      authService.changePassword(user.id, wrongPassword, 'anotherNewPass')
    ).rejects.toThrow(UnauthorizedException);
  });

  /**
   * Error test: New password same as current
   */
  it('should throw BadRequestException when new password is same as current', async () => {
    // Arrange - Get test user and reset password to known value
    const user = await repository.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    const hashedPassword = await bcrypt.hash(originalPassword, 10);
    await repository.update(user.id, { password: hashedPassword });

    // Act & Assert - Verify error handling
    await expect(
      authService.changePassword(user.id, originalPassword, originalPassword)
    ).rejects.toThrow(BadRequestException);
  });
});
