# Arquitectura y Patrones de Diseño - The WOD League

Este documento detalla la arquitectura y los patrones de diseño que deben seguirse en el desarrollo del proyecto The WOD League. Proporciona una guía clara sobre cómo estructurar el código y qué patrones implementar para mantener un código limpio, mantenible y escalable.

## Tabla de Contenidos

1. [Arquitectura General](#arquitectura-general)
2. [Patrones de Diseño](#patrones-de-diseño)
3. [Estructura de Módulos](#estructura-de-módulos)
4. [Flujo de Datos](#flujo-de-datos)
5. [Principios de Diseño](#principios-de-diseño)
6. [Antipatrones a Evitar](#antipatrones-a-evitar)
7. [Ejemplos de Implementación](#ejemplos-de-implementación)

## Arquitectura General

The WOD League sigue una arquitectura de capas basada en el framework NestJS, que implementa el patrón MVC (Modelo-Vista-Controlador) con algunas adaptaciones para APIs RESTful:

### Capas de la Aplicación

1. **Capa de Presentación (Controladores)**
   - Maneja las solicitudes HTTP
   - Valida los datos de entrada
   - Delega la lógica de negocio a los servicios
   - Formatea las respuestas

2. **Capa de Lógica de Negocio (Servicios)**
   - Implementa la lógica de negocio
   - Coordina operaciones complejas
   - Maneja transacciones
   - Aplica reglas de negocio

3. **Capa de Acceso a Datos (Repositorios)**
   - Abstrae las operaciones de base de datos
   - Implementa consultas específicas
   - Maneja la persistencia de datos

4. **Capa de Dominio (Entidades y DTOs)**
   - Define las entidades del dominio
   - Define los objetos de transferencia de datos (DTOs)
   - Implementa validaciones de datos

### Diagrama de Arquitectura

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │     │                 │
│  Controladores  │────▶│    Servicios    │────▶│   Repositorios  │────▶│   Base de Datos │
│                 │     │                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                       ▲                       ▲
        │                       │                       │
        │                       │                       │
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│      DTOs       │     │    Entidades    │     │   Interfaces    │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Patrones de Diseño

### Patrón Repositorio

El patrón Repositorio se utiliza para abstraer la capa de acceso a datos, proporcionando una interfaz unificada para interactuar con la base de datos.

```typescript
@Injectable()
export class UsuariosRepository {
  constructor(
    @InjectRepository(Usuario)
    private repository: Repository<Usuario>,
  ) {}

  async findById(id: string): Promise<Usuario | null> {
    return this.repository.findOne({ where: { id } });
  }

  async findByEmail(email: string): Promise<Usuario | null> {
    return this.repository.findOne({ where: { email } });
  }

  async create(usuario: Partial<Usuario>): Promise<Usuario> {
    const newUsuario = this.repository.create(usuario);
    return this.repository.save(newUsuario);
  }
}
```

### Patrón Servicio

El patrón Servicio encapsula la lógica de negocio y coordina operaciones complejas.

```typescript
@Injectable()
export class UsuariosService {
  constructor(
    private usuariosRepository: UsuariosRepository,
    private configService: ConfigService,
  ) {}

  async findOne(id: string): Promise<Usuario> {
    const usuario = await this.usuariosRepository.findById(id);
    if (!usuario) {
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }
    return usuario;
  }

  async create(createUsuarioDto: CreateUsuarioDto): Promise<Usuario> {
    const existingUsuario = await this.usuariosRepository.findByEmail(createUsuarioDto.email);
    if (existingUsuario) {
      throw new ConflictException('El email ya está registrado');
    }

    const hashedPassword = await bcrypt.hash(createUsuarioDto.password, 10);
    
    return this.usuariosRepository.create({
      ...createUsuarioDto,
      password: hashedPassword,
    });
  }
}
```

### Patrón DTO (Data Transfer Object)

Los DTOs se utilizan para transferir datos entre capas y validar datos de entrada.

```typescript
export class CreateUsuarioDto {
  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @MinLength(8)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, {
    message: 'La contraseña debe contener al menos una letra mayúscula, una minúscula, un número y un carácter especial',
  })
  password: string;

  @IsEnum(NivelUsuario)
  nivel: NivelUsuario;

  @IsEnum(GeneroUsuario)
  genero: GeneroUsuario;
}
```

### Patrón Módulo

NestJS utiliza el patrón Módulo para organizar la aplicación en bloques funcionales cohesivos.

```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([Usuario]),
    ConfigModule,
  ],
  controllers: [UsuariosController],
  providers: [UsuariosService, UsuariosRepository],
  exports: [UsuariosService],
})
export class UsuariosModule {}
```

### Patrón Decorador

Los decoradores se utilizan para añadir metadatos y comportamiento a clases y métodos.

```typescript
@Controller('usuarios')
@ApiTags('usuarios')
export class UsuariosController {
  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Obtener un usuario por ID' })
  @ApiResponse({ status: 200, description: 'Usuario encontrado', type: Usuario })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  findOne(@Param('id') id: string): Promise<Usuario> {
    return this.usuariosService.findOne(id);
  }
}
```

### Patrón Observador (con WebSockets)

El patrón Observador se implementa mediante WebSockets para notificaciones en tiempo real.

```typescript
@WebSocketGateway({ namespace: 'clasificaciones' })
export class ClasificacionesGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  handleConnection(client: Socket) {
    // Manejar conexión
  }

  handleDisconnect(client: Socket) {
    // Manejar desconexión
  }

  @SubscribeMessage('suscribirClasificacion')
  handleSuscripcion(client: Socket, payload: { ligaId: string, categoria: string }) {
    // Suscribir cliente a actualizaciones
  }

  emitirActualizacion(ligaId: string, categoria: string, data: any) {
    this.server.to(`clasificacion:${ligaId}:${categoria}`).emit('clasificacionActualizada', data);
  }
}
```

### Patrón Estrategia

El patrón Estrategia se utiliza para implementar diferentes algoritmos de cálculo de puntuación según el tipo de WOD.

```typescript
interface EstrategiaCalculo {
  calcularPuntuacion(resultado: number, mejorResultado: number): number;
}

class CalculoForTime implements EstrategiaCalculo {
  calcularPuntuacion(resultado: number, mejorResultado: number): number {
    // Menor tiempo es mejor
    return 100 * (mejorResultado / resultado);
  }
}

class CalculoAMRAP implements EstrategiaCalculo {
  calcularPuntuacion(resultado: number, mejorResultado: number): number {
    // Mayor número de repeticiones es mejor
    return 100 * (resultado / mejorResultado);
  }
}

class CalculadoraPuntuacion {
  private estrategias: Map<TipoWOD, EstrategiaCalculo> = new Map();

  constructor() {
    this.estrategias.set(TipoWOD.FORTIME, new CalculoForTime());
    this.estrategias.set(TipoWOD.AMRAP, new CalculoAMRAP());
    // Añadir más estrategias según sea necesario
  }

  calcularPuntuacion(tipoWOD: TipoWOD, resultado: number, mejorResultado: number): number {
    const estrategia = this.estrategias.get(tipoWOD);
    if (!estrategia) {
      throw new Error(`No hay estrategia de cálculo para el tipo de WOD: ${tipoWOD}`);
    }
    return estrategia.calcularPuntuacion(resultado, mejorResultado);
  }
}
```

## Estructura de Módulos

La aplicación se organiza en módulos funcionales, cada uno responsable de una parte específica del dominio:

```
src/
├── modules/
│   ├── usuarios/              # Gestión de usuarios
│   ├── auth/                  # Autenticación y autorización
│   ├── boxes/                 # Gestión de boxes de CrossFit
│   ├── ligas/                 # Gestión de ligas
│   ├── wods/                  # Gestión de WODs
│   ├── resultados/            # Registro y validación de resultados
│   ├── clasificaciones/       # Cálculo de clasificaciones
│   ├── inscripciones/         # Gestión de inscripciones
│   └── notificaciones/        # Sistema de notificaciones
├── common/                    # Código compartido
├── config/                    # Configuración
├── jobs/                      # Tareas programadas
├── websockets/                # Gateways de WebSockets
├── app.module.ts              # Módulo principal
└── main.ts                    # Punto de entrada
```

Cada módulo debe seguir una estructura consistente:

```
modules/usuarios/
├── dto/                       # Data Transfer Objects
│   ├── create-usuario.dto.ts
│   └── update-usuario.dto.ts
├── entities/                  # Entidades de base de datos
│   └── usuario.entity.ts
├── interfaces/                # Interfaces
│   └── usuario.interface.ts
├── usuarios.controller.ts     # Controlador
├── usuarios.service.ts        # Servicio
├── usuarios.repository.ts     # Repositorio
└── usuarios.module.ts         # Módulo
```

## Flujo de Datos

El flujo de datos en la aplicación sigue un patrón unidireccional:

1. **Entrada de Datos**:
   - Los datos entran a través de los controladores
   - Se validan mediante DTOs y pipes
   - Se transforman si es necesario

2. **Procesamiento**:
   - Los servicios procesan los datos
   - Aplican la lógica de negocio
   - Coordinan operaciones complejas

3. **Persistencia**:
   - Los repositorios manejan la persistencia
   - Ejecutan operaciones de base de datos
   - Devuelven entidades o resultados

4. **Salida de Datos**:
   - Los controladores formatean las respuestas
   - Se aplican transformaciones si es necesario
   - Se envían las respuestas al cliente

### Diagrama de Flujo de Datos

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Cliente    │────▶│ Controlador │────▶│  Servicio   │────▶│ Repositorio │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
       ▲                                                           │
       │                                                           │
       │                                                           ▼
       │                                                    ┌─────────────┐
       │                                                    │             │
       └────────────────────────────────────────────────────┤    BD      │
                                                            │             │
                                                            └─────────────┘
```

## Principios de Diseño

### Principios SOLID

1. **Principio de Responsabilidad Única (SRP)**
   - Cada clase debe tener una única responsabilidad
   - Ejemplo: Separar la lógica de negocio (servicios) del acceso a datos (repositorios)

2. **Principio de Abierto/Cerrado (OCP)**
   - Las entidades deben estar abiertas para extensión pero cerradas para modificación
   - Ejemplo: Usar herencia y composición para extender funcionalidad

3. **Principio de Sustitución de Liskov (LSP)**
   - Los objetos de una superclase deben poder ser reemplazados por objetos de una subclase sin afectar la funcionalidad
   - Ejemplo: Implementar interfaces comunes para diferentes tipos de servicios

4. **Principio de Segregación de Interfaces (ISP)**
   - Muchas interfaces específicas son mejores que una interfaz general
   - Ejemplo: Definir interfaces específicas para diferentes aspectos de un servicio

5. **Principio de Inversión de Dependencias (DIP)**
   - Depender de abstracciones, no de implementaciones concretas
   - Ejemplo: Usar inyección de dependencias para desacoplar componentes

### Principios DRY y KISS

- **DRY (Don't Repeat Yourself)**
  - Evitar la duplicación de código
  - Extraer código común a funciones, clases o módulos reutilizables

- **KISS (Keep It Simple, Stupid)**
  - Mantener el código simple y fácil de entender
  - Evitar soluciones complejas cuando una simple es suficiente

## Antipatrones a Evitar

1. **God Object (Objeto Dios)**
   - Evitar clases que hacen demasiado o saben demasiado
   - Solución: Dividir en clases más pequeñas con responsabilidades específicas

2. **Spaghetti Code (Código Espagueti)**
   - Evitar código con flujo de control complejo y difícil de seguir
   - Solución: Estructurar el código de manera clara y lógica

3. **Shotgun Surgery (Cirugía de Escopeta)**
   - Evitar que un cambio requiera modificaciones en muchos lugares diferentes
   - Solución: Encapsular lo que varía y aplicar el principio DRY

4. **Feature Envy (Envidia de Características)**
   - Evitar métodos que acceden más a datos de otras clases que a los propios
   - Solución: Mover el método a la clase a la que pertenece lógicamente

5. **Callback Hell (Infierno de Callbacks)**
   - Evitar anidamiento excesivo de callbacks
   - Solución: Usar async/await, Promises o RxJS

## Ejemplos de Implementación

### Ejemplo 1: Módulo de Usuarios

```typescript
// usuario.entity.ts
@Entity('usuarios')
export class Usuario {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  nombre: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({
    type: 'enum',
    enum: NivelUsuario,
    default: NivelUsuario.INTERMEDIO,
  })
  nivel: NivelUsuario;

  @Column({
    type: 'enum',
    enum: GeneroUsuario,
  })
  genero: GeneroUsuario;

  @Column({
    type: 'enum',
    enum: RolUsuario,
    default: RolUsuario.USUARIO,
  })
  rol: RolUsuario;
}

// usuarios.repository.ts
@Injectable()
export class UsuariosRepository {
  constructor(
    @InjectRepository(Usuario)
    private repository: Repository<Usuario>,
  ) {}

  async findById(id: string): Promise<Usuario | null> {
    return this.repository.findOne({ where: { id } });
  }

  async findByEmail(email: string): Promise<Usuario | null> {
    return this.repository.findOne({ where: { email } });
  }

  async create(usuario: Partial<Usuario>): Promise<Usuario> {
    const newUsuario = this.repository.create(usuario);
    return this.repository.save(newUsuario);
  }

  async update(id: string, data: Partial<Usuario>): Promise<Usuario> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}

// usuarios.service.ts
@Injectable()
export class UsuariosService {
  constructor(
    private usuariosRepository: UsuariosRepository,
  ) {}

  async findOne(id: string): Promise<Usuario> {
    const usuario = await this.usuariosRepository.findById(id);
    if (!usuario) {
      throw new NotFoundException(`Usuario con ID ${id} no encontrado`);
    }
    return usuario;
  }

  async findByEmail(email: string): Promise<Usuario> {
    const usuario = await this.usuariosRepository.findByEmail(email);
    if (!usuario) {
      throw new NotFoundException(`Usuario con email ${email} no encontrado`);
    }
    return usuario;
  }

  async create(createUsuarioDto: CreateUsuarioDto): Promise<Usuario> {
    const existingUsuario = await this.usuariosRepository.findByEmail(createUsuarioDto.email);
    if (existingUsuario) {
      throw new ConflictException('El email ya está registrado');
    }

    const hashedPassword = await bcrypt.hash(createUsuarioDto.password, 10);
    
    return this.usuariosRepository.create({
      ...createUsuarioDto,
      password: hashedPassword,
    });
  }

  async update(id: string, updateUsuarioDto: UpdateUsuarioDto): Promise<Usuario> {
    await this.findOne(id);
    
    if (updateUsuarioDto.password) {
      updateUsuarioDto.password = await bcrypt.hash(updateUsuarioDto.password, 10);
    }
    
    return this.usuariosRepository.update(id, updateUsuarioDto);
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id);
    await this.usuariosRepository.remove(id);
  }
}

// usuarios.controller.ts
@Controller('usuarios')
@ApiTags('usuarios')
export class UsuariosController {
  constructor(private readonly usuariosService: UsuariosService) {}

  @Post()
  @ApiOperation({ summary: 'Crear un nuevo usuario' })
  @ApiResponse({ status: 201, description: 'Usuario creado', type: Usuario })
  @ApiResponse({ status: 400, description: 'Datos inválidos' })
  @ApiResponse({ status: 409, description: 'Email ya registrado' })
  create(@Body() createUsuarioDto: CreateUsuarioDto): Promise<Usuario> {
    return this.usuariosService.create(createUsuarioDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  @ApiOperation({ summary: 'Obtener todos los usuarios' })
  @ApiResponse({ status: 200, description: 'Lista de usuarios', type: [Usuario] })
  findAll(): Promise<Usuario[]> {
    return this.usuariosService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Obtener un usuario por ID' })
  @ApiResponse({ status: 200, description: 'Usuario encontrado', type: Usuario })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  findOne(@Param('id') id: string): Promise<Usuario> {
    return this.usuariosService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Actualizar un usuario' })
  @ApiResponse({ status: 200, description: 'Usuario actualizado', type: Usuario })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  update(
    @Param('id') id: string,
    @Body() updateUsuarioDto: UpdateUsuarioDto,
    @Request() req,
  ): Promise<Usuario> {
    // Solo los administradores pueden actualizar otros usuarios
    if (req.user.id !== id && req.user.rol !== RolUsuario.ADMIN) {
      throw new ForbiddenException('No tienes permiso para actualizar este usuario');
    }
    return this.usuariosService.update(id, updateUsuarioDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  @ApiOperation({ summary: 'Eliminar un usuario' })
  @ApiResponse({ status: 200, description: 'Usuario eliminado' })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  remove(@Param('id') id: string): Promise<void> {
    return this.usuariosService.remove(id);
  }
}

// usuarios.module.ts
@Module({
  imports: [
    TypeOrmModule.forFeature([Usuario]),
  ],
  controllers: [UsuariosController],
  providers: [UsuariosService, UsuariosRepository],
  exports: [UsuariosService],
})
export class UsuariosModule {}
```

### Ejemplo 2: Implementación del Patrón Estrategia para Cálculo de Puntuaciones

```typescript
// estrategias/calculo-puntuacion.strategy.ts
export interface EstrategiaCalculoPuntuacion {
  calcular(resultado: number, mejorResultado: number): number;
}

@Injectable()
export class CalculoForTimeStrategy implements EstrategiaCalculoPuntuacion {
  calcular(resultado: number, mejorResultado: number): number {
    // Menor tiempo es mejor
    return 100 * (mejorResultado / resultado);
  }
}

@Injectable()
export class CalculoAMRAPStrategy implements EstrategiaCalculoPuntuacion {
  calcular(resultado: number, mejorResultado: number): number {
    // Mayor número de repeticiones es mejor
    return 100 * (resultado / mejorResultado);
  }
}

@Injectable()
export class CalculoMaxWeightStrategy implements EstrategiaCalculoPuntuacion {
  calcular(resultado: number, mejorResultado: number): number {
    // Mayor peso es mejor
    return 100 * (resultado / mejorResultado);
  }
}

// calculadora-puntuacion.service.ts
@Injectable()
export class CalculadoraPuntuacionService {
  private estrategias: Map<TipoWOD, EstrategiaCalculoPuntuacion>;

  constructor(
    private readonly calculoForTimeStrategy: CalculoForTimeStrategy,
    private readonly calculoAMRAPStrategy: CalculoAMRAPStrategy,
    private readonly calculoMaxWeightStrategy: CalculoMaxWeightStrategy,
  ) {
    this.estrategias = new Map();
    this.estrategias.set(TipoWOD.FORTIME, this.calculoForTimeStrategy);
    this.estrategias.set(TipoWOD.AMRAP, this.calculoAMRAPStrategy);
    this.estrategias.set(TipoWOD.MAXREPS, this.calculoAMRAPStrategy);
    this.estrategias.set(TipoWOD.MAXWEIGHT, this.calculoMaxWeightStrategy);
    this.estrategias.set(TipoWOD.EMOM, this.calculoAMRAPStrategy);
  }

  calcularPuntuacion(tipoWOD: TipoWOD, resultado: number, mejorResultado: number): number {
    const estrategia = this.estrategias.get(tipoWOD);
    if (!estrategia) {
      throw new Error(`No hay estrategia de cálculo para el tipo de WOD: ${tipoWOD}`);
    }
    
    const puntuacion = estrategia.calcular(resultado, mejorResultado);
    
    // Redondear a 2 decimales
    return Math.round(puntuacion * 100) / 100;
  }
}
```

## Conclusión

Seguir esta arquitectura y estos patrones de diseño nos permitirá construir una aplicación robusta, mantenible y escalable. La estructura modular facilita la colaboración entre desarrolladores y la implementación de nuevas características. Los patrones de diseño proporcionan soluciones probadas a problemas comunes, mejorando la calidad del código y reduciendo la deuda técnica.

Recuerda que estos patrones son guías, no reglas rígidas. Siempre debemos adaptar nuestras soluciones al contexto específico del problema que estamos resolviendo.

---

*Última actualización: Mayo 2025*
