import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResultadosService } from './resultados.service';
import { ResultadosController } from './resultados.controller';
import { Resultado } from './entities/resultado.entity';
import { WodsModule } from '../wods/wods.module';
import { UsuariosModule } from '../usuarios/usuarios.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Resultado]),
    WodsModule,
    UsuariosModule,
  ],
  controllers: [ResultadosController],
  providers: [ResultadosService],
  exports: [ResultadosService],
})
export class ResultadosModule {}
