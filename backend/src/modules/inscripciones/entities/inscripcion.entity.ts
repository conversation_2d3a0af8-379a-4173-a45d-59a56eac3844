import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Usuario } from '../../usuarios/entities/usuario.entity';
import { Liga } from '../../ligas/entities/liga.entity';

export enum EstadoInscripcion {
  PENDIENTE = 'Pendiente',
  PAGADA = 'Pagada',
  CANCELADA = 'Cancelada',
  RECHAZADA = 'Rechazada',
}

export enum MetodoPago {
  STRIPE = 'Stripe',
  TRANSFERENCIA = 'Transferencia',
  EFECTIVO = 'Efectivo',
  OTRO = 'Otro',
}

@Entity('inscripciones')
export class Inscripcion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Usuario)
  @JoinColumn({ name: 'usuario_id' })
  usuario: Usuario;

  @Column({ name: 'usuario_id' })
  usuarioId: string;

  @ManyToOne(() => Liga)
  @JoinColumn({ name: 'liga_id' })
  liga: Liga;

  @Column({ name: 'liga_id' })
  ligaId: string;

  @Column({ name: 'fecha_inscripcion' })
  fechaInscripcion: Date;

  @Column({
    type: 'enum',
    enum: EstadoInscripcion,
    default: EstadoInscripcion.PENDIENTE,
  })
  estado: EstadoInscripcion;

  @Column({ name: 'fecha_pago', nullable: true })
  fechaPago: Date;

  @Column({
    type: 'enum',
    enum: MetodoPago,
    nullable: true,
  })
  metodoPago: MetodoPago;

  @Column({ name: 'referencia_pago', nullable: true })
  referenciaPago: string;

  @Column({ name: 'monto_pagado', type: 'decimal', precision: 10, scale: 2, default: 0 })
  montoPagado: number;

  @Column({ name: 'es_early', default: false })
  esEarly: boolean;

  @Column({ name: 'categoria', length: 50 })
  categoria: string;

  @Column({ name: 'comentarios', type: 'text', nullable: true })
  comentarios: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
