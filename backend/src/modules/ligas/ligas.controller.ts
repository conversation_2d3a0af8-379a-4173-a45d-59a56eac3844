import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { LigasService } from './ligas.service';
import { CreateLigaDto } from './dto/create-liga.dto';
import { UpdateLigaDto } from './dto/update-liga.dto';
import { Liga } from './entities/liga.entity';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RolUsuario } from '../usuarios/entities/usuario.entity';

@Controller('ligas')
export class LigasController {
  constructor(private readonly ligasService: LigasService) {}

  @Post()
  // @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles(RolUsuario.ADMIN)
  create(@Body() createLigaDto: CreateLigaDto): Promise<Liga> {
    return this.ligasService.create(createLigaDto);
  }

  @Get()
  findAll(): Promise<Liga[]> {
    return this.ligasService.findAll();
  }

  @Get('activas')
  findActivas(): Promise<Liga[]> {
    return this.ligasService.findActivas();
  }

  @Get('proximas')
  findProximas(): Promise<Liga[]> {
    return this.ligasService.findProximas();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Liga> {
    return this.ligasService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  update(@Param('id') id: string, @Body() updateLigaDto: UpdateLigaDto): Promise<Liga> {
    return this.ligasService.update(id, updateLigaDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  remove(@Param('id') id: string): Promise<void> {
    return this.ligasService.remove(id);
  }

  @Post(':id/activar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  activarLiga(@Param('id') id: string): Promise<Liga> {
    return this.ligasService.activarLiga(id);
  }

  @Post(':id/finalizar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  finalizarLiga(@Param('id') id: string): Promise<Liga> {
    return this.ligasService.finalizarLiga(id);
  }
}
