import React from 'react';
import { DateInput, useInput, useNotify, useRecordContext, FormDataConsumer } from 'react-admin';
import { Button, Box } from '@mui/material';
import { addDays } from 'date-fns';

interface SixWeekDateInputProps {
  source: string;
  label?: string;
  fullWidth?: boolean;
  helperText?: string;
  validate?: any;
  // Field name that contains the start date (defaults to "fechaInicio")
  startDateField?: string;
  // Include any other props that might be passed to DateInput
  [key: string]: any;
}

/**
 * Componente personalizado que añade un botón para ajustar la fecha de finalización
 * a 6 semanas después de la fecha de inicio
 */
export const SixWeekDateInput = (props: SixWeekDateInputProps) => {
  const { source, label, startDateField = 'fechaInicio', ...rest } = props;
  const notify = useNotify();
  
  return (
    <FormDataConsumer>
      {({ formData }) => {
        // Función para ajustar la fecha de fin a 6 semanas después de la fecha de inicio
        const handleAdjustDate = () => {
          // Obtener el valor actual de la fecha de inicio del campo especificado
          const startDate = formData?.[startDateField];
          
          if (!startDate) {
            notify(`Por favor, selecciona primero una fecha de inicio en el campo ${startDateField}`, { type: 'warning' });
            return;
          }
          
          try {
            // Convertir a objeto Date si es una string
            const startDateObj = typeof startDate === 'string' 
              ? new Date(startDate) 
              : startDate;
              
            // Calcular la fecha 6 semanas (42 días) después
            const endDate = addDays(startDateObj, 42);
            
            // No podemos modificar directamente el formData, pero podemos
            // mostrar una notificación al usuario con la fecha calculada
            // para que la actualice manualmente
            const formattedDate = endDate.toISOString().split('T')[0];
            
            // Calcular el número real de semanas entre las fechas
            const startMs = startDateObj.getTime();
            const endMs = endDate.getTime();
            const diffDays = Math.ceil((endMs - startMs) / (1000 * 60 * 60 * 24));
            const diffWeeks = Math.ceil(diffDays / 7);
            
            // Desencadenar un evento de cambio en el input para la fecha fin
            // Usamos el nombre del campo definido en source para buscar el input
            const fechaFinInput = document.querySelector(`input[name="${source}"]`);
            if (fechaFinInput) {
              // Establecer el valor
              (fechaFinInput as HTMLInputElement).value = formattedDate;
              
              // Disparar un evento de cambio para que React Admin actualice el formulario
              const event = new Event('input', { bubbles: true });
              fechaFinInput.dispatchEvent(event);
              
              // Actualizar el campo de duración si existe
              // Primero intentamos con duracionSemanas (para ligas)
              let duracionInput = document.querySelector('input[name="duracionSemanas"]');
              
              // Si no existe, intentamos con el campo semana (para WODs)
              if (!duracionInput) {
                duracionInput = document.querySelector('input[name="semana"]');
              }
              
              if (duracionInput) {
                (duracionInput as HTMLInputElement).value = String(diffWeeks);
                duracionInput.dispatchEvent(event);
              }
              
              notify(`Fecha de finalización ajustada a ${diffWeeks} semanas`, { type: 'success' });
            } else {
              notify(`Establece la fecha de fin a ${formattedDate} (${diffWeeks} semanas después)`, { type: 'info' });
            }
          } catch (error) {
            notify('Error al ajustar la fecha', { type: 'error' });
            console.error('Error al ajustar la fecha:', error);
          }
        };
        return (
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
            <Box sx={{ flexGrow: 1 }}>
              <DateInput source={source} label={label} {...rest} />
            </Box>
            <Button 
              variant="contained" 
              color="primary"
              onClick={handleAdjustDate}
              sx={{ mt: 1 }} // Alinear con el campo de fecha
            >
              Ajustar a 6 semanas
            </Button>
          </Box>
        );
      }}
    </FormDataConsumer>
  );
};
