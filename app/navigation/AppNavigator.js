import React, { useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import LogoutModal from '../components/LogoutModal';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Importar pantallas
import HomeScreen from '../screens/HomeScreen';
import WodDetailsScreen from '../screens/WodDetailsScreen';
import CreateBattleScreen from '../screens/CreateBattleScreen';
import BattleDetailsScreen from '../screens/BattleDetailsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import PasswordSettingsScreen from '../screens/PasswordSettingsScreen';
import DeleteAccountScreen from '../screens/DeleteAccountScreen';
import RankingScreen from '../screens/RankingScreen';
import RecordsScreen from '../screens/RecordsScreen';
import HelpScreen from '../screens/HelpScreen';
import CustomerServiceScreen from '../screens/CustomerServiceScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Stack principal que incluye HomeScreen y todas las pantallas relacionadas
const HomeStack = () => (
  <Stack.Navigator
    screenOptions={() => ({
      headerStyle: {
        backgroundColor: '#4a90e2',
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    })}
  >
    <Stack.Screen
      name="HomeList"
      component={HomeScreen}
      options={{
        title: 'The WOD League',
      }}
    />
    <Stack.Screen
      name="WodDetails"
      component={WodDetailsScreen}
      options={({ navigation }) => ({
        title: 'Detalles del WOD',
        headerLeft: () => (
          <Ionicons
            name="arrow-back"
            size={25}
            color="#fff"
            style={{ marginLeft: 15 }}
            onPress={() => navigation.goBack()}
          />
        ),
      })}
    />
    <Stack.Screen
      name="CreateBattle"
      component={CreateBattleScreen}
      options={({ navigation }) => ({
        title: 'Nueva Batalla',
        headerLeft: () => (
          <Ionicons
            name="arrow-back"
            size={25}
            color="#fff"
            style={{ marginLeft: 15 }}
            onPress={() => navigation.goBack()}
          />
        ),
      })}
    />
    <Stack.Screen
      name="BattleDetails"
      component={BattleDetailsScreen}
      options={({ navigation }) => ({
        title: 'Detalles de la Batalla',
        headerLeft: () => (
          <Ionicons
            name="arrow-back"
            size={25}
            color="#fff"
            style={{ marginLeft: 15 }}
            onPress={() => navigation.goBack()}
          />
        ),
      })}
    />
  </Stack.Navigator>
);

// Stack para el perfil
const ProfileStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen
      name="ProfileMain"
      component={ProfileScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="EditProfile"
      component={EditProfileScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="Settings"
      component={SettingsScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="NotificationSettings"
      component={NotificationSettingsScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="PasswordSettings"
      component={PasswordSettingsScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="DeleteAccount"
      component={DeleteAccountScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen
      name="Help"
      component={HelpScreen}
      options={{ headerShown: false }}
    />

    <Stack.Screen
      name="CustomerService"
      component={CustomerServiceScreen}
      options={{ headerShown: false }}
    />
  </Stack.Navigator>
);

// Stack para el ranking
const RankingStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#4a90e2',
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    }}
  >
    <Stack.Screen
      name="RankingMain"
      component={RankingScreen}
      options={{ title: 'Ranking' }}
    />
  </Stack.Navigator>
);

// Stack para los records
const RecordsStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: '#4a90e2',
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    }}
  >
    <Stack.Screen
      name="RecordsMain"
      component={RecordsScreen}
      options={{ title: 'Records' }}
    />
  </Stack.Navigator>
);

// Componente vacío para la pantalla de logout
const EmptyScreen = () => {
  return <View style={{ flex: 1, backgroundColor: 'transparent' }} />;
};

// Stack para las notificaciones
const NotificationsStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen
      name="NotificationsList"
      component={NotificationsScreen}
    />
  </Stack.Navigator>
);

// Context global para el modal de logout
export const LogoutModalContext = React.createContext({
  showLogoutModal: false,
  setShowLogoutModal: () => { },
});

// Proveedor del contexto de logout
export const LogoutModalProvider = ({ children }) => {
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  return (
    <LogoutModalContext.Provider value={{ showLogoutModal, setShowLogoutModal }}>
      {children}
    </LogoutModalContext.Provider>
  );
};

const MainNavigator = () => {
  const { logout } = useAuth();
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const navigation = useNavigation();

  const handleLogout = async () => {
    try {
      await logout();
      // Limpiar el token de Strapi al cerrar sesión
      await AsyncStorage.removeItem('strapi-auth');
      navigation.reset({
        index: 0,
        routes: [{ name: 'Auth' }]
      });
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    } finally {
      setShowLogoutModal(false);
    }
  };

  return (
    <>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Home') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Profile') {
              iconName = focused ? 'person' : 'person-outline';
            } else if (route.name === 'Ranking') {
              iconName = focused ? 'podium' : 'podium-outline';
            } else if (route.name === 'Notifications') {
              iconName = focused ? 'notifications' : 'notifications-outline';
            } else if (route.name === 'Logout') {
              iconName = 'log-out-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#e0fe10',
          tabBarInactiveTintColor: '#ffffff',
          tabBarStyle: {
            backgroundColor: '#2e2e2e',
            borderTopWidth: 0,
            elevation: 8,
            paddingBottom: 15,
            paddingTop: 15,
            height: 80,
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: 'bold',
            marginBottom: 5,
          },
        })}
      >
        <Tab.Screen
          name="Home"
          component={HomeStack}
          options={{ title: 'Inicio' }}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileStack}
          options={{ title: 'Mi Perfil' }}
        />
        <Tab.Screen
          name="Ranking"
          component={RankingStack}
          options={{ title: 'Ranking' }}
        />
        <Tab.Screen
          name="Notifications"
          component={NotificationsStack}
          options={{ title: 'Notificaciones' }}
        />
        <Tab.Screen
          name="Logout"
          component={EmptyScreen}
          options={{
            title: 'Salir',
            tabBarButton: (props) => (
              <TouchableOpacity
                {...props}
                style={[props.style, styles.logoutTab]}
                onPress={() => setShowLogoutModal(true)}
              />
            )
          }}
        />
      </Tab.Navigator>

      {/* Modal de logout */}
      <LogoutModal
        visible={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={handleLogout}
      />
    </>
  );
};

const styles = StyleSheet.create({
  logoutTab: {
    borderLeftWidth: 1,
    borderLeftColor: '#598392',
  }
});

export default MainNavigator;
