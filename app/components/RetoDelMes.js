import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

/**
 * Componente para mostrar el Reto del Mes en la pantalla Home
 * @param {Object} props - Props
 * @param {Object} props.reto - Datos del reto 
 * @param {Function} props.onPress - Función a ejecutar al presionar el componente
 */
const RetoDelMes = ({ reto = {}, onPress }) => {
  // Si no hay reto, usamos datos de prueba
  const titulo = reto.titulo || 'Reto Del Mes';
  const descripcion = reto.descripcion || 'Plank With Hip Twist';
  const imagen = reto.imagen || 'https://images.unsplash.com/photo-1517838277536-f5f99be501cd?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80';

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <ImageBackground
        source={{ uri: imagen }}
        style={styles.background}
        imageStyle={styles.imageStyle}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.4)', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.title}>{titulo}</Text>
            <Text style={styles.description}>{descripcion}</Text>
          </View>
        </LinearGradient>
      </ImageBackground>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 120,
    marginHorizontal: 15,
    marginVertical: 10,
    borderRadius: 15,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    backgroundColor: '#232323',
  },
  background: {
    width: '100%',
    height: '100%',
  },
  imageStyle: {
    borderRadius: 15,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    padding: 15,
  },
  contentContainer: {
    justifyContent: 'center',
  },
  title: {
    color: '#e0fe10',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  description: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default RetoDelMes;
