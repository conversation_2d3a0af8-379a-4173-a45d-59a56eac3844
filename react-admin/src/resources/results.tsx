import { 
  List, 
  Datagrid, 
  TextField, 
  ReferenceField,
  Edit, 
  SimpleForm, 
  SelectInput,
  Create,
  ReferenceInput,
  AutocompleteInput,
  NumberInput,
  TextInput,
  required
} from 'react-admin';

// Lista de Resultados
export const ResultList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <ReferenceField source="usuarioId" reference="users" label="Atleta">
        <TextField source="nombre" />
      </ReferenceField>
      <ReferenceField source="wodId" reference="wods" label="WOD">
        <TextField source="nombre" />
      </ReferenceField>
      <TextField source="puntuacion" />
      <TextField source="tiempo" />
      <TextField source="estadoVerificacion" />
    </Datagrid>
  </List>
);

// Función para formatear el tiempo en minutos:segundos
const formatTimeInput = (value: number | null): string => {
  if (!value) return '';
  const minutes = Math.floor(value / 60);
  const seconds = value % 60;
  return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
};

// Función para convertir minutos:segundos a segundos
const parseTimeInput = (value: string | number | null): number | null => {
  if (value === null || value === undefined || value === '') return null;
  
  // Si el valor ya es un número, devolverlo directamente
  if (typeof value === 'number') return value;
  
  // Si es una cadena, intentar convertirla
  if (typeof value === 'string') {
    // Verificar si ya tiene el formato mm:ss
    if (value.includes(':')) {
      const parts = value.split(':');
      if (parts.length === 2) {
        const minutes = parseInt(parts[0], 10);
        const seconds = parseInt(parts[1], 10);
        if (!isNaN(minutes) && !isNaN(seconds)) {
          return minutes * 60 + seconds;
        }
      }
    }
    
    // Intentar convertir a número si es solo un valor
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      return numValue;
    }
  }
  
  // Si nada funciona, devolver null
  return null;
};

// Componente de edición de resultados
export const ResultEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput disabled source="id" />
      <ReferenceInput source="usuarioId" reference="users" label="Atleta">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="wodId" reference="wods" label="WOD">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <NumberInput source="puntuacion" label="Puntuación (reps o peso)" />
      <NumberInput 
        source="tiempo" 
        label="Tiempo" 
        format={formatTimeInput}
        parse={parseTimeInput}
        helperText="Formato: minutos:segundos (e.g. 20:00)"
      />
      <SelectInput source="estadoVerificacion" choices={[
        { id: 'pending', name: 'Pendiente' },
        { id: 'approved', name: 'Aprobado' },
        { id: 'rejected', name: 'Rechazado' },
      ]} />
      <TextInput source="videoEvidencia" label="URL del vídeo de evidencia" fullWidth />
      <TextInput source="comentarios" multiline rows={3} fullWidth />
    </SimpleForm>
  </Edit>
);

// Componente de creación de resultados
export const ResultCreate = () => (
  <Create>
    <SimpleForm>
      <ReferenceInput source="usuarioId" reference="users" label="Atleta">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="wodId" reference="wods" label="WOD">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <NumberInput source="puntuacion" label="Puntuación (reps o peso)" />
      <NumberInput 
        source="tiempo" 
        label="Tiempo" 
        format={formatTimeInput}
        parse={parseTimeInput}
        helperText="Formato: minutos:segundos (e.g. 20:00)"
      />
      <SelectInput source="estadoVerificacion" choices={[
        { id: 'pending', name: 'Pendiente' },
        { id: 'approved', name: 'Aprobado' },
        { id: 'rejected', name: 'Rechazado' },
      ]} defaultValue="pending" />
      <TextInput source="videoEvidencia" label="URL del vídeo de evidencia" fullWidth />
      <TextInput source="comentarios" multiline rows={3} fullWidth />
    </SimpleForm>
  </Create>
);
