import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Usuario } from '../../usuarios/entities/usuario.entity';
import { WOD } from '../../wods/entities/wod.entity';

@Entity('resultados')
export class Resultado {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Usuario)
  @JoinColumn({ name: 'usuario_id' })
  usuario: Usuario;

  @Column({ name: 'usuario_id' })
  usuarioId: string;

  @ManyToOne(() => WOD)
  @JoinColumn({ name: 'wod_id' })
  wod: WOD;

  @Column({ name: 'wod_id' })
  wodId: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  valor: number;

  @Column({ name: 'fecha_envio' })
  fechaEnvio: Date;

  @Column({ default: false })
  validado: boolean;

  @Column({ name: 'validado_por', nullable: true })
  validadoPor: string;

  @Column({ type: 'text', nullable: true })
  comentario: string;

  @Column({ default: 0 })
  flags: number;

  @Column({ name: 'flags_recibidos', default: 0 })
  flagsRecibidos: number;

  @Column({ default: 0 })
  modificaciones: number;

  @Column({ name: 'ultima_modificacion', nullable: true })
  ultimaModificacion: Date;

  @Column({ name: 'puntuacion_raw', type: 'decimal', precision: 10, scale: 2, default: 0 })
  puntuacionRaw: number;

  @Column({ name: 'tiempo_cap', default: false })
  tiempoCap: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
