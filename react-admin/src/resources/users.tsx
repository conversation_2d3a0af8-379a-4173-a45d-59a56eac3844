import { 
  List, 
  Datagrid, 
  TextField, 
  EmailField, 
  Edit, 
  SimpleForm, 
  TextInput,
  Create,
  SelectInput,
  NumberInput,
  ImageField,
  ImageInput,
  required,
  email,
  minLength,
  ReferenceInput,
  AutocompleteInput,
  TabbedForm,
  FormTab,
  TopToolbar,
  EditButton,
  ShowButton,
  Button,
  useRecordContext
} from 'react-admin';

// Importamos nuestro componente personalizado para cambiar roles
import ChangeRoleButton from '../components/buttons/ChangeRoleButton';

// Acciones personalizadas para cada fila del listado
const UserListActions = () => {
  const record = useRecordContext();
  
  if (!record) return null;
  
  return (
    <TopToolbar>
      <EditButton record={record} />
      <ChangeRoleButton />
    </TopToolbar>
  );
};

// Lista de usuarios
export const UserList = () => (
  <List>
    <Datagrid rowClick={false}>
      <TextField source="id" />
      <TextField source="nombre" />
      <EmailField source="email" />
      <TextField source="alias" />
      <TextField source="nivel" />
      <TextField source="genero" />
      <TextField source="rol" />
      <TextField source="estado" />
      <UserListActions />
    </Datagrid>
  </List>
);

// Componente de edición de usuarios
export const UserEdit = () => (
  <Edit>
    <TabbedForm>
      <FormTab label="Datos personales" icon={<span>👤</span>}>
        <TextInput disabled source="id" />
        <TextInput source="nombre" validate={[required()]} fullWidth />
        <TextInput source="email" type="email" validate={[required(), email()]} fullWidth />
        <TextInput source="alias" validate={[required()]} fullWidth />
        <NumberInput source="edad" />
        
        <ImageInput source="foto" label="Foto de perfil" accept={{
          'image/*': []
        }}>
          <ImageField source="src" title="title" />
        </ImageInput>
      </FormTab>
      
      <FormTab label="Categoría" icon={<span>🏆</span>}>
        {/* Campos de nivel y género con los valores correctos del enum */}
        <SelectInput source="nivel" choices={[
          { id: 'RX', name: 'RX' },
          { id: 'Intermedio', name: 'Intermedio' },
          { id: 'Scaled', name: 'Scaled' },
        ]} fullWidth />
        <SelectInput source="genero" choices={[
          { id: 'Masculino', name: 'Masculino' },
          { id: 'Femenino', name: 'Femenino' },
        ]} fullWidth />
      </FormTab>
      
      <FormTab label="Permisos y estado" icon={<span>🔐</span>}>
        <ReferenceInput source="boxId" reference="boxes">
          <AutocompleteInput optionText="nombre" fullWidth />
        </ReferenceInput>
        
        {/* Campo rol deshabilitado - usar el botón específico para cambiar roles */}
        <SelectInput source="rol" disabled 
          helperText="Para cambiar el rol, usa el botón 'Cambiar Rol' en el listado" 
          choices={[
            { id: 'Usuario', name: 'Usuario' },
            { id: 'BoxOwner', name: 'Dueño de Box' },
            { id: 'Admin', name: 'Administrador' },
        ]} fullWidth />
        
        {/* Estado del usuario */}
        <SelectInput source="estado" choices={[
          { id: 'Activo', name: 'Activo' },
          { id: 'Inactivo', name: 'Inactivo' },
          { id: 'Suspendido', name: 'Suspendido' },
        ]} fullWidth />
      </FormTab>
    </TabbedForm>
  </Edit>
);

// Componente de creación de usuarios
export const UserCreate = () => (
  <Create>
    <TabbedForm>
      <FormTab label="Datos personales" icon={<span>👤</span>}>
        <TextInput source="nombre" validate={[required()]} fullWidth />
        <TextInput source="email" type="email" validate={[required(), email()]} fullWidth />
        <TextInput source="alias" validate={[required()]} fullWidth />
        <TextInput 
          source="password" 
          type="password" 
          validate={[required(), minLength(6)]} 
          helperText="Mínimo 6 caracteres"
          fullWidth
        />
        <NumberInput source="edad" />
        
        <ImageInput source="foto" label="Foto de perfil" accept={{
          'image/*': []
        }}>
          <ImageField source="src" title="title" />
        </ImageInput>
      </FormTab>
      
      <FormTab label="Categoría" icon={<span>🏆</span>}>
        {/* Campos de nivel y género con los valores correctos del enum */}
        <SelectInput source="nivel" choices={[
          { id: 'RX', name: 'RX' },
          { id: 'Intermedio', name: 'Intermedio' },
          { id: 'Scaled', name: 'Scaled' },
        ]} fullWidth />
        <SelectInput source="genero" choices={[
          { id: 'Masculino', name: 'Masculino' },
          { id: 'Femenino', name: 'Femenino' },
        ]} fullWidth />
      </FormTab>
      
      <FormTab label="Permisos y estado" icon={<span>🔐</span>}>
        <ReferenceInput source="boxId" reference="boxes">
          <AutocompleteInput optionText="nombre" fullWidth />
        </ReferenceInput>
        
        {/* Rol con los valores correctos del enum */}
        <SelectInput source="rol" choices={[
          { id: 'Usuario', name: 'Usuario' },
          { id: 'BoxOwner', name: 'Dueño de Box' },
          { id: 'Admin', name: 'Administrador' },
        ]} defaultValue="Usuario" fullWidth />
        
        {/* Estado del usuario */}
        <SelectInput source="estado" choices={[
          { id: 'Activo', name: 'Activo' },
          { id: 'Inactivo', name: 'Inactivo' },
          { id: 'Suspendido', name: 'Suspendido' },
        ]} defaultValue="Activo" fullWidth />
      </FormTab>
    </TabbedForm>
  </Create>
);
