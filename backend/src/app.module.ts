import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-store';
import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';

// Configuraciones
import appConfig from './config/app.config.js';
import databaseConfig from './config/database.config.js';
import redisConfig from './config/redis.config.js';
import jwtConfig from './config/jwt.config.js';
import s3Config from './config/s3.config.js';

// Módulos
import { UsuariosModule } from './modules/usuarios/usuarios.module.js';
import { BoxesModule } from './modules/boxes/boxes.module.js';
import { AuthModule } from './modules/auth/auth.module.js';
import { LigasModule } from './modules/ligas/ligas.module.js';
import { WodsModule } from './modules/wods/wods.module.js';
import { MovimientosModule } from './modules/movimientos/movimientos.module.js';
import { ResultadosModule } from './modules/resultados/resultados.module.js';
import { ClasificacionesModule } from './modules/clasificaciones/clasificaciones.module.js';
import { InscripcionesModule } from './modules/inscripciones/inscripciones.module.js';
import { WebsocketsModule } from './websockets/websockets.module.js';
import { JobsModule } from './jobs/jobs.module.js';


@Module({
  imports: [
    // Configuración global
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, databaseConfig, redisConfig, jwtConfig, s3Config],
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
    }),

    // Base de datos PostgreSQL
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get('database'),
      }),
    }),

    // Redis para caché
    CacheModule.registerAsync({
      isGlobal: true,
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const redisConfig = configService.get('redis');
        return {
          store: redisStore,
          host: redisConfig.host,
          port: redisConfig.port,
          password: redisConfig.password,
          ttl: redisConfig.ttl,
        };
      },
    }),

    // Módulos de la aplicación
    UsuariosModule,
    BoxesModule,
    AuthModule,
    LigasModule,
    WodsModule,
    MovimientosModule,
    ResultadosModule,
    ClasificacionesModule,
    InscripcionesModule,
    WebsocketsModule,
    JobsModule,

  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
