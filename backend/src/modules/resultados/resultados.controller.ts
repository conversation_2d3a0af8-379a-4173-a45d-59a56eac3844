import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ResultadosService } from './resultados.service';
import { CreateResultadoDto } from './dto/create-resultado.dto';
import { UpdateResultadoDto } from './dto/update-resultado.dto';
import { FlagResultadoDto } from './dto/flag-resultado.dto';
import { Resultado } from './entities/resultado.entity';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RolUsuario } from '../usuarios/entities/usuario.entity';

@Controller('resultados')
export class ResultadosController {
  constructor(private readonly resultadosService: ResultadosService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createResultadoDto: CreateResultadoDto): Promise<Resultado> {
    return this.resultadosService.create(createResultadoDto);
  }

  @Get()
  findAll(): Promise<Resultado[]> {
    return this.resultadosService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Resultado> {
    return this.resultadosService.findOne(id);
  }

  @Get('usuario/:usuarioId')
  findByUsuario(@Param('usuarioId') usuarioId: string): Promise<Resultado[]> {
    return this.resultadosService.findByUsuario(usuarioId);
  }

  @Get('wod/:wodId')
  findByWod(@Param('wodId') wodId: string): Promise<Resultado[]> {
    return this.resultadosService.findByWod(wodId);
  }

  @Get('usuario/:usuarioId/wod/:wodId')
  findByUsuarioAndWod(
    @Param('usuarioId') usuarioId: string,
    @Param('wodId') wodId: string,
  ): Promise<Resultado> {
    return this.resultadosService.findByUsuarioAndWod(usuarioId, wodId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  update(@Param('id') id: string, @Body() updateResultadoDto: UpdateResultadoDto): Promise<Resultado> {
    return this.resultadosService.update(id, updateResultadoDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  remove(@Param('id') id: string): Promise<void> {
    return this.resultadosService.remove(id);
  }

  @Post(':id/validar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN, RolUsuario.BOX_OWNER)
  validarResultado(@Param('id') id: string, @Request() req): Promise<Resultado> {
    return this.resultadosService.validarResultado(id, req.user.id);
  }

  @Post(':id/flag')
  @UseGuards(JwtAuthGuard)
  flagResultado(@Param('id') id: string, @Body() flagDto: FlagResultadoDto): Promise<Resultado> {
    return this.resultadosService.flagResultado(id, flagDto);
  }

  @Post(':id/calcular-puntuacion')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  calcularPuntuacion(@Param('id') id: string): Promise<Resultado> {
    return this.resultadosService.calcularPuntuacion(id);
  }

  @Post('detectar-outliers')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  detectarOutliers(): Promise<Resultado[]> {
    return this.resultadosService.detectarOutliers();
  }
}
