import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Liga } from '../../ligas/entities/liga.entity';

export enum TipoWOD {
  AMRAP = 'AMRAP',
  FORTIME = 'FORTIME',
  EMOM = 'EMOM',
  MAXWEIGHT = 'MAXWEIGHT',
  MAXREPS = 'MAXREPS',
}

export enum EstadoWOD {
  BORRADOR = 'Borrador',
  PUBLICADO = 'Publicado',
  CERRADO = 'Cerrado',
}

@Entity('wods')
export class WOD {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Liga)
  @JoinColumn({ name: 'liga_id' })
  liga: Liga;

  @Column({ name: 'liga_id' })
  ligaId: string;

  @Column({ name: 'tipo_evento_id', nullable: true })
  tipoEventoId: string;

  @Column({ length: 100 })
  titulo: string;

  @Column({ type: 'text' })
  descripcion: string;

  @Column({
    type: 'enum',
    enum: TipoWOD,
  })
  tipo: TipoWOD;

  @Column({ name: 'fecha_publicacion' })
  fechaPublicacion: Date;

  @Column({ name: 'fecha_limite' })
  fechaLimite: Date;

  @Column({ name: 'es_bonus', default: false })
  esBonus: boolean;

  @Column()
  semana: number;

  @Column({
    type: 'enum',
    enum: EstadoWOD,
    default: EstadoWOD.BORRADOR,
  })
  estado: EstadoWOD;

  @Column({ nullable: true })
  imagen: string;

  @Column({ type: 'jsonb', default: '[]' })
  movimientos: any;

  @Column({ name: 'resultado_limite', nullable: true })
  resultadoLimite: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
