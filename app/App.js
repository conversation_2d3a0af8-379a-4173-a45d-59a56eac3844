import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import SplashScreen from './screens/SplashScreen';
import OnboardingScreen from './screens/OnboardingScreen';
import SetupScreen from './screens/SetupScreen';
import EmailVerificationScreen from './screens/EmailVerificationScreen';
import ResetPasswordScreen from './screens/ResetPasswordScreen';
import { AuthProvider, useAuth } from './context/AuthContextNest';
import MainNavigator from './navigation/AppNavigator';
import { UserDataProvider } from './context/UserDataContext';
import { UserProvider } from './context/UserContext';
import { toastConfig } from './config/toastConfig';

const Stack = createStackNavigator();

/**
 * Authentication Stack for non-authenticated users
 * Contains Login and Register screens
 */
/**
 * Authentication Stack for non-authenticated users
 * Contains Login, Register, Email Verification and Password Reset screens
 */
const AuthStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false
    }}
  >
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Register" component={RegisterScreen} />
    <Stack.Screen name="EmailVerification" component={EmailVerificationScreen} />
    <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
  </Stack.Navigator>
);

/**
 * Root Stack that contains Splash Screen and conditional navigation
 * based on authentication status
 */
const RootStack = () => {
  const { user, loading } = useAuth();
  const [isFirstLaunch, setIsFirstLaunch] = useState(null);

  useEffect(() => {
    // Check if onboarding has been completed before
    const checkOnboardingStatus = async () => {
      try {
        console.log('Verificando estado de onboarding...');
        
        const onboardingValue = await AsyncStorage.getItem('onboardingCompleted');
        if (onboardingValue === null || onboardingValue !== 'true') {
          // First time launching the app
          console.log('Primera vez iniciando la app, mostrando onboarding');
          setIsFirstLaunch(true);
        } else {
          // Not first launch
          console.log('No es la primera vez, omitiendo onboarding');
          setIsFirstLaunch(false);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding if there's an error
        setIsFirstLaunch(true);
      }
    };

    checkOnboardingStatus();
  }, []);

  // If still loading auth state or checking first launch, show nothing
  if (loading || isFirstLaunch === null) {
    return null;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false
      }}
      initialRouteName={isFirstLaunch ? "Onboarding" : "Splash"}
    >
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      <Stack.Screen name="Setup" component={SetupScreen} />
      <Stack.Screen name="Main" component={MainNavigator} />
      <Stack.Screen name="Auth" component={AuthStack} />
    </Stack.Navigator>
  );
};

/**
 * Main App component
 * Wraps the entire application with necessary providers
 * Integra los proveedores de autenticación y datos de usuario
 */
const App = () => {
  return (
    <AuthProvider>
      <UserProvider>
        <UserDataProvider>
          <NavigationContainer>
            <RootStack />
          </NavigationContainer>
          <Toast config={toastConfig} />
        </UserDataProvider>
      </UserProvider>
    </AuthProvider>
  );
};

export default App;
