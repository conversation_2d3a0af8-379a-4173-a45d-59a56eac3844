// Interfaces para la API
export interface ApiMovimiento {
  id: string;
  nombre: string;
  descripcion?: string;

  // Variantes RX
  rxMasc?: string;
  rxFem?: string;

  // Variantes INT
  intMasc?: string;
  intFem?: string;

  // Variantes SC
  scMasc?: string;
  scFem?: string;

  // Índice para permitir acceso dinámico a las propiedades
  [key: string]: string | number | boolean | undefined;
}

// Estructura para el movimiento seleccionado que se está configurando
export interface MovimientoSeleccionado {
  id: string;
  nombre: string;
  movimiento: ApiMovimiento;
  categorias: {
    rx: boolean;
    int: boolean;
    sc: boolean;
  };
  generos: {
    masculino: boolean;
    femenino: boolean;
  };
  repeticiones: {
    rx?: {
      masculino?: string | number;
      femenino?: string | number;
    };
    int?: {
      masculino?: string | number;
      femenino?: string | number;
    };
    sc?: {
      masculino?: string | number;
      femenino?: string | number;
    };
  };
  pesos: {
    rx?: {
      masculino?: number;
      femenino?: number;
    };
    int?: {
      masculino?: number;
      femenino?: number;
    };
    sc?: {
      masculino?: number;
      femenino?: number;
    };
  };
}

// Estructura para los detalles de un movimiento (variante específica)
export interface DetalleMovimiento {
  nombre: string;
  reps: string | number; // Puede ser un número o "max"
  peso?: number;
  unidad?: string;
}

// Estructura para un movimiento en el WOD con todas sus variantes
export interface MovimientoWod {
  nombre: string;
  reps?: number;
  peso?: number;
  unidad?: string;
  categorias?: {
    rx?: {
      masculino?: DetalleMovimiento;
      femenino?: DetalleMovimiento;
    };
    int?: {
      masculino?: DetalleMovimiento;
      femenino?: DetalleMovimiento;
    };
    sc?: {
      masculino?: DetalleMovimiento;
      femenino?: DetalleMovimiento;
    };
  };
}
