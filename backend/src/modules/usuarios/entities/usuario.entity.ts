import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Box } from '../../boxes/entities/box.entity';

export enum NivelUsuario {
  RX = 'RX',
  INTERMEDIO = 'Intermedio',
  SCALED = 'Scaled',
}

export enum GeneroUsuario {
  MASCULINO = 'Masculino',
  FEMENINO = 'Femenino',
}

export enum RolUsuario {
  USUARIO = 'Usuario',
  BOX_OWNER = 'BoxOwner',
  ADMIN = 'Admin',
}

export enum EstadoUsuario {
  ACTIVO = 'Activo',
  INACTIVO = 'Inactivo',
  SUSPENDIDO = 'Suspendido',
}

@Entity('usuarios')
export class Usuario {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  nombre: string;

  @Column({ length: 100 })
  alias: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  foto: string;

  @Column({
    type: 'enum',
    enum: NivelUsuario,
    nullable: true,
    // Removed default value to make it initially null
  })
  nivel: NivelUsuario | null;

  @Column({
    type: 'enum',
    enum: GeneroUsuario,
    nullable: true,
  })
  genero: GeneroUsuario | null;

  @Column({
    type: 'int',
    nullable: true,
  })
  edad: number | null;

  @ManyToOne(() => Box, box => box.miembros, { nullable: true })
  @JoinColumn({ name: 'box_id' })
  box: Box;

  @Column({ nullable: true })
  box_id: string;

  @CreateDateColumn({ name: 'fecha_registro' })
  fechaRegistro: Date;

  @Column({ nullable: true, name: 'ultima_conexion' })
  ultimaConexion: Date;

  @Column({ default: 20, name: 'puntos_pvp' })
  puntosPvp: number;

  @Column({
    type: 'enum',
    enum: RolUsuario,
    default: RolUsuario.USUARIO,
  })
  rol: RolUsuario;

  @Column({
    type: 'enum',
    enum: EstadoUsuario,
    default: EstadoUsuario.ACTIVO,
  })
  estado: EstadoUsuario;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ default: false, name: 'email_verificado' })
  emailVerificado: boolean;

  @Column({ nullable: true, name: 'token_verificacion' })
  tokenVerificacion: string;

  @Column({ nullable: true, name: 'token_verificacion_expira' })
  tokenVerificacionExpira: Date;
  
  @Column({ default: false, name: 'setup_completed' })
  setupCompleted: boolean;
  
  @Column({ default: false, name: 'on_boarding' })
  onBoarding: boolean;
  
  @Column({ nullable: true, name: 'token_reset_password' })
  tokenResetPassword: string;
  
  @Column({ nullable: true, name: 'token_reset_password_expira' })
  tokenResetPasswordExpira: Date;
}
