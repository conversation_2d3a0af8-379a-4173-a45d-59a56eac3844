import React, { useState, useEffect } from 'react';
import { useInput } from 'react-admin';
import { TextField, Grid, Typography, InputAdornment } from '@mui/material';

interface TimeInputProps {
  source: string;
  label?: string;
  helperText?: string;
  fullWidth?: boolean;
  disabled?: boolean;
  required?: boolean;
}

/**
 * Componente personalizado para introducir tiempo en formato minutos:segundos
 * Permite al usuario introducir minutos y segundos en campos separados
 */
export const TimeInput = (props: TimeInputProps) => {
  const { source, label, helperText, fullWidth = false, disabled = false, required = false } = props;
  
  // Usamos el hook useInput de react-admin para manejar el valor
  const {
    field,
    fieldState: { error },
    formState: { isSubmitted },
    isRequired
  } = useInput({
    source,
    validate: required ? [(value) => (value ? undefined : 'Campo requerido')] : undefined,
  });

  // Extraer minutos y segundos del valor (que está en segundos totales)
  const [minutes, setMinutes] = useState<string>('');
  const [seconds, setSeconds] = useState<string>('');

  // Actualizar minutos y segundos cuando cambia el valor
  useEffect(() => {
    if (field.value !== undefined && field.value !== null) {
      const totalSeconds = Number(field.value);
      const mins = Math.floor(totalSeconds / 60);
      const secs = totalSeconds % 60;
      
      setMinutes(mins.toString());
      setSeconds(secs < 10 ? `0${secs}` : secs.toString());
    }
  }, [field.value]);

  // Actualizar el valor cuando cambian minutos o segundos
  const updateValue = (newMinutes: string, newSeconds: string) => {
    const mins = newMinutes === '' ? 0 : parseInt(newMinutes, 10);
    const secs = newSeconds === '' ? 0 : parseInt(newSeconds, 10);
    
    if (!isNaN(mins) && !isNaN(secs)) {
      const totalSeconds = mins * 60 + secs;
      field.onChange(totalSeconds);
    }
  };

  // Manejadores de cambios
  const handleMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMinutes = e.target.value;
    if (newMinutes === '' || /^\d+$/.test(newMinutes)) {
      setMinutes(newMinutes);
      updateValue(newMinutes, seconds);
    }
  };

  const handleSecondsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSeconds = e.target.value;
    if (newSeconds === '' || (/^\d+$/.test(newSeconds) && parseInt(newSeconds, 10) < 60)) {
      setSeconds(newSeconds);
      updateValue(minutes, newSeconds);
    }
  };

  return (
    <div style={{ marginBottom: 16 }}>
      <Typography variant="body2" color="textSecondary" gutterBottom>
        {label || source}
        {isRequired && ' *'}
      </Typography>
      <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
        <TextField
          label="Minutos"
          value={minutes}
          onChange={handleMinutesChange}
          variant="outlined"
          size="small"
          type="text"
          disabled={disabled}
          InputProps={{
            endAdornment: <InputAdornment position="end">min</InputAdornment>,
          }}
          error={!!error}
          style={{ width: '120px' }}
        />
        <TextField
          label="Segundos"
          value={seconds}
          onChange={handleSecondsChange}
          variant="outlined"
          size="small"
          type="text"
          disabled={disabled}
          InputProps={{
            endAdornment: <InputAdornment position="end">seg</InputAdornment>,
          }}
          error={!!error}
          style={{ width: '120px' }}
        />
      </div>
      {(error || helperText) && (
        <Typography variant="caption" color={error ? "error" : "textSecondary"} style={{ marginTop: 4, display: 'block' }}>
          {error?.message || helperText}
        </Typography>
      )}
    </div>
  );
};
