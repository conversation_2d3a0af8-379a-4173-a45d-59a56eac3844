import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Resultado } from './entities/resultado.entity';
import { CreateResultadoDto } from './dto/create-resultado.dto';
import { UpdateResultadoDto } from './dto/update-resultado.dto';
import { FlagResultadoDto } from './dto/flag-resultado.dto';
import { WodsService } from '../wods/wods.service';
import { UsuariosService } from '../usuarios/usuarios.service';
import { TipoWOD, EstadoWOD } from '../wods/entities/wod.entity';

@Injectable()
export class ResultadosService {
  constructor(
    @InjectRepository(Resultado)
    private resultadosRepository: Repository<Resultado>,
    private wodsService: WodsService,
    private usuariosService: UsuariosService,
  ) {}

  async create(createResultadoDto: CreateResultadoDto): Promise<Resultado> {
    // Verificar que el WOD existe y está publicado
    const wod = await this.wodsService.findOne(createResultadoDto.wodId);

    if (wod.estado !== EstadoWOD.PUBLICADO) {
      throw new ConflictException('No se pueden registrar resultados para un WOD que no está publicado');
    }

    // Verificar que la fecha límite no ha pasado
    const now = new Date();
    if (wod.fechaLimite < now) {
      throw new ConflictException('La fecha límite para registrar resultados ha pasado');
    }

    // Verificar que el usuario existe
    await this.usuariosService.findOne(createResultadoDto.usuarioId);

    // Verificar si ya existe un resultado para este usuario y WOD
    const existingResultado = await this.resultadosRepository.findOne({
      where: {
        usuarioId: createResultadoDto.usuarioId,
        wodId: createResultadoDto.wodId,
      },
    });

    if (existingResultado) {
      // Si ya existe, actualizar el resultado
      existingResultado.valor = createResultadoDto.valor;
      existingResultado.comentario = createResultadoDto.comentario || existingResultado.comentario;
      existingResultado.tiempoCap = createResultadoDto.tiempoCap || existingResultado.tiempoCap;
      existingResultado.ultimaModificacion = new Date();
      existingResultado.modificaciones += 1;

      return this.resultadosRepository.save(existingResultado);
    }

    // Validar el valor según el tipo de WOD
    this.validarValorSegunTipoWOD(createResultadoDto.valor, wod.tipo, wod.resultadoLimite);

    // Crear el nuevo resultado
    const newResultado = this.resultadosRepository.create({
      ...createResultadoDto,
      fechaEnvio: new Date(),
    });

    return this.resultadosRepository.save(newResultado);
  }

  async findAll(): Promise<Resultado[]> {
    return this.resultadosRepository.find({
      relations: ['usuario', 'wod'],
    });
  }

  async findOne(id: string): Promise<Resultado> {
    const resultado = await this.resultadosRepository.findOne({
      where: { id },
      relations: ['usuario', 'wod'],
    });

    if (!resultado) {
      throw new NotFoundException(`Resultado con ID ${id} no encontrado`);
    }

    return resultado;
  }

  async update(id: string, updateResultadoDto: UpdateResultadoDto): Promise<Resultado> {
    const resultado = await this.findOne(id);

    // Si se está actualizando el valor, validar según el tipo de WOD
    if (updateResultadoDto.valor !== undefined) {
      const wod = await this.wodsService.findOne(resultado.wodId);
      this.validarValorSegunTipoWOD(updateResultadoDto.valor, wod.tipo, wod.resultadoLimite);

      // Actualizar la fecha de última modificación y el contador
      resultado.ultimaModificacion = new Date();
      resultado.modificaciones += 1;
    }

    // Actualizar el resultado
    const updatedResultado = this.resultadosRepository.merge(resultado, updateResultadoDto);
    return this.resultadosRepository.save(updatedResultado);
  }

  async remove(id: string): Promise<void> {
    const result = await this.resultadosRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Resultado con ID ${id} no encontrado`);
    }
  }

  async findByUsuario(usuarioId: string): Promise<Resultado[]> {
    return this.resultadosRepository.find({
      where: { usuarioId },
      relations: ['wod'],
    });
  }

  async findByWod(wodId: string): Promise<Resultado[]> {
    return this.resultadosRepository.find({
      where: { wodId },
      relations: ['usuario'],
    });
  }

  async findByUsuarioAndWod(usuarioId: string, wodId: string): Promise<Resultado> {
    const resultado = await this.resultadosRepository.findOne({
      where: { usuarioId, wodId },
      relations: ['usuario', 'wod'],
    });

    if (!resultado) {
      throw new NotFoundException(`Resultado no encontrado para el usuario ${usuarioId} y WOD ${wodId}`);
    }

    return resultado;
  }

  async validarResultado(id: string, validadoPor: string): Promise<Resultado> {
    const resultado = await this.findOne(id);

    resultado.validado = true;
    resultado.validadoPor = validadoPor;

    return this.resultadosRepository.save(resultado);
  }

  async flagResultado(id: string, flagDto: FlagResultadoDto): Promise<Resultado> {
    const resultado = await this.findOne(id);

    // Incrementar el contador de flags
    resultado.flagsRecibidos += 1;

    // Guardar el comentario del flag
    if (!resultado.comentario) {
      resultado.comentario = `Flag por ${flagDto.usuarioId}: ${flagDto.razon}`;
    } else {
      resultado.comentario += `\nFlag por ${flagDto.usuarioId}: ${flagDto.razon}`;
    }

    return this.resultadosRepository.save(resultado);
  }

  async calcularPuntuacion(id: string): Promise<Resultado> {
    const resultado = await this.findOne(id);
    const wod = await this.wodsService.findOne(resultado.wodId);

    // Obtener el mejor resultado para este WOD
    const mejorResultado = await this.obtenerMejorResultadoPorWod(wod.id, wod.tipo);

    // Calcular la puntuación según el tipo de WOD
    let puntuacion = 0;

    switch (wod.tipo) {
      case TipoWOD.FORTIME:
        // Menor tiempo es mejor
        puntuacion = 100 * (mejorResultado.valor / resultado.valor);
        break;
      case TipoWOD.AMRAP:
      case TipoWOD.MAXREPS:
        // Mayor número de repeticiones es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      case TipoWOD.MAXWEIGHT:
        // Mayor peso es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      case TipoWOD.EMOM:
        // Mayor número de minutos completados es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      default:
        throw new Error(`Tipo de WOD no soportado: ${wod.tipo}`);
    }

    // Redondear a 2 decimales
    puntuacion = Math.round(puntuacion * 100) / 100;

    // Actualizar la puntuación
    resultado.puntuacionRaw = puntuacion;

    return this.resultadosRepository.save(resultado);
  }

  private async obtenerMejorResultadoPorWod(wodId: string, tipoWOD: TipoWOD): Promise<Resultado> {
    const resultados = await this.resultadosRepository.find({
      where: { wodId, validado: true },
    });

    if (resultados.length === 0) {
      throw new BadRequestException('No hay resultados validados para este WOD');
    }

    // Ordenar según el tipo de WOD
    if (tipoWOD === TipoWOD.FORTIME) {
      // Menor tiempo es mejor
      resultados.sort((a, b) => a.valor - b.valor);
    } else {
      // Mayor valor es mejor para los demás tipos
      resultados.sort((a, b) => b.valor - a.valor);
    }

    return resultados[0];
  }

  private validarValorSegunTipoWOD(valor: number, tipoWOD: TipoWOD, resultadoLimite?: string): void {
    switch (tipoWOD) {
      case TipoWOD.FORTIME:
        // Validar que el tiempo es positivo
        if (valor <= 0) {
          throw new BadRequestException('El tiempo debe ser positivo');
        }

        // Validar contra el tiempo límite si existe
        if (resultadoLimite) {
          const limiteSecs = this.convertirTiempoASegundos(resultadoLimite);
          if (valor > limiteSecs) {
            throw new BadRequestException(`El tiempo excede el límite de ${resultadoLimite}`);
          }
        }
        break;

      case TipoWOD.AMRAP:
      case TipoWOD.MAXREPS:
      case TipoWOD.MAXWEIGHT:
      case TipoWOD.EMOM:
        // Validar que el valor es positivo
        if (valor < 0) {
          throw new BadRequestException('El valor debe ser positivo o cero');
        }
        break;

      default:
        throw new BadRequestException(`Tipo de WOD no soportado: ${tipoWOD}`);
    }
  }

  private convertirTiempoASegundos(tiempo: string): number {
    // Formato esperado: "MM:SS" o "HH:MM:SS"
    const partes = tiempo.split(':').map(Number);

    if (partes.length === 2) {
      // MM:SS
      return partes[0] * 60 + partes[1];
    } else if (partes.length === 3) {
      // HH:MM:SS
      return partes[0] * 3600 + partes[1] * 60 + partes[2];
    } else {
      throw new BadRequestException('Formato de tiempo inválido. Use MM:SS o HH:MM:SS');
    }
  }

  async detectarOutliers(): Promise<Resultado[]> {
    const outliers: Resultado[] = [];

    // Obtener todos los WODs
    const wods = await this.wodsService.findAll();

    for (const wod of wods) {
      // Obtener resultados validados para este WOD
      const resultados = await this.resultadosRepository.find({
        where: { wodId: wod.id, validado: true },
      });

      if (resultados.length < 5) {
        // No hay suficientes resultados para detectar outliers
        continue;
      }

      // Calcular media y desviación estándar
      const valores = resultados.map(r => r.valor);
      const media = valores.reduce((a, b) => a + b, 0) / valores.length;
      const desviacion = Math.sqrt(
        valores.map(x => Math.pow(x - media, 2)).reduce((a, b) => a + b, 0) / valores.length
      );

      // Detectar outliers (valores fuera de 3 desviaciones estándar)
      for (const resultado of resultados) {
        const zScore = Math.abs((resultado.valor - media) / desviacion);

        if (zScore > 3) {
          // Es un outlier
          resultado.flags += 1;
          outliers.push(resultado);

          await this.resultadosRepository.save(resultado);
        }
      }
    }

    return outliers;
  }
}
