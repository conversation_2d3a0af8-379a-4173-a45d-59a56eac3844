# **INFORME DETALLADO: DESARROLLO DE THE WOD LEAGUE**

## **RESUMEN EJECUTIVO**

Este informe detalla el plan de desarrollo para The WOD League, una aplicación para competiciones remotas de CrossFit. Basado en un estudio de mercado con 58 encuestados y análisis de tendencias actuales, el proyecto propone un ecosistema digital que combina ligas virtuales regulares con eventos especiales y batallas PVP, aprovechando el creciente interés en competiciones remotas y la gamificación del fitness.

> **NOTA IMPORTANTE**: Este documento ha sido reorganizado para reflejar las prioridades de implementación:
> - **Implementación Actual**: Sistema de Ligas Principal, Clasificaciones en Tiempo Real, Perfiles de Usuario, Validación de Resultados, Integración con Redes Sociales y Modo PVP.
> - **Futuras Releases**: Battle Box entre Boxes y Eventos Especiales Temáticos.

## **ANÁLISIS DE MERCADO**

### **Tendencias Identificadas:**

* Crecimiento significativo del mercado de aplicaciones fitness post-pandemia  
* Aumento en la demanda de experiencias híbridas (virtual \+ presencial)  
* Mayor adopción de tecnología en entrenamiento deportivo  
* Interés creciente en competiciones virtuales/remotas  
* Tendencia hacia la gamificación y socialización del fitness

### **Insights de la Encuesta:**

* 72.4% de encuestados son usuarios activos de CrossFit  
* 70.7% prefiere competiciones por equipos  
* 69% participaría en competiciones aunque llegue tarde  
* Base demográfica mayoritariamente joven (62.1% entre 25-34 años)  
* Instagram es el canal dominante (74.1% lo utiliza)  
* 51.7% pagaría entre 20€-30€ por participar en una liga

## **ESTRUCTURA DE LA APLICACIÓN**

## **IMPLEMENTACIÓN ACTUAL**

### **Funcionalidades Core:**

#### **1\. Sistema de Ligas Principal**

* Duración: 6 semanas por liga
* Formato: 1 WOD principal obligatorio
* Categorías: Separación por género (M/F) y nivel (RX/Intermedio/Scaled) 
* Sistema de puntuación:  
  * Primer puesto: 100 puntos  
  * Resto: Puntuación proporcional

#### **1.1\. Cálculo de Puntos por Tipo de WOD**

* **Principios Generales:**  
  * El primer clasificado recibe siempre 100 puntos  
  * Resto de participantes: puntuación proporcional al mejor resultado

* **Fórmulas de Cálculo Según Tipo de WOD:**  
  * **WODs tipo "For Time":**  
    * Puntos = 100 × (Mejor tiempo ÷ Tu tiempo)  
    * Ejemplo: Si el mejor tiempo es 10:00 y un atleta hace 12:30, recibe 80 puntos  
  * **WODs tipo "AMRAP" (repeticiones):**  
    * Puntos = 100 × (Tus repeticiones ÷ Mejores repeticiones)  
    * Ejemplo: Si el mejor resultado es 200 reps y un atleta hace 160, recibe 80 puntos  
  * **WODs tipo "Peso" (1RM o cargas máximas):**  
    * Puntos = 100 × (Tu peso ÷ Mejor peso)  
    * Ejemplo: Si el mejor peso es 120kg y un atleta levanta 102kg, recibe 85 puntos  
  * **WODs tipo "EMOM" o por rondas completadas:**  
    * Puntos = 100 × (Tus rondas/minutos completados ÷ Máximas rondas/minutos)  
    * Ejemplo: En un EMOM de 20 minutos, completar 17 minutos otorga 85 puntos  
  * **WODs Combinados:**  
    * Se calcula por separado cada segmento y se promedian los resultados  
    * Ejemplo: (Puntos Parte 1 + Puntos Parte 2 + Puntos Parte 3) ÷ 3

* **Consideraciones Técnicas:**  
  * **Procesamiento Eficiente:**  
    * Cálculos realizados al cierre de plazo de envío de resultados  
    * Optimización para grandes volúmenes de participantes (1000+)  
    * Actualización programada del leaderboard para evitar sobrecarga  
  * **Gestión de Casos Especiales:**  
    * DNF (Did Not Finish): 0 puntos para ese WOD específico  
    * Empates exactos: misma puntuación para los participantes implicados  
    * Cap time: se considera el tiempo máximo permitido para el cálculo  
  * **Transparencia y Verificación:**  
    * Los cálculos de puntuación son visibles para todos los usuarios  
    * Sistema de detección de outliers estadísticos para valores sospechosos  
    * Sistema de flags comunitarios como medida adicional de integridad

#### **2\. Clasificaciones en Tiempo Real**

* **Actualización instantánea:**  
  * Posición en ranking general (debe verse en el card de la home la posición pero no el resultado)  
  * Puntos acumulados  
  * Distancia con competidores cercanos (opcional)  
  * Estado de validación  
  * Notificaciones de cambio de posición  
* **Durante la semana activa:**  
  * Ranking específico del WOD actual  
  * Tiempo restante para enviar resultados  
* **Actualización periódica:**  
  * Datos históricos y estadísticas  

#### **3\. Perfiles de Usuario**
  * nombre: string
  * alias: string
  * edad: int "nullable durante registro"
  * email: string
  * password (hash): string
  * foto: string "path/URL"
  * nivel: enum {RX, Intermedio, Scaled} "nullable durante registro"
  * genero: enum {Masculino, Femenino} "nullable durante registro"
  * box_id: int "opcional"
  * fecha_registro: date
  * ultima_conexion: date
  * puntos_pvp: int = 20
  * rol: enum {Usuario, BoxOwner, Admin}
  * estado: enum {Activo, Inactivo, Suspendido}
  * emailVerificado: boolean = false
  * tokenVerificacion: string "token para verificación de email"
  * tokenVerificacionExpira: date "fecha expiración del token"
  * setupCompleted: boolean = false "completó configuración inicial"
  * onBoarding: boolean = false "completó proceso de onboarding"

#### **4\. Sistema de Verificación de Resultados**

* **Basado en honestidad:**  
  * Confirmación mediante checkbox  
  * Detección automática de outliers estadísticos     
* **Medidas anti-fraude:**  
  * Validación del nivel durante inscripción  
  * Avisos para resultados fuera de rangos esperados  

#### **5\. Integración con Redes Sociales**

* Enfoque en Instagram (74.1% de usuarios)  
* Plantillas para compartir resultados  
* Botones de sharing (Historia, Feed, DM)  
* Momentos clave para compartir (completar WOD, mejora de posición, etc.)

### **Funcionalidades Complementarias:**

#### **1\. Modo PVP entre Usuarios**

* **Desafío Directo:**  
  * Usuario vs Usuario con WOD específico  
  * Simplemente ganas 1 punto si ganas o pierdes 1 punto si pierdes de forma acumulativa 
  * -- Usuario A → Usuario B
  * \- WOD específico  
  * \- Tiempo límite: 24-48h  
  * \- Mismo nivel requerido
    
  PARTICIPACIÓN:  
  * Para unirse necesitas:   
  * \- Mismo nivel (RX/Intermedio/Scaled)  
    
  ESTRUCTURA:  
  * Configuración:  
  * \- Plazo: 7 días como máximo. Cuando se llena, al dia siguiente (notificación el dia antes)  
  * \- Si alguien no rellena su puntuación al acabar los 7 días, pierde  

  MECÁNICA:  
  * \- Los resultados se mantienen ocultos  
  * \- Se revelan al final del plazo  
  * \- Sistema de validación por honestidad (como liga)  
  * \- Outliers automáticos (como liga)  
  * \- No hay opción de reclamar/disputar  
     
## **FUTURAS RELEASES**

### **Battle Box entre Boxes**

#### Estructura:
* **Duración**: Desafíos de box contra box
* **Formato**: WODs especiales con participación grupal
* **Periodicidad**: A demanda de los boxes
* **Categorías**: Por nivel de box y tamaño

#### Características:
* **Desafío directo**: Un box desafía a otro específicamente
* **Participación mínima**: Porcentaje requerido de miembros del box
* **Puntuaciones grupales**: Suma o promedio de resultados individuales
* **Tabla de clasificación de boxes**: Ranking entre boxes participantes

### **Eventos Especiales Temáticos**

#### Estructura**:**

* **Duración**: Eventos puntuales de 1-3 días  
* **Formato**: 1 WOD único por evento, con estándares específicos  
* **Periodicidad**: a convenir  
* **Categorías**: Abiertos a todos los niveles, con escalado por capacidad

### Características:

* **Independencia del sistema de ligas**: No afectan a las puntuaciones de ligas regulares  
* **Acceso**: Disponibles tanto para usuarios free como premium  
* **Temáticas variadas**:  
  * Eventos estacionales (Halloween, Navidad, etc.)  
  * Colaboraciones con atletas reconocidos  
  * Conmemoraciones de benchmarks clásicos  
  * Desafíos de habilidades específicas

### Sistema de recompensas:

* **Badges exclusivos**: Coleccionables digitales por participación  
* **Logros especiales**: Reconocimientos por rendimiento destacado  
* **Visibilidad en perfil**: Sección dedicada a mostrar badges/logros obtenidos  
* **Recompensas tangibles**: Posibilidad de premios físicos para top performers

### Beneficios estratégicos:

* **Retención entre ligas**: Mantiene el engagement durante periodos menos activos  
* **Gamificación adicional**: Incentiva la colección de badges únicos  
* **Comunidad**: Genera momentos de participación masiva simultánea  
* **Marketing**: Oportunidades para colaboraciones y patrocinios puntuales

### Validación de resultados:

* Sistema simplificado basado en honestidad  
* Sin sistema de flags para mantener el enfoque en la participación y diversión

## **ESTRATEGIA DE MARKETING**

### **Fases de Lanzamiento:**

#### **1\. Pre-lanzamiento (2-3 meses)**

* Creación de marca e identidad  
* Establecimiento en Instagram  
* Waitlist con early bird discount  
* Acercamiento a boxes para beta testing

#### **2\. Soft Launch (1 mes)**

* Testeo con grupo selecto de la waitlist  
* Recolección de feedback y mejoras  
* Desarrollo de casos de éxito iniciales

#### **3\. Lanzamiento Oficial**

* Primera liga con precios early bird  
* Activación de todas las funcionalidades  
* Marketing de contenidos (historias de usuarios, rankings)  
* Campaña en Instagram, colaboraciones con boxes

### **Métricas Clave:**

* Registros en waitlist y tasa de conversión  
* Engagement en redes sociales  
* Conversión de usuarios gratuitos a pagos  
* Retención por liga

## **MODELO DE NEGOCIO**

### **1\. Estructura Freemium:**

* **Funcionalidades gratuitas:**  
  * Registro y perfil básico  
  * 1 desafío PVP semanal  
  * Visualización limitada de rankings  
  * Sin acceso a estadísticas completas

### **2\. Monetización:**
PENDIENTE DE DEFINIR

## **NOTAS DE IMPLEMENTACIÓN**

* Las funcionalidades de Battle Box y Eventos Especiales se implementarán en una fase posterior
* Se priorizará el Sistema de Ligas y el modo PVP para la implementación inicial
* La estructura técnica seguirá una arquitectura modular que facilite la incorporación de nuevas funcionalidades
