import { IsOptional, IsString } from 'class-validator';

/**
 * DTO for handling profile photo updates
 * Contains metadata about the uploaded photo
 * Note: The file itself is handled by MulterModule and not part of this DTO
 */
export class ProfilePhotoDto {
  /**
   * Optional caption for the photo
   * @example "My profile picture at the gym"
   */
  @IsOptional()
  @IsString()
  caption?: string;

  /**
   * Optional alternative text for accessibility
   * @example "Person doing a CrossFit workout"
   */
  @IsOptional()
  @IsString()
  altText?: string;
}
