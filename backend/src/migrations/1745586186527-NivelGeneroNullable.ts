import { MigrationInterface, QueryRunner } from "typeorm";

export class NivelGeneroNullable1745586186527 implements MigrationInterface {
    name = 'NivelGeneroNullable1745586186527'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "nivel" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "nivel" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "genero" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "genero" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "nivel" SET DEFAULT 'Scaled'`);
        await queryRunner.query(`ALTER TABLE "usuarios" ALTER COLUMN "nivel" SET NOT NULL`);
    }

}
