import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STRAPI_URL } from '../config';

const BattleDetailsScreen = ({ route, navigation }) => {
  const { battle } = route.params;
  const [result, setResult] = useState('');
  const [wodDetails, setWodDetails] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchWodDetails();
  }, []);

  const fetchWodDetails = async () => {
    try {
      const response = await axios.get(`${STRAPI_URL}/api/wods/${battle.attributes.wod.data.id}`);
      setWodDetails(response.data.data);
    } catch (error) {
      console.error('Error fetching WOD details:', error);
    }
  };

  const submitResult = async () => {
    if (!result.trim()) {
      Alert.alert('Error', 'Por favor ingresa tu resultado');
      return;
    }

    try {
      setIsSubmitting(true);
      const userId = await AsyncStorage.getItem('userId');
      
      // Obtener los resultados actuales
      const currentBattle = await axios.get(`${STRAPI_URL}/api/battles/${battle.id}`);
      const currentResults = currentBattle.data.data.attributes.results || {};
      
      // Agregar el nuevo resultado
      const newResults = {
        ...currentResults,
        [userId]: {
          value: result,
          submittedAt: new Date().toISOString(),
        },
      };

      // Verificar si ambos participantes han enviado resultados
      const allResultsSubmitted = battle.attributes.participants.data.every(
        (participant) => newResults[participant.id]
      );

      // Actualizar estado de la batalla si todos han enviado resultados
      const status = allResultsSubmitted ? 'completed' : 'active';

      await axios.put(`${STRAPI_URL}/api/battles/${battle.id}`, {
        data: {
          results: newResults,
          status,
        },
      });

      Alert.alert(
        'Éxito',
        'Tu resultado ha sido registrado',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error submitting result:', error);
      Alert.alert('Error', 'No se pudo registrar el resultado. Inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderResults = () => {
    if (!battle.attributes.results) return null;

    return Object.entries(battle.attributes.results).map(([userId, data]) => (
      <View key={userId} style={styles.resultItem}>
        <Text style={styles.resultName}>
          {userId === battle.attributes.createdBy.data.id 
            ? battle.attributes.creatorName 
            : battle.attributes.opponentName}
        </Text>
        <Text style={styles.resultValue}>{data.value}</Text>
      </View>
    ));
  };

  if (!wodDetails) {
    return (
      <View style={styles.container}>
        <Text>Cargando...</Text>
      </View>
    );
  }

  const hasSubmittedResult = async () => {
    const userId = await AsyncStorage.getItem('userId');
    return battle.attributes.results && battle.attributes.results[userId];
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4a90e2', '#357abd']}
        style={styles.wodCard}
      >
        <Text style={styles.wodTitle}>{wodDetails.attributes.title}</Text>
        <Text style={styles.wodDescription}>{wodDetails.attributes.description}</Text>
        <View style={styles.wodDetails}>
          <Text style={styles.wodInfo}>Nivel: {wodDetails.attributes.level}</Text>
          <Text style={styles.wodInfo}>Duración: {wodDetails.attributes.duration} min</Text>
        </View>
      </LinearGradient>

      <View style={styles.battleInfo}>
        <Text style={styles.sectionTitle}>Detalles de la Batalla</Text>
        <Text style={styles.deadline}>
          Termina el: {battle.attributes.deadline}
        </Text>
        
        {battle.attributes.results && Object.keys(battle.attributes.results).length > 0 && (
          <View style={styles.resultsSection}>
            <Text style={styles.resultsSectionTitle}>Resultados:</Text>
            {renderResults()}
          </View>
        )}

        {!hasSubmittedResult() && (
          <View style={styles.submitSection}>
            <Text style={styles.submitTitle}>Registrar Resultado</Text>
            <TextInput
              style={styles.input}
              value={result}
              onChangeText={setResult}
              placeholder="Ingresa tu resultado"
              keyboardType="numeric"
              placeholderTextColor="#999"
            />
            <TouchableOpacity
              style={styles.submitButton}
              onPress={submitResult}
              disabled={isSubmitting}
            >
              <LinearGradient
                colors={['#f7b733', '#fc4a1a']}
                style={styles.gradientButton}
              >
                <Text style={styles.buttonText}>
                  {isSubmitting ? 'Enviando...' : 'Enviar Resultado'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  wodCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  wodTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  wodDescription: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 16,
  },
  wodDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wodInfo: {
    color: '#fff',
    fontSize: 14,
  },
  battleInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  deadline: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  resultsSection: {
    marginTop: 16,
  },
  resultsSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  resultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  resultName: {
    fontSize: 14,
    color: '#333',
  },
  resultValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  submitSection: {
    marginTop: 24,
  },
  submitTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  submitButton: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  gradientButton: {
    padding: 16,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BattleDetailsScreen;
