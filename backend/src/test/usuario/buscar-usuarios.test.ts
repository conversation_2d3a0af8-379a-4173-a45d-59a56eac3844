import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario, RolUsuario } from '../../modules/usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { DatabaseTestHelper, UsuariosTestHelper, usuariosFixtures } from '../helper';

/**
 * Test de búsqueda de usuarios
 * 
 * Este test utiliza una base de datos real (configurada para pruebas)
 * para verificar las operaciones de búsqueda de usuarios.
 */
describe('Buscar Usuarios Tests', () => {
  let usuariosService: UsuariosService;
  let testUserIds: string[] = [];
  const logger = new Logger('BuscarUsuariosTest');
  const testEmails = [
    '<EMAIL>', 
    '<EMAIL>', 
    '<EMAIL>'
  ];

  beforeAll(async () => {
    // Arrange: Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection('.env.development'))) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Configuración del módulo de testing usando el helper especializado para usuarios
    const module: TestingModule = await UsuariosTestHelper.createUsuariosTestingModule();
    
    usuariosService = module.get<UsuariosService>(UsuariosService);

    // Limpiar usuarios de prueba anteriores
    // Buscamos y eliminamos usuarios de prueba que puedan haber quedado de ejecuciones anteriores
    for (const email of testEmails) {
      try {
        const user = await usuariosService['usuariosRepository'].findOne({
          where: { email }
        });
        if (user) {
          await DatabaseTestHelper.cleanTestData(usuariosService['usuariosRepository'], { id: user.id });
          logger.log(`Usuario de prueba previo eliminado: ${email}`);
        }
      } catch (error) {
        logger.error(`Error al limpiar usuario previo: ${error.message}`);
      }
    }

    // Crear usuarios de prueba
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUsers = [
      {
        nombre: 'Ana García',
        alias: 'anagym',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.FEMENINO,
        rol: RolUsuario.USUARIO
      },
      {
        nombre: 'Carlos Rodríguez',
        alias: 'carlosfit',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO,
        rol: RolUsuario.USUARIO
      },
      {
        nombre: 'Laura Martínez',
        alias: 'laurabox',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.SCALED,
        genero: GeneroUsuario.FEMENINO,
        rol: RolUsuario.BOX_OWNER
      }
    ];

    // Crear usuarios en la base de datos
    for (const userData of testUsers) {
      const createdUser = await usuariosService['usuariosRepository'].save(
        usuariosService['usuariosRepository'].create(userData)
      );
      testUserIds.push(createdUser.id);
    }
  });

  afterAll(async () => {
    // Limpiar: eliminar los usuarios de prueba usando el helper
    try {
      // Usamos el ID como criterio de limpieza
      for (const id of testUserIds) {
        await DatabaseTestHelper.cleanTestData(usuariosService['usuariosRepository'], { id });
      }
      logger.log('Datos de prueba limpiados correctamente');
      
      // Limpiar los IDs guardados
      testUserIds = [];
    } catch (error) {
      logger.error(`Error al limpiar datos de prueba: ${error.message}`);
    }
  });

  // Test para obtener todos los usuarios
  it('debe obtener todos los usuarios', async () => {
    // Act: Obtener todos los usuarios
    const usuarios = await usuariosService.findAll();

    // Assert: Verificar que se obtuvieron al menos los 3 usuarios de prueba
    expect(usuarios.length).toBeGreaterThanOrEqual(3);
    
    // Verificar que los usuarios de prueba estén en los resultados
    const foundTestUsers = usuarios.filter(u => testEmails.includes(u.email));
    expect(foundTestUsers.length).toBe(3);
  });

  // Test para encontrar un usuario por ID
  it('debe encontrar un usuario por ID', async () => {
    // Arrange: Obtener un ID de usuario de prueba
    const userId = testUserIds[0];

    // Act: Buscar usuario por ID
    const usuario = await usuariosService.findOne(userId);

    // Assert: Verificaciones
    expect(usuario).toBeDefined();
    expect(usuario.id).toBe(userId);
  });

  // Test para encontrar un usuario por email
  it('debe encontrar un usuario por email', async () => {
    // Arrange: Definir email a buscar
    const testEmail = '<EMAIL>';

    // Act: Buscar usuario por email
    const usuario = await usuariosService.findByEmail(testEmail);

    // Assert: Verificaciones
    expect(usuario).toBeDefined();
    
    // Verificar que usuario no sea null antes de acceder a sus propiedades
    if (usuario) {
      expect(usuario.email).toBe(testEmail);
      expect(usuario.nombre).toBe('Ana García');
    } else {
      fail('El usuario no debería ser null');
    }
  });

  // Test para verificar que la búsqueda incluye relaciones (como box)
  it('debe incluir las relaciones en la búsqueda de usuarios', async () => {
    // Arrange: Obtener un ID de usuario de prueba
    const userId = testUserIds[0];

    // Act: Buscar usuario por ID
    const usuario = await usuariosService.findOne(userId);

    // Assert: Verificar que la propiedad box está definida (aunque podría ser null)
    expect(usuario).toHaveProperty('box');
  });
});
