import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authApiNest from '../api/authApiNest';
import { jwtDecode } from 'jwt-decode'; // Ya tenemos la dependencia instalada

/**
 * Contexto de Autenticación para NestJS
 * Maneja el estado de autenticación y provee métodos para login, logout, etc.
 */
const AuthContext = createContext();

/**
 * Hook para usar el contexto de autenticación
 * @returns {Object} - Objeto con el estado de autenticación y métodos
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

/**
 * Proveedor de Autenticación para NestJS
 * @param {Object} props - Props del componente
 * @param {React.ReactNode} props.children - Hijos del componente
 */
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [setupCompleted, setSetupCompleted] = useState(false);

  /**
   * Efecto para verificar el token al inicio
   */
  useEffect(() => {
    const checkToken = async () => {
      try {
        // Intentar obtener el token
        const token = await AsyncStorage.getItem('auth-token');
        
        if (token) {
          // Decodificar el token para obtener información del usuario
          try {
            const decoded = jwtDecode(token);
            
            // Verificar si el token ha expirado
            if (decoded.exp * 1000 > Date.now()) {
              // Token válido, actualizar estado
              // Log simplificado
              console.log(`🔓 Sesión iniciada: ${decoded.email}`);
              setIsAuthenticated(true);
              setUser({
                id: decoded.sub,
                email: decoded.email,
                emailVerified: decoded.emailVerificado || false,
                setupCompleted: decoded.setupCompleted || false,
                onBoarding: decoded.onBoarding || false,
                // Otras propiedades según el payload del token
              });
              setEmailVerified(decoded.emailVerificado || false);
              setSetupCompleted(decoded.setupCompleted || false);
            } else {
              // Token expirado, intentar renovarlo
              const refreshed = await refreshToken();
              if (!refreshed) {
                // No se pudo renovar, limpiar estado
                setUser(null);
                setIsAuthenticated(false);
                setEmailVerified(false);
              }
            }
          } catch (decodeError) {
            console.error('Error al decodificar token:', decodeError);
            // Token inválido, limpiar estado
            await AsyncStorage.removeItem('auth-token');
            setUser(null);
            setIsAuthenticated(false);
            setEmailVerified(false);
          }
        } else {
          // No hay token, usuario no autenticado
          setUser(null);
          setIsAuthenticated(false);
          setEmailVerified(false);
        }
      } catch (error) {
        console.error('Error al verificar estado de autenticación:', error);
        // En caso de error, asumir que no está autenticado
        setUser(null);
        setIsAuthenticated(false);
        setEmailVerified(false);
      } finally {
        // Finalizar carga
        setLoading(false);
      }
    };
    
    checkToken();
  }, []);

  /**
   * Renueva el token usando el refresh token
   * @returns {Promise<boolean>} - True si se renovó correctamente
   */
  const refreshToken = async () => {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh-token');
      if (!refreshToken) {
        return false;
      }
      
      const response = await authApiNest.refreshToken(refreshToken);
      
      if (response.access_token) {
        // Guardar token
        await AsyncStorage.setItem('auth-token', response.access_token);
        
        // Guardar refresh token si existe
        if (response.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.refresh_token);
        }
        
        // Actualizar estado de autenticación
        const decoded = jwtDecode(response.access_token);
        
        setUser({
          id: decoded.sub,
          email: decoded.email,
          emailVerified: decoded.emailVerificado || false,
          setupCompleted: decoded.setupCompleted || false,
          onBoarding: decoded.onBoarding || false,
          // Otras propiedades
        });
        
        setIsAuthenticated(true);
        setEmailVerified(decoded.emailVerificado || false);
        setSetupCompleted(decoded.setupCompleted || false);
        
        // Siempre mostramos el estado que acabamos de establecer, no el estado React actual
        console.log(`🔓 Usuario autenticado${decoded.emailVerificado ? ', email verificado' : ''}`);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error al renovar token:', error);
      return false;
    }
  };

  /**
   * Inicia sesión con email y password
   * @param {string} email - Email del usuario
   * @param {string} password - Contraseña del usuario
   * @returns {Promise<boolean>} - True si es el primer inicio de sesión
   */
  const login = async (email, password) => {
    try {
      // Iniciar sesión con NestJS
      const response = await authApiNest.login(email, password);
      
      // Verificar respuesta
      if (response.access_token) {
        // Decodificar token para obtener información del usuario
        const decoded = jwtDecode(response.access_token);
        
        // Verificar si es el primer inicio de sesión
        // Priorizar la información del token JWT sobre el AsyncStorage
        const isSetupCompleted = decoded.setupCompleted || false;
        if (isSetupCompleted) {
          // Si el token indica que ya se completó el setup, actualizar AsyncStorage
          await AsyncStorage.setItem('setupCompleted', 'true');
        } else {
          // Verificar si hay información en AsyncStorage como respaldo
          const storedSetupCompleted = await AsyncStorage.getItem('setupCompleted');
          // Solo actualizar si no hay info en el token pero sí en AsyncStorage
          if (storedSetupCompleted === 'true' && !isSetupCompleted) {
            console.log('📝 Actualizando estado de setupCompleted desde AsyncStorage');
          }
        }
        const isFirstLogin = !isSetupCompleted && await AsyncStorage.getItem('setupCompleted') !== 'true';
        
        // Actualizar estado
        setUser({
          id: decoded.sub,
          email: decoded.email,
          emailVerified: decoded.emailVerificado || false,
          setupCompleted: decoded.setupCompleted || false,
          onBoarding: decoded.onBoarding || false,
          // Otras propiedades según el payload del token
        });
        
        setIsAuthenticated(true);
        setEmailVerified(decoded.emailVerificado || false);
        setSetupCompleted(decoded.setupCompleted || false);

        // El usuario acaba de iniciar sesión correctamente
        console.log(`🔓 Usuario autenticado${decoded.emailVerificado ? ', email verificado' : ''}`);
        
        return isFirstLogin;
      }
      
      throw new Error('No se recibió token de acceso');
    } catch (error) {
      console.error(`Error en login para usuario ${email}:`, error);
      throw error;
    }
  };

  /**
   * Cierra la sesión del usuario
   * @returns {Promise<boolean>} - True si se cerró sesión correctamente
   */
  const logout = async () => {
    try {
      // Cerrar sesión en NestJS
      await authApiNest.logout();
      
      // Actualizar estado
      setIsAuthenticated(false);
      setUser(null);
      setEmailVerified(false);
      
      console.log('🔒 Usuario no autenticado');
      return true;
    } catch (error) {
      console.error('Error en proceso de logout:', error);
      throw error;
    }
  };

  /**
   * Actualiza los datos del usuario desde el servidor y refresca el token
   * @returns {Promise<boolean>} - True si el email está verificado
   */
  const refreshUser = async () => {
    try {
      // Verificar estado de verificación de email
      const verificationStatus = await authApiNest.checkEmailVerification();
      
      // Obtener tokens actuales
      const currentToken = await AsyncStorage.getItem('auth-token');
      
      if (verificationStatus.verified) {
        console.log('Email verificado en el servidor! 👍');
        
        // Actualizar estado local inmediatamente
        setEmailVerified(true);
        
        // Actualizar datos del usuario en memoria
        if (user) {
          setUser({
            ...user,
            emailVerified: true
          });
        }
        
        // *** MEJORA: ACTUALIZAR TOKEN JWT ***
        if (currentToken) {
          try {
            // Solicitar un nuevo token que refleje el estado actualizado
            // Esto hace una llamada al endpoint de refresh token o re-autentica
            console.log('Obteniendo nuevo token con estado actualizado...');
            
            // Intenta refrescar el token primero
            const refreshSuccess = await refreshToken();
            
            if (refreshSuccess) {
              console.log('Token actualizado correctamente 🚀');
            } else {
              console.log('No se pudo refrescar el token, pero el email está verificado');
            }
          } catch (tokenError) {
            console.error('Error al actualizar token JWT:', tokenError);
            // Continuamos aunque haya error en la actualización del token
            // El estado está actualizado en memoria
          }
        }
        
        return true;
      } else {
        console.log('Email aún no verificado en el servidor ⏳');
        setEmailVerified(false);
      }
      
      return verificationStatus.verified || false;
    } catch (error) {
      console.error('Error al actualizar usuario:', error);
      return false;
    }
  };

  // Si está cargando, no mostrar nada
  if (loading) {
    return null; // o un spinner de carga
  }

  // Proveer contexto
  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      login,
      logout,
      loading,
      emailVerified,
      refreshUser,
      refreshToken
    }}>
      {children}
    </AuthContext.Provider>
  );
};
