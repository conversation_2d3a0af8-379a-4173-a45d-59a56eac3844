/**
 * Box Service
 * Gestiona todas las operaciones relacionadas con boxes (gimnasios)
 * Implementa caché optimizada para reducir llamadas repetitivas
 */
import apiClient from '../api/apiClient';
import cacheManager from '../utils/cacheManager';

// Claves de caché para recursos de boxes
const CACHE_KEYS = {
  ALL_BOXES: 'boxes_all',
  BOX_DETAIL: (id) => `box_${id}`,
  BOX_MEMBERS: (id) => `box_${id}_members`,
  BOX_RANKINGS: (id) => `box_${id}_rankings`,
  BOX_LEAGUES: (id) => `box_${id}_leagues`,
  MY_BOX: 'my_box'
};

const boxService = {
  /**
   * Obtiene todos los boxes disponibles
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de boxes
   */
  async getAllBoxes(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedBoxes = await cacheManager.get(CACHE_KEYS.ALL_BOXES);
        if (cachedBoxes) {
          return cachedBoxes;
        }
      }
      
      // Obtener datos frescos
      const boxes = await apiClient.get('/api/boxes');
      
      // Guardar en caché (24 horas - datos que cambian con poca frecuencia)
      await cacheManager.set(CACHE_KEYS.ALL_BOXES, boxes, 24 * 60 * 60 * 1000);
      
      return boxes;
    } catch (error) {
      console.error('Error obteniendo todos los boxes:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Busca boxes por nombre
   * @param {string} query - Texto de búsqueda
   * @returns {Promise<Array>} - Resultados de búsqueda
   */
  async searchBoxes(query) {
    try {
      // Para búsquedas no usamos caché ya que los parámetros son variables
      return await apiClient.get(`/api/boxes/search?q=${encodeURIComponent(query)}`);
    } catch (error) {
      console.error(`Error buscando boxes con query "${query}":`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene detalles de un box específico
   * @param {string} boxId - ID del box
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos del box
   */
  async getBoxDetails(boxId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BOX_DETAIL(boxId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedBox = await cacheManager.get(cacheKey);
        if (cachedBox) {
          return cachedBox;
        }
      }
      
      // Obtener datos frescos
      const box = await apiClient.get(`/api/boxes/${boxId}`);
      
      // Guardar en caché (2 horas)
      await cacheManager.set(cacheKey, box, 2 * 60 * 60 * 1000);
      
      return box;
    } catch (error) {
      console.error(`Error obteniendo detalles del box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene los miembros de un box
   * @param {string} boxId - ID del box
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de miembros
   */
  async getBoxMembers(boxId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BOX_MEMBERS(boxId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedMembers = await cacheManager.get(cacheKey);
        if (cachedMembers) {
          return cachedMembers;
        }
      }
      
      // Obtener datos frescos
      const members = await apiClient.get(`/api/boxes/${boxId}/members`);
      
      // Guardar en caché (30 minutos)
      await cacheManager.set(cacheKey, members, 30 * 60 * 1000);
      
      return members;
    } catch (error) {
      console.error(`Error obteniendo miembros del box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene las clasificaciones de un box por ligas
   * @param {string} boxId - ID del box
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Rankings del box
   */
  async getBoxRankings(boxId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BOX_RANKINGS(boxId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedRankings = await cacheManager.get(cacheKey);
        if (cachedRankings) {
          return cachedRankings;
        }
      }
      
      // Obtener datos frescos
      const rankings = await apiClient.get(`/api/boxes/${boxId}/rankings`);
      
      // Guardar en caché (15 minutos)
      await cacheManager.set(cacheKey, rankings, 15 * 60 * 1000);
      
      return rankings;
    } catch (error) {
      console.error(`Error obteniendo rankings del box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene las ligas en las que participa un box
   * @param {string} boxId - ID del box
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de ligas
   */
  async getBoxLeagues(boxId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BOX_LEAGUES(boxId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeagues = await cacheManager.get(cacheKey);
        if (cachedLeagues) {
          return cachedLeagues;
        }
      }
      
      // Obtener datos frescos
      const leagues = await apiClient.get(`/api/boxes/${boxId}/leagues`);
      
      // Guardar en caché (60 minutos)
      await cacheManager.set(cacheKey, leagues, 60 * 60 * 1000);
      
      return leagues;
    } catch (error) {
      console.error(`Error obteniendo ligas del box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene información del box al que pertenece el usuario actual
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos del box
   */
  async getMyBox(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedBox = await cacheManager.get(CACHE_KEYS.MY_BOX);
        if (cachedBox) {
          return cachedBox;
        }
      }
      
      // Obtener datos frescos
      const box = await apiClient.get('/api/boxes/my-box');
      
      // Guardar en caché (2 horas)
      await cacheManager.set(CACHE_KEYS.MY_BOX, box, 2 * 60 * 60 * 1000);
      
      return box;
    } catch (error) {
      // Si el error es 404, significa que el usuario no pertenece a ningún box
      if (error.response && error.response.status === 404) {
        return null;
      }
      
      console.error('Error obteniendo mi box:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Solicita unirse a un box
   * @param {string} boxId - ID del box
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async requestToJoinBox(boxId) {
    try {
      const result = await apiClient.post(`/api/boxes/${boxId}/join`);
      
      // Invalidar caché
      await cacheManager.invalidate(CACHE_KEYS.MY_BOX);
      
      return result;
    } catch (error) {
      console.error(`Error solicitando unirse al box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Abandona el box actual
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async leaveBox() {
    try {
      const result = await apiClient.post('/api/boxes/leave');
      
      // Invalidar caché
      await cacheManager.invalidate(CACHE_KEYS.MY_BOX);
      
      return result;
    } catch (error) {
      console.error('Error abandonando box:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Crea un nuevo box (para owners/administradores)
   * @param {Object} boxData - Datos del nuevo box
   * @returns {Promise<Object>} - Box creado
   */
  async createBox(boxData) {
    try {
      const box = await apiClient.post('/api/boxes', boxData);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.ALL_BOXES),
        cacheManager.invalidate(CACHE_KEYS.MY_BOX)
      ]);
      
      return box;
    } catch (error) {
      console.error('Error creando box:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Actualiza información de un box (para owners/administradores)
   * @param {string} boxId - ID del box
   * @param {Object} boxData - Nuevos datos del box
   * @returns {Promise<Object>} - Box actualizado
   */
  async updateBox(boxId, boxData) {
    try {
      const box = await apiClient.patch(`/api/boxes/${boxId}`, boxData);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.ALL_BOXES),
        cacheManager.invalidate(CACHE_KEYS.BOX_DETAIL(boxId)),
        cacheManager.invalidate(CACHE_KEYS.MY_BOX)
      ]);
      
      return box;
    } catch (error) {
      console.error(`Error actualizando box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Actualiza avatar/logo del box
   * @param {string} boxId - ID del box
   * @param {Object} imageData - Datos de la imagen
   * @returns {Promise<Object>} - Respuesta con URL de imagen
   */
  async updateBoxLogo(boxId, imageData) {
    try {
      // Crear FormData para envío de archivo
      const formData = new FormData();
      formData.append('logo', {
        uri: imageData.uri,
        type: imageData.type || 'image/jpeg',
        name: imageData.fileName || 'box_logo.jpg'
      });
      
      const result = await apiClient.postForm(`/api/boxes/${boxId}/logo`, formData);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.ALL_BOXES),
        cacheManager.invalidate(CACHE_KEYS.BOX_DETAIL(boxId)),
        cacheManager.invalidate(CACHE_KEYS.MY_BOX)
      ]);
      
      return result;
    } catch (error) {
      console.error(`Error actualizando logo del box ${boxId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Aprueba o rechaza solicitud de membresía (para owners/administradores)
   * @param {string} boxId - ID del box
   * @param {string} userId - ID del usuario
   * @param {boolean} approved - Si es aprobado o rechazado
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async respondToJoinRequest(boxId, userId, approved) {
    try {
      const result = await apiClient.post(`/api/boxes/${boxId}/members/${userId}`, {
        approved
      });
      
      // Invalidar caché de miembros
      await cacheManager.invalidate(CACHE_KEYS.BOX_MEMBERS(boxId));
      
      return result;
    } catch (error) {
      console.error(`Error respondiendo a solicitud de unión al box ${boxId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Remueve un miembro del box (para owners/administradores)
   * @param {string} boxId - ID del box
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async removeMember(boxId, userId) {
    try {
      const result = await apiClient.delete(`/api/boxes/${boxId}/members/${userId}`);
      
      // Invalidar caché de miembros
      await cacheManager.invalidate(CACHE_KEYS.BOX_MEMBERS(boxId));
      
      return result;
    } catch (error) {
      console.error(`Error removiendo miembro ${userId} del box ${boxId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  }
};

export default boxService;
