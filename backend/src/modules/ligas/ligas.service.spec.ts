import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LigasService } from './ligas.service';
import { Liga, EstadoLiga } from './entities/liga.entity';
import { ConflictException, NotFoundException } from '@nestjs/common';

describe('LigasService', () => {
  let service: LigasService;
  let repository: Repository<Liga>;

  const mockLiga = {
    id: '1',
    nombre: 'Test Liga',
    descripcion: 'Test Description',
    fechaInicio: new Date('2025-01-01'),
    fechaFin: new Date('2025-02-01'),
    estado: EstadoLiga.PREPARACION,
    precioInscripcion: 100,
    precioEarly: 80,
    tieneDescuentoEarly: true,
    bonusConsistencia: true,
    categoriaRx: true,
    categoriaIntermedio: true,
    categoriaScaled: true,
    generoMasculino: true,
    generoFemenino: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockLigaRepository = {
    create: jest.fn().mockReturnValue(mockLiga),
    save: jest.fn().mockResolvedValue(mockLiga),
    findOne: jest.fn().mockImplementation((options) => {
      if (options?.where?.nombre === 'Test Liga') {
        return null; // Para evitar el error de liga existente
      }
      return Promise.resolve(mockLiga);
    }),
    find: jest.fn().mockResolvedValue([mockLiga]),
    update: jest.fn().mockResolvedValue({ affected: 1 }),
    delete: jest.fn().mockResolvedValue({ affected: 1 }),
    merge: jest.fn().mockReturnValue(mockLiga),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LigasService,
        {
          provide: getRepositoryToken(Liga),
          useValue: mockLigaRepository,
        },
      ],
    }).compile();

    service = module.get<LigasService>(LigasService);
    repository = module.get<Repository<Liga>>(getRepositoryToken(Liga));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new liga', async () => {
      // Arrange
      const createLigaDto = {
        nombre: 'New Liga',
        descripcion: 'Test Description',
        fechaInicio: new Date('2025-01-01'),
        fechaFin: new Date('2025-02-01'),
        duracionSemanas: 4,
        precioInscripcion: 100,
        precioEarly: 80,
        tieneDescuentoEarly: true,
        bonusConsistencia: true,
        categoriaRx: true,
        categoriaIntermedio: true,
        categoriaScaled: true,
        generoMasculino: true,
        generoFemenino: true,
      };

      // Mock para evitar el error de liga existente
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(null);

      // Act
      const result = await service.create(createLigaDto);

      // Assert
      expect(result).toEqual(mockLiga);
      expect(mockLigaRepository.create).toHaveBeenCalled();
      expect(mockLigaRepository.save).toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return an array of ligas', async () => {
      // Act
      const result = await service.findAll();

      // Assert
      expect(result).toEqual([mockLiga]);
      expect(mockLigaRepository.find).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a liga by id', async () => {
      // Act
      const result = await service.findOne('1');

      // Assert
      expect(result).toEqual(mockLiga);
      expect(mockLigaRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
      });
    });

    it('should throw NotFoundException if liga not found', async () => {
      // Arrange
      mockLigaRepository.findOne.mockResolvedValueOnce(null);

      // Act & Assert
      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a liga', async () => {
      // Arrange
      const updateLigaDto = {
        nombre: 'Updated Liga',
      };

      // Act
      const result = await service.update('1', updateLigaDto);

      // Assert
      expect(result).toEqual(mockLiga);
      expect(mockLigaRepository.merge).toHaveBeenCalledWith(mockLiga, updateLigaDto);
      expect(mockLigaRepository.save).toHaveBeenCalledWith(mockLiga);
    });
  });

  describe('remove', () => {
    it('should remove a liga', async () => {
      // Act
      await service.remove('1');

      // Assert
      expect(mockLigaRepository.delete).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException if liga not found', async () => {
      // Arrange
      mockLigaRepository.delete.mockResolvedValueOnce({ affected: 0 });

      // Act & Assert
      await expect(service.remove('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('activarLiga', () => {
    it('should activate a liga', async () => {
      // Act
      const result = await service.activarLiga('1');

      // Assert
      expect(result).toEqual(mockLiga);
      expect(mockLigaRepository.save).toHaveBeenCalled();
    });

    it('should throw ConflictException if liga is already active', async () => {
      // Arrange
      const activeLiga = { ...mockLiga, estado: EstadoLiga.ACTIVA };
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(activeLiga);

      // Act & Assert
      await expect(service.activarLiga('1')).rejects.toThrow(ConflictException);
    });

    it('should throw ConflictException if liga is finalized', async () => {
      // Arrange
      const finalizedLiga = { ...mockLiga, estado: EstadoLiga.FINALIZADA };
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(finalizedLiga);

      // Act & Assert
      await expect(service.activarLiga('1')).rejects.toThrow(ConflictException);
    });
  });

  describe('finalizarLiga', () => {
    it('should finalize a liga', async () => {
      // Arrange
      const activeLiga = { ...mockLiga, estado: EstadoLiga.ACTIVA };
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(activeLiga);

      // Act
      const result = await service.finalizarLiga('1');

      // Assert
      expect(result).toEqual(mockLiga);
      expect(mockLigaRepository.save).toHaveBeenCalled();
    });

    it('should throw ConflictException if liga is in preparation', async () => {
      // Arrange
      const prepLiga = { ...mockLiga, estado: EstadoLiga.PREPARACION };
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(prepLiga);

      // Act & Assert
      await expect(service.finalizarLiga('1')).rejects.toThrow(ConflictException);
    });

    it('should throw ConflictException if liga is already finalized', async () => {
      // Arrange
      const finalizedLiga = { ...mockLiga, estado: EstadoLiga.FINALIZADA };
      jest.spyOn(mockLigaRepository, 'findOne').mockResolvedValueOnce(finalizedLiga);

      // Act & Assert
      await expect(service.finalizarLiga('1')).rejects.toThrow(ConflictException);
    });
  });

  describe('findActivas', () => {
    it('should return active ligas', async () => {
      // Act
      const result = await service.findActivas();

      // Assert
      expect(result).toEqual([mockLiga]);
      expect(mockLigaRepository.find).toHaveBeenCalledWith({
        where: { estado: EstadoLiga.ACTIVA },
      });
    });
  });

  describe('findProximas', () => {
    it('should return upcoming ligas', async () => {
      // Arrange
      jest.spyOn(mockLigaRepository, 'find').mockResolvedValueOnce([mockLiga]);

      // Act
      const result = await service.findProximas();

      // Assert
      expect(result).toEqual([mockLiga]);
      expect(mockLigaRepository.find).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            estado: EstadoLiga.PREPARACION,
          }),
        })
      );
    });
  });
});
