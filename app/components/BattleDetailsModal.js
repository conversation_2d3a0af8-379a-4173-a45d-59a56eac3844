import React from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, Pressable } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const BattleDetailsModal = ({ battle, visible, onClose }) => {
  if (!battle) return null;

  const getWinnerStyle = (playerId) => {
    return battle.winner === playerId ? styles.winner : styles.loser;
  };

  const getScoreStyle = (playerId) => {
    return battle.winner === playerId ? styles.winnerScore : styles.loserScore;
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        <View style={styles.modalContent} onStartShouldSetResponder={() => true}>
          <View style={styles.card}>
            <View style={styles.wodInfo}>
              <View style={styles.wodHeader}>
                <Text style={styles.wodTitle} numberOfLines={1}>
                  {battle.wod?.title || 'WOD no asignado'}
                </Text>
              </View>
              <View style={styles.levelContainer}>
                <Icon name="fitness-center" size={14} color="#4a90e2" />
                <Text style={styles.levelText}>{battle.wod?.level || 'Nivel'}</Text>
              </View>
              {battle.wod?.description && (
                <Text style={styles.wodDescription}>{battle.wod.description}</Text>
              )}
            </View>

            <View style={styles.playersContainer}>
              <View style={[styles.playerSide, getWinnerStyle(battle.creatorId)]}>
                <View style={styles.avatarContainer}>
                  <Icon name="person" size={40} color="#4a90e2" />
                  {battle.winner === battle.creatorId && (
                    <View style={styles.crownContainer}>
                      <Icon name="emoji-events" size={20} color="#FFD700" />
                    </View>
                  )}
                </View>
                <Text style={styles.playerName}>{battle.creatorName}</Text>
                <Text style={[styles.score, getScoreStyle(battle.creatorId)]}>
                  {battle.creatorResult || 'Sin resultado'}
                </Text>
              </View>

              <View style={styles.vsContainer}>
                <Text style={styles.vsText}>VS</Text>
              </View>

              <View style={[styles.playerSide, getWinnerStyle(battle.participants?.[0]?.id)]}>
                <View style={styles.avatarContainer}>
                  <Icon name="person" size={40} color="#4a90e2" />
                  {battle.winner === battle.participants?.[0]?.id && (
                    <View style={styles.crownContainer}>
                      <Icon name="emoji-events" size={20} color="#FFD700" />
                    </View>
                  )}
                </View>
                <Text style={styles.playerName}>
                  {battle.participants?.[0]?.username || 'Waiting...'}
                </Text>
                <Text style={[styles.score, getScoreStyle(battle.participants?.[0]?.id)]}>
                  {battle.participants?.[0]?.result || 'Sin resultado'}
                </Text>
              </View>
            </View>

            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: 'transparent',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  wodInfo: {
    marginBottom: 20,
  },
  wodHeader: {
    marginBottom: 8,
  },
  wodTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  wodDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  levelText: {
    fontSize: 12,
    color: '#4a90e2',
    marginLeft: 4,
  },
  playersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  playerSide: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
  },
  winner: {
    backgroundColor: '#f0f8ff',
  },
  loser: {
    backgroundColor: '#f8f8f8',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  crownContainer: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  playerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
  },
  score: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  winnerScore: {
    color: '#4a90e2',
  },
  loserScore: {
    color: '#666',
  },
  vsContainer: {
    paddingHorizontal: 16,
  },
  vsText: {
    fontSize: 16,
    color: '#999',
    fontWeight: 'bold',
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 4,
  },
});

export default BattleDetailsModal;
