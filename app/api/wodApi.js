import { STRAPI_URL } from '../config';

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    const error = await response.text();
    console.error('API Error Response:', error);
    throw new Error(error);
  }
  const data = await response.json();
  return data;
};

/**
 * Fetch all available WODs
 * @returns {Promise<Array>} Array of WODs
 */
const fetchWODs = async () => {
  try {
    //console.log('Fetching WODs from:', `${STRAPI_URL}/api/wods`);
    const response = await fetch(
      `${STRAPI_URL}/api/wods?populate=*&filters[isActive][$eq]=true`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    const data = await handleResponse(response);
    //console.log('WODs response:', data);
    return data.data || [];
  } catch (error) {
    console.error('Error fetching WODs:', error);
    throw error;
  }
};

/**
 * Fetch a specific WOD by ID
 * @param {string} wodId - WOD ID
 * @returns {Promise<Object>} WOD data
 */
const fetchWODById = async (wodId) => {
  try {
    console.log('Fetching WOD by ID:', wodId);
    const response = await fetch(
      `${STRAPI_URL}/api/wods/${wodId}?populate=*`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    const data = await handleResponse(response);
    console.log('WOD detail response:', data);
    return data.data;
  } catch (error) {
    console.error('Error fetching WOD:', error);
    throw error;
  }
};

export default {
  fetchWODs,
  fetchWODById,
};
