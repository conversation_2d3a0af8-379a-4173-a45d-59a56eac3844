import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Linking
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FontAwesome } from '@expo/vector-icons';

const CustomerServiceScreen = ({ navigation }) => {
  // Función para abrir enlaces externos
  const openLink = (url) => {
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log("No se puede abrir la URL: " + url);
      }
    });
  };

  // Función para llamar a un número
  const callNumber = (number) => {
    const url = `tel:${number}`;
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log("No se puede llamar al número: " + number);
      }
    });
  };

  // Función para enviar un email
  const sendEmail = (email) => {
    const url = `mailto:${email}`;
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log("No se puede enviar email a: " + email);
      }
    });
  };

  // Función para renderizar un elemento de contacto
  const renderContactItem = (icon, title, subtitle, action) => (
    <TouchableOpacity style={styles.contactItem} onPress={action}>
      <View style={styles.contactIconContainer}>
        {icon}
      </View>
      <View style={styles.contactTextContainer}>
        <Text style={styles.contactTitle}>{title}</Text>
        <Text style={styles.contactSubtitle}>{subtitle}</Text>
      </View>
      <Ionicons name="chevron-forward" size={24} color="#e0fe10" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Servicio al Cliente</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <Text style={styles.title}>¿Cómo podemos ayudarte?</Text>
          
          <Text style={styles.subtitle}>
            Nuestro equipo de servicio al cliente está disponible para ayudarte con cualquier pregunta o problema que puedas tener.
          </Text>
          
          {/* Opciones de contacto */}
          {renderContactItem(
            <FontAwesome name="phone" size={24} color="#fff" />,
            'Llámanos',
            '+34 123 456 789',
            () => callNumber('+34123456789')
          )}
          
          {renderContactItem(
            <FontAwesome name="envelope" size={24} color="#fff" />,
            'Envíanos un email',
            '<EMAIL>',
            () => sendEmail('<EMAIL>')
          )}
          
          {renderContactItem(
            <FontAwesome name="whatsapp" size={24} color="#fff" />,
            'WhatsApp',
            'Chatea con nosotros',
            () => openLink('https://wa.me/34123456789')
          )}
          
          {renderContactItem(
            <FontAwesome name="comments" size={24} color="#fff" />,
            'Chat en vivo',
            'Disponible de 9:00 a 18:00',
            () => navigation.navigate('LiveChat')
          )}
          
          {/* Horario de atención */}
          <View style={styles.scheduleContainer}>
            <Text style={styles.scheduleTitle}>Horario de atención</Text>
            <View style={styles.scheduleItem}>
              <Text style={styles.scheduleDay}>Lunes - Viernes:</Text>
              <Text style={styles.scheduleHours}>9:00 - 18:00</Text>
            </View>
            <View style={styles.scheduleItem}>
              <Text style={styles.scheduleDay}>Sábado:</Text>
              <Text style={styles.scheduleHours}>10:00 - 14:00</Text>
            </View>
            <View style={styles.scheduleItem}>
              <Text style={styles.scheduleDay}>Domingo:</Text>
              <Text style={styles.scheduleHours}>Cerrado</Text>
            </View>
          </View>
          
          {/* Botón de formulario de contacto */}
          <TouchableOpacity 
            style={styles.contactFormButton}
            onPress={() => navigation.navigate('Contact')}
          >
            <Text style={styles.contactFormButtonText}>Formulario de contacto</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2e2e2e',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  contactIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#598392',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  contactTextContainer: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  contactSubtitle: {
    fontSize: 14,
    color: '#cccccc',
    marginTop: 4,
  },
  scheduleContainer: {
    backgroundColor: '#2e2e2e',
    borderRadius: 10,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginBottom: 15,
  },
  scheduleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  scheduleDay: {
    fontSize: 16,
    color: '#ffffff',
  },
  scheduleHours: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '500',
  },
  contactFormButton: {
    backgroundColor: '#e0fe10',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  contactFormButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#232323',
  },
});

export default CustomerServiceScreen;
