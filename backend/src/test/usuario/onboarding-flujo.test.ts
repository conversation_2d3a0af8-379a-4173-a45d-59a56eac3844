import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as bcrypt from 'bcrypt';
import { AuthService } from '../../modules/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { UpdateUsuarioDto } from '../../modules/usuarios/dto/update-usuario.dto';
import { GeneroUsuario, NivelUsuario } from '../../modules/usuarios/entities/usuario.entity';
import { DatabaseTestHelper, UsuariosTestHelper, AuthTestHelper, usuariosFixtures } from '../helper';
import { LoginDto } from '../../modules/auth/dto/login.dto';
import { ConfigService } from '@nestjs/config';


/**
 * Test del Flujo de On-boarding
 * 
 * Este test verifica el flujo completo de on-boarding:
 * 1. Registro de usuario
 * 2. Inicio de sesión
 * 3. Verificación de los campos setupCompleted y onBoarding
 * 4. Actualización de los campos setupCompleted y onBoarding
 * 5. Verificación de que los campos se reflejan correctamente en el token JWT
 */
describe('Flujo de On-boarding Tests', () => {
  let authService: AuthService;
  let usuariosService: UsuariosService;
  let jwtService: JwtService;
  let testUserId: string;
  const testPassword = 'OnboardingTest123!';
  const logger = new Logger('OnboardingFlujoTest');
  
  // Usamos los fixtures para datos de prueba
  const testUserData = {
    ...usuariosFixtures.defaultUser,
    email: '<EMAIL>',
    nombre: 'Onboarding Test User',
    alias: 'onboardingtest',
    genero: GeneroUsuario.MASCULINO,
    nivel: NivelUsuario.INTERMEDIO
  };

  beforeAll(async () => {
    // Arrange: Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Crear primero el módulo base con servicios de usuarios usando el helper
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);
    
    // Crear módulo de autenticación usando el helper especializado
    // El helper ya incluye un mock para EmailService
    const authModule = await AuthTestHelper.createAuthTestingModule(usuariosService);
    
    // Obtener las referencias de los servicios
    authService = authModule.get<AuthService>(AuthService);
    jwtService = authModule.get<JwtService>(JwtService);
    
    // Limpiar cualquier usuario de prueba previo
    await UsuariosTestHelper.deleteTestUser(
      usuariosService['usuariosRepository'],
      { email: testUserData.email }
    );
    
    // Crear un usuario de prueba con el helper
    const createdUser = await UsuariosTestHelper.createTestUser(
      usuariosService['usuariosRepository'],
      { ...testUserData, password: testPassword }
    );
    
    testUserId = createdUser.id;
    logger.log(`Usuario de prueba creado con ID: ${testUserId}`);
  });
  
  afterAll(async () => {
    // Limpiar datos de prueba usando el helper
    await UsuariosTestHelper.deleteTestUser(
      usuariosService['usuariosRepository'],
      { id: testUserId }
    );
    logger.log('Datos de prueba limpiados correctamente');
  });

  // Test que verifica que al crear un usuario, setupCompleted y onBoarding son false por defecto
  it('debe tener setupCompleted y onBoarding como false por defecto al registrarse', async () => {
    // Arrange & Act: Obtener el usuario recién creado
    const user = await usuariosService.findOne(testUserId);
    
    // Assert: Verificar valores por defecto
    expect(user).toBeDefined();
    expect(user.setupCompleted).toBe(false);
    expect(user.onBoarding).toBe(false);
  });

  // Test que verifica que estos campos se incluyen en el token JWT al iniciar sesión
  it('debe incluir setupCompleted y onBoarding en el token JWT al iniciar sesión', async () => {
    // Arrange: Credenciales para login
    const loginDto: LoginDto = {
      email: testUserData.email,
      password: testPassword,
    };

    // Act: Iniciar sesión
    const result = await authService.login(loginDto);

    // Assert: Verificar token JWT
    expect(result).toBeDefined();
    expect(result.access_token).toBeDefined();
    
    // Decodificar token
    const decoded = jwtService.decode(result.access_token);
    
    // Verificar que contiene los campos correctos
    expect(decoded).toHaveProperty('setupCompleted');
    expect(decoded).toHaveProperty('onBoarding');
    expect(decoded['setupCompleted']).toBe(false); // Valor por defecto
    expect(decoded['onBoarding']).toBe(false); // Valor por defecto
    
    // Verificar que también están en el objeto usuario
    expect(result.user).toHaveProperty('setupCompleted');
    expect(result.user).toHaveProperty('onBoarding');
    expect(result.user.setupCompleted).toBe(false);
    expect(result.user.onBoarding).toBe(false);
  });

  // Test que verifica la actualización de los campos de on-boarding
  it('debe permitir actualizar setupCompleted y onBoarding', async () => {
    // Arrange: Datos para actualizar
    const updateData: UpdateUsuarioDto = {
      setupCompleted: true,
      onBoarding: true
    };

    // Act: Actualizar perfil
    const updatedUser = await usuariosService.update(testUserId, updateData);

    // Assert: Verificar cambios
    expect(updatedUser).toBeDefined();
    expect(updatedUser.setupCompleted).toBe(true);
    expect(updatedUser.onBoarding).toBe(true);
  });

  // Test que verifica que los cambios en setupCompleted y onBoarding se reflejan en el token
  it('debe reflejar los cambios de setupCompleted y onBoarding en el token JWT', async () => {
    // Arrange: Credenciales para login
    const loginDto: LoginDto = {
      email: testUserData.email,
      password: testPassword,
    };

    // Act: Iniciar sesión después de la actualización
    const result = await authService.login(loginDto);

    // Assert: Verificar token JWT
    const decoded = jwtService.decode(result.access_token);
    
    // Los campos deben ahora ser true
    expect(decoded['setupCompleted']).toBe(true); 
    expect(decoded['onBoarding']).toBe(true);
    
    // También en el objeto usuario
    expect(result.user.setupCompleted).toBe(true);
    expect(result.user.onBoarding).toBe(true);
  });

  // Test para verificar que checkEmailVerification también devuelve setupCompleted y onBoarding
  it('debe devolver setupCompleted y onBoarding en la respuesta de checkEmailVerification', async () => {
    // Act: Verificar estado de email
    const result = await authService.checkEmailVerification(testUserId);

    // Assert: Verificar que incluye los campos correctos
    expect(result).toBeDefined();
    expect(result).toHaveProperty('setupCompleted');
    expect(result).toHaveProperty('onBoarding');
    expect(result.setupCompleted).toBe(true);
    expect(result.onBoarding).toBe(true);
  });
});
