import { IsBoolean, IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { TipoWOD, EstadoWOD } from '../entities/wod.entity';

export class CreateWodDto {
  @IsNotEmpty()
  @IsUUID()
  ligaId: string;

  @IsOptional()
  @IsUUID()
  tipoEventoId?: string;

  @IsNotEmpty()
  @IsString()
  titulo: string;

  @IsNotEmpty()
  @IsString()
  descripcion: string;

  @IsNotEmpty()
  @IsEnum(TipoWOD)
  tipo: TipoWOD;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaPublicacion: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaLimite: Date;

  @IsOptional()
  @IsBoolean()
  esBonus?: boolean;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  semana: number;

  @IsOptional()
  @IsEnum(EstadoWOD)
  estado?: EstadoWOD;

  @IsOptional()
  @IsString()
  imagen?: string;

  @IsNotEmpty()
  movimientos: any;

  @IsOptional()
  @IsString()
  resultadoLimite?: string;
}
