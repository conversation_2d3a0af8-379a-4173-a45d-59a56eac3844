<svg viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="sunsetGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF4500;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#sunsetGradient)"/>
  
  <!-- Sun -->
  <circle cx="200" cy="50" r="30" fill="#FFD700"/>
  
  <!-- Mountains silhouette -->
  <path d="M0 120 L100 80 L200 130 L300 70 L400 110 L400 200 L0 200 Z" fill="#2C3E50"/>
  
  <!-- Reflection -->
  <path d="M0 150 Q200 180 400 150 L400 200 L0 200 Z" fill="#34495E" opacity="0.5"/>
</svg>
