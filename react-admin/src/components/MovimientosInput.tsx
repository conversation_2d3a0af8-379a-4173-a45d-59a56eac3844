import React, { useState, useEffect, useCallback } from 'react';
import { useInput, useGetList } from 'react-admin';
import {
  Box,
  Typography,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';

// Importamos los subcomponentes
import MovimientoBuscador from './MovimientoBuscador';
import MovimientoConfiguracion from './MovimientoConfiguracion';
import MovimientoTabla from './MovimientoTabla';
import WodVisualizador from './WodVisualizador';
import { useWodFormData } from './useWodFormData';

// Importamos los tipos
import { ApiMovimiento, MovimientoSeleccionado, MovimientoWod } from './tipos';

interface MovimientosInputProps {
  source: string;
  label?: string;
}

export const MovimientosInput = (props: MovimientosInputProps) => {
  const { source, label } = props;

  // Obtener el control del campo en React Admin
  const {
    field,
    fieldState: { error },
    isRequired
  } = useInput({
    source,
  });

  // Cargar los movimientos desde la API
  const { data: movimientosApi, isLoading } = useGetList('movimientos', {
    pagination: { page: 1, perPage: 100 },
    sort: { field: 'nombre', order: 'ASC' }
  });

  // Estado para los movimientos del WOD
  const [movimientosWod, setMovimientosWod] = useState<MovimientoWod[]>([]);

  // Para el visualizador, vamos a usar datos estáticos por ahora
  // En una implementación futura se puede conectar con el contexto del formulario
  const formData = useWodFormData({
    titulo: 'Vista Previa del WOD',
    descripcion: 'Esta es una vista previa de cómo se verá el WOD con los movimientos seleccionados',
    tipo: 'AMRAP',
    movimientos: movimientosWod,
    semana: 1,
    esBonus: false
  });
  
  // Estado para el movimiento seleccionado con sus opciones
  const [movimientoSeleccionado, setMovimientoSeleccionado] = useState<MovimientoSeleccionado | null>(null);
  
  // Estado para el diálogo de vista previa
  const [previewMovimiento, setPreviewMovimiento] = useState<MovimientoWod | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Efecto para sincronizar el valor del campo con el estado local
  useEffect(() => {
    // Si el campo tiene un valor, lo cargamos en el estado local
    if (field.value && Array.isArray(field.value)) {
      setMovimientosWod(field.value);
    }
  }, [field.value]);

  // Función para agregar un movimiento al WOD
  const agregarMovimiento = useCallback(() => {
    if (!movimientoSeleccionado) return;

    // Aseguramos que categorias siempre esté inicializado
    const nuevoMovimiento: MovimientoWod = {
      nombre: movimientoSeleccionado.nombre,
      categorias: {}
    };

    // Estructura para agregar las variantes seleccionadas
    const m = movimientoSeleccionado.movimiento;
    const reps = movimientoSeleccionado.repeticiones;

    // Añadir variantes RX si están seleccionadas
    if (movimientoSeleccionado.categorias.rx && nuevoMovimiento.categorias) {
      nuevoMovimiento.categorias.rx = {};

      if (movimientoSeleccionado.generos.masculino && m.rxMasc && nuevoMovimiento.categorias?.rx) {
        nuevoMovimiento.categorias.rx.masculino = {
          nombre: m.rxMasc,
          reps: reps.rx?.masculino || 1
        };
      }

      if (movimientoSeleccionado.generos.femenino && m.rxFem && nuevoMovimiento.categorias?.rx) {
        nuevoMovimiento.categorias.rx.femenino = {
          nombre: m.rxFem,
          reps: reps.rx?.femenino || 1
        };
      }
    }

    // Añadir variantes INT si están seleccionadas
    if (movimientoSeleccionado.categorias.int && nuevoMovimiento.categorias) {
      nuevoMovimiento.categorias.int = {};

      if (movimientoSeleccionado.generos.masculino && m.intMasc && nuevoMovimiento.categorias?.int) {
        nuevoMovimiento.categorias.int.masculino = {
          nombre: m.intMasc,
          reps: reps.int?.masculino || 1
        };
      }

      if (movimientoSeleccionado.generos.femenino && m.intFem && nuevoMovimiento.categorias?.int) {
        nuevoMovimiento.categorias.int.femenino = {
          nombre: m.intFem,
          reps: reps.int?.femenino || 1
        };
      }
    }

    // Añadir variantes SC si están seleccionadas
    if (movimientoSeleccionado.categorias.sc && nuevoMovimiento.categorias) {
      nuevoMovimiento.categorias.sc = {};

      if (movimientoSeleccionado.generos.masculino && m.scMasc && nuevoMovimiento.categorias?.sc) {
        nuevoMovimiento.categorias.sc.masculino = {
          nombre: m.scMasc,
          reps: reps.sc?.masculino || 1
        };
      }

      if (movimientoSeleccionado.generos.femenino && m.scFem && nuevoMovimiento.categorias?.sc) {
        nuevoMovimiento.categorias.sc.femenino = {
          nombre: m.scFem,
          reps: reps.sc?.femenino || 1
        };
      }
    }

    // Añadir el movimiento a la lista y actualizar el campo
    const nuevosMovimientos = [...movimientosWod, nuevoMovimiento];
    setMovimientosWod(nuevosMovimientos);
    field.onChange(nuevosMovimientos);

    // Resetear la selección
    setMovimientoSeleccionado(null);
  }, [movimientoSeleccionado, movimientosWod, field]);

  // Función para eliminar un movimiento del WOD
  const eliminarMovimiento = useCallback((index: number) => {
    const nuevosMovimientos = [...movimientosWod];
    nuevosMovimientos.splice(index, 1);
    setMovimientosWod(nuevosMovimientos);
    field.onChange(nuevosMovimientos);
  }, [movimientosWod, field]);

  // Función para manejar la vista previa de un movimiento
  const handlePreview = useCallback((movimiento: MovimientoWod) => {
    setPreviewMovimiento(movimiento);
    setPreviewOpen(true);
  }, []);

  // Renderizado del componente
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {label || 'Movimientos del WOD'}
      </Typography>

      {isLoading ? (
        <Box display="flex" justifyContent="center" my={3}>
          <CircularProgress />
        </Box>
      ) : (
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', lg: 'row' },
          gap: 3,
          alignItems: 'flex-start'
        }}>
          {/* Columna izquierda: Editor de movimientos */}
          <Box sx={{ flex: { xs: '1 1 100%', lg: '1 1 50%' }, minWidth: 0 }}>
            {/* Buscador de movimientos */}
            <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Buscar y añadir movimientos
              </Typography>

              <MovimientoBuscador
                movimientos={movimientosApi || []}
                isLoading={isLoading}
                value={movimientoSeleccionado}
                onChange={setMovimientoSeleccionado}
              />

              {movimientoSeleccionado && (
                <MovimientoConfiguracion
                  movimientoSeleccionado={movimientoSeleccionado}
                  onChange={setMovimientoSeleccionado}
                  onAgregar={agregarMovimiento}
                />
              )}
            </Paper>

            {/* Tabla de movimientos seleccionados */}
            <MovimientoTabla
              movimientosWod={movimientosWod}
              onEliminar={eliminarMovimiento}
              onPreview={handlePreview}
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error.message}
              </Alert>
            )}
          </Box>

          {/* Columna derecha: Visualizador del WOD */}
          <Box sx={{ flex: { xs: '1 1 100%', lg: '1 1 50%' }, minWidth: 0 }}>
            <WodVisualizador
              titulo={formData.titulo}
              descripcion={formData.descripcion}
              tipo={formData.tipo}
              movimientos={movimientosWod}
              semana={formData.semana}
              esBonus={formData.esBonus}
            />
          </Box>
        </Box>
      )}
      
      {/* Diálogo de vista previa */}
      <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="md">
        <DialogTitle>Vista previa del movimiento</DialogTitle>
        <DialogContent>
          {previewMovimiento && (
            <Box>
              <Typography variant="h6">{previewMovimiento.nombre}</Typography>
              
              {Object.entries(previewMovimiento.categorias || {}).map(([categoria, generos]) => (
                <Paper key={categoria} sx={{ p: 2, mt: 2 }}>
                  <Typography variant="subtitle1">
                    {categoria.toUpperCase()}
                  </Typography>
                  
                  {generos.masculino && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="subtitle2">Masculino:</Typography>
                      <Typography>
                        Movimiento: {generos.masculino.nombre}
                      </Typography>
                      <Typography>
                        Repeticiones: {generos.masculino.reps} reps
                      </Typography>
                    </Box>
                  )}

                  {generos.femenino && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="subtitle2">Femenino:</Typography>
                      <Typography>
                        Movimiento: {generos.femenino.nombre}
                      </Typography>
                      <Typography>
                        Repeticiones: {generos.femenino.reps} reps
                      </Typography>
                    </Box>
                  )}
                </Paper>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MovimientosInput;
