import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  Divider
} from '@mui/material';
import { MovimientoWod } from './tipos';

interface WodVisualizadorProps {
  titulo?: string;
  descripcion?: string;
  tipo?: string;
  movimientos: MovimientoWod[];
  semana?: number;
  esBonus?: boolean;
}

const WodVisualizador: React.FC<WodVisualizadorProps> = ({
  titulo,
  descripcion,
  tipo,
  movimientos,
  semana,
  esBonus
}) => {
  // Función para formatear el nombre del tipo de WOD
  const formatTipo = (tipoWod?: string) => {
    switch (tipoWod) {
      case 'AMRAP':
        return 'AMRAP';
      case 'FORTIME':
        return 'For Time';
      case 'EMOM':
        return 'EMOM';
      case 'MAXWEIGHT':
        return 'Peso Máximo';
      case 'MAXREPS':
        return 'Repeticiones Máximas';
      default:
        return tipoWod || 'Sin tipo';
    }
  };

  // Función para renderizar un movimiento de forma compacta
  const renderMovimiento = (movimiento: MovimientoWod, index: number) => {
    return (
      <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1, bgcolor: '#f9f9f9' }}>
        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
          {index + 1}. {movimiento.nombre}
        </Typography>

        {movimiento.categorias && (
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {/* RX */}
            {movimiento.categorias.rx && (
              <Box>
                <Chip label="RX" size="small" color="primary" sx={{ mr: 0.5 }} />
                {movimiento.categorias.rx.masculino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♂ {movimiento.categorias.rx.masculino.reps} reps {movimiento.categorias.rx.masculino.nombre}
                    {movimiento.categorias.rx.masculino.peso && ` @ ${movimiento.categorias.rx.masculino.peso} kg`}
                  </Typography>
                )}
                {movimiento.categorias.rx.femenino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♀ {movimiento.categorias.rx.femenino.reps} reps {movimiento.categorias.rx.femenino.nombre}
                    {movimiento.categorias.rx.femenino.peso && ` @ ${movimiento.categorias.rx.femenino.peso} kg`}
                  </Typography>
                )}
              </Box>
            )}

            {/* INT */}
            {movimiento.categorias.int && (
              <Box>
                <Chip label="INT" size="small" color="secondary" sx={{ mr: 0.5 }} />
                {movimiento.categorias.int.masculino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♂ {movimiento.categorias.int.masculino.reps} reps {movimiento.categorias.int.masculino.nombre}
                    {movimiento.categorias.int.masculino.peso && ` @ ${movimiento.categorias.int.masculino.peso} kg`}
                  </Typography>
                )}
                {movimiento.categorias.int.femenino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♀ {movimiento.categorias.int.femenino.reps} reps {movimiento.categorias.int.femenino.nombre}
                    {movimiento.categorias.int.femenino.peso && ` @ ${movimiento.categorias.int.femenino.peso} kg`}
                  </Typography>
                )}
              </Box>
            )}

            {/* SC */}
            {movimiento.categorias.sc && (
              <Box>
                <Chip label="SC" size="small" sx={{ mr: 0.5 }} />
                {movimiento.categorias.sc.masculino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♂ {movimiento.categorias.sc.masculino.reps} reps {movimiento.categorias.sc.masculino.nombre}
                    {movimiento.categorias.sc.masculino.peso && ` @ ${movimiento.categorias.sc.masculino.peso} kg`}
                  </Typography>
                )}
                {movimiento.categorias.sc.femenino && (
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    ♀ {movimiento.categorias.sc.femenino.reps} reps {movimiento.categorias.sc.femenino.nombre}
                    {movimiento.categorias.sc.femenino.peso && ` @ ${movimiento.categorias.sc.femenino.peso} kg`}
                  </Typography>
                )}
              </Box>
            )}
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        mt: 2,
        border: '1px solid #e0e0e0',
        borderRadius: 1,
        bgcolor: '#fafafa'
      }}
    >
      {/* Header compacto */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            Vista Previa: {titulo || 'Nuevo WOD'}
          </Typography>
          {esBonus && (
            <Chip label="BONUS" size="small" color="warning" />
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
          {tipo && (
            <Chip label={formatTipo(tipo)} size="small" color="primary" />
          )}
          {semana && (
            <Chip label={`Semana ${semana}`} size="small" variant="outlined" />
          )}
        </Box>

        {descripcion && (
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
            {descripcion}
          </Typography>
        )}
      </Box>

      <Divider sx={{ my: 1 }} />

      {/* Movimientos compactos */}
      <Box>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          Movimientos ({movimientos.length})
        </Typography>

        {movimientos.length === 0 ? (
          <Box sx={{ p: 2, textAlign: 'center', bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              No hay movimientos agregados
            </Typography>
          </Box>
        ) : (
          <Box>
            {movimientos.map((movimiento, index) => renderMovimiento(movimiento, index))}
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default WodVisualizador;
