import React, { useEffect, useState } from 'react';
import { View, Image, StyleSheet, Dimensions, Text, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../context/AuthContextNest';
import authApiNest from '../api/authApiNest';

/**
 * SplashScreen component
 * This screen is shown when the app is launched
 * It displays the app logo and then navigates to the appropriate screen
 * based on authentication status and email verification
 */
const SplashScreen = () => {
  const navigation = useNavigation();
  const { isAuthenticated, user } = useAuth();
  const [checking, setChecking] = useState(false);
  
  useEffect(() => {
    // Función para verificar el estado y redirigir
    const checkStatusAndNavigate = async () => {
      setChecking(true);
      try {
        // Si el usuario está autenticado
        if (isAuthenticated) {
          // Verificar si el email está verificado
          let emailVerified = user?.emailVerified;
          
          // Si no tenemos el estado en el contexto, consultarlo a la API
          if (emailVerified === undefined) {
            try {
              console.log('Verificando estado de email desde SplashScreen...');
              const verificationStatus = await authApiNest.checkEmailVerification();
              emailVerified = verificationStatus?.verified === true;
              console.log('Estado de verificación obtenido:', { emailVerified });
            } catch (error) {
              console.error('Error al verificar email:', error);
              // Si hay error al verificar, asumimos que no está verificado por seguridad
              emailVerified = false;
            }
          }
          
          // Redireccionar según estado de verificación
          if (emailVerified) {
            console.log('Email verificado, redirigiendo a Main');
            navigation.replace('Main');
          } else {
            console.log('Email NO verificado, redirigiendo a EmailVerification');
            // Si el email no está verificado, ir a la pantalla de verificación
            navigation.reset({
              index: 0,
              routes: [
                { 
                  name: 'Auth', 
                  state: {
                    routes: [{ name: 'EmailVerification' }]
                  }
                }
              ]
            });
          }
        } else {
          // Usuario no autenticado, ir a Auth
          navigation.replace('Auth');
        }
      } catch (error) {
        console.error('Error en navegación de SplashScreen:', error);
        // Si hay error, ir a Auth por seguridad
        navigation.replace('Auth');
      } finally {
        setChecking(false);
      }
    };
    
    // Dar un breve tiempo para mostrar el splash y luego verificar
    const timer = setTimeout(checkStatusAndNavigate, 1500);
    
    // Limpiar el timeout al desmontar
    return () => clearTimeout(timer);
  }, [navigation, isAuthenticated, user]);
  
  return (
    <View style={styles.container}>
      <Image
        source={require('../assets/icon.png')}
        style={styles.logo}
        resizeMode="contain"
      />
      <Text style={styles.title}>The WOD League</Text>
      
      {checking && (
        <ActivityIndicator 
          style={styles.loader}
          size="large" 
          color="#e0fe10" 
        />
      )}
    </View>
  );
};

// Calculate dimensions for responsive design
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#232323', // Brand color background
  },
  logo: {
    width: width * 0.7, // 70% of screen width
    height: height * 0.3, // 30% of screen height
    maxWidth: 300, // Maximum width
    maxHeight: 300, // Maximum height
  },
  title: {
    marginTop: 20,
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff', // White text
    textAlign: 'center',
  },
  loader: {
    marginTop: 30,
  }
});

export default SplashScreen;
