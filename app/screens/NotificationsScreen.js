import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

// Componente para renderizar un ítem de notificación
const NotificationItem = ({ item, onPress }) => {
  // Función para obtener el icono según el tipo de notificación
  const getNotificationIcon = () => {
    switch (item.type) {
      case 'wod':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="star" size={24} color="#fff" />
          </View>
        );
      case 'reminder':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#e0fe10' }]}>
            <Ionicons name="bulb" size={24} color="#232323" />
          </View>
        );
      case 'achievement':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#e0fe10' }]}>
            <Ionicons name="trophy" size={24} color="#232323" />
          </View>
        );
      case 'deadline':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="time" size={24} color="#fff" />
          </View>
        );
      case 'article':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="document-text" size={24} color="#fff" />
          </View>
        );
      case 'challenge':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="star" size={24} color="#fff" />
          </View>
        );
      case 'training':
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="star" size={24} color="#fff" />
          </View>
        );
      default:
        return (
          <View style={[styles.iconContainer, { backgroundColor: '#598392' }]}>
            <Ionicons name="notifications" size={24} color="#fff" />
          </View>
        );
    }
  };

  // Formatear la fecha
  const formattedDate = () => {
    const date = new Date(item.date);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    return `${month} ${day} - ${time}`;
  };

  return (
    <TouchableOpacity style={styles.notificationItem} onPress={() => onPress(item)}>
      {getNotificationIcon()}
      <View style={styles.notificationContent}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationDate}>{formattedDate()}</Text>
      </View>
    </TouchableOpacity>
  );
};

// Componente para agrupar notificaciones por fecha
const NotificationGroup = ({ title, data, onPressNotification }) => {
  return (
    <View style={styles.notificationGroup}>
      <Text style={styles.groupTitle}>{title}</Text>
      {data.map((item) => (
        <NotificationItem
          key={item.id}
          item={item}
          onPress={onPressNotification}
        />
      ))}
    </View>
  );
};

const NotificationsScreen = () => {
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);

  // Cargar notificaciones (simulado)
  useEffect(() => {
    // Simulamos la carga de notificaciones
    const fetchNotifications = async () => {
      try {
        // En un caso real, aquí se haría una llamada a la API
        // Por ahora, usamos datos de ejemplo
        const mockNotifications = [
          {
            id: '1',
            type: 'wod',
            title: 'Nuevo Wod Disponible',
            date: '2023-06-10T10:00:00',
            read: false,
            data: { wodId: '123' }
          },
          {
            id: '2',
            type: 'reminder',
            title: '¡No Te Olvides De Beber Agua!',
            date: '2023-06-10T08:00:00',
            read: true,
            data: {}
          },
          {
            id: '3',
            type: 'achievement',
            title: '¡WOD De PVP Completado!',
            date: '2023-06-09T18:00:00',
            read: false,
            data: { battleId: '456' }
          },
          {
            id: '4',
            type: 'deadline',
            title: '¡Te Quedan Dos Días Para Completar Tu WOD!',
            date: '2023-06-09T15:00:00',
            read: false,
            data: { wodId: '789' }
          },
          {
            id: '5',
            type: 'article',
            title: 'New Article & Tip Posted!',
            date: '2023-06-09T11:00:00',
            read: true,
            data: { articleId: '101' }
          },
          {
            id: '6',
            type: 'challenge',
            title: '¡Has Empezado Un Nuevo Reto!',
            date: '2023-05-29T09:00:00',
            read: false,
            data: { challengeId: '202' }
          },
          {
            id: '7',
            type: 'training',
            title: 'New House Training Ideas!',
            date: '2023-05-29T08:20:00',
            read: true,
            data: { trainingId: '303' }
          }
        ];

        setNotifications(mockNotifications);
        setLoading(false);
      } catch (error) {
        console.error('Error al cargar notificaciones:', error);
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  // Agrupar notificaciones por fecha
  const groupNotificationsByDate = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const groups = {};

    notifications.forEach(notification => {
      const notificationDate = new Date(notification.date);
      notificationDate.setHours(0, 0, 0, 0);

      let groupKey;

      if (notificationDate.getTime() === today.getTime()) {
        groupKey = 'Hoy';
      } else if (notificationDate.getTime() === yesterday.getTime()) {
        groupKey = 'Ayer';
      } else {
        // Para fechas anteriores, usamos el formato "Month Day - Year"
        groupKey = format(notificationDate, 'MMMM d - yyyy', { locale: es });
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }

      groups[groupKey].push(notification);
    });

    // Ordenar notificaciones dentro de cada grupo por fecha (más recientes primero)
    Object.keys(groups).forEach(key => {
      groups[key].sort((a, b) => new Date(b.date) - new Date(a.date));
    });

    return groups;
  };

  // Manejar la acción al presionar una notificación
  const handleNotificationPress = (notification) => {
    // Marcar como leída
    const updatedNotifications = notifications.map(item =>
      item.id === notification.id ? { ...item, read: true } : item
    );
    setNotifications(updatedNotifications);

    // Navegar según el tipo de notificación
    switch (notification.type) {
      case 'wod':
        navigation.navigate('WodDetails', { wodId: notification.data.wodId });
        break;
      case 'achievement':
      case 'deadline':
        navigation.navigate('BattleDetails', { battleId: notification.data.battleId });
        break;
      case 'article':
        // Navegar a la pantalla de artículos (si existe)
        // navigation.navigate('ArticleDetails', { articleId: notification.data.articleId });
        break;
      case 'challenge':
        // Navegar a la pantalla de retos (si existe)
        // navigation.navigate('ChallengeDetails', { challengeId: notification.data.challengeId });
        break;
      case 'training':
        // Navegar a la pantalla de entrenamiento (si existe)
        // navigation.navigate('TrainingDetails', { trainingId: notification.data.trainingId });
        break;
      default:
        // Por defecto, no hacemos nada
        break;
    }
  };

  // Renderizar el contenido principal
  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#e0fe10" />
          <Text style={styles.emptyText}>Cargando notificaciones...</Text>
        </View>
      );
    }

    if (notifications.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off" size={50} color="#e0fe10" />
          <Text style={styles.emptyText}>No tienes notificaciones</Text>
        </View>
      );
    }

    const groupedNotifications = groupNotificationsByDate();

    return (
      <View style={styles.notificationsContainer}>
        {Object.entries(groupedNotifications).map(([date, items]) => (
          <NotificationGroup
            key={date}
            title={date}
            data={items}
            onPressNotification={handleNotificationPress}
          />
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Notificaciones</Text>
        </TouchableOpacity>

        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="notifications" size={24} color="#e0fe10" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Profile')}
          >
            <Ionicons name="person" size={24} color="#e0fe10" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Contenido principal */}
      <View style={styles.content}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 15,
  },
  content: {
    flex: 1,
    paddingHorizontal: 15,
  },
  notificationsContainer: {
    paddingBottom: 20,
  },
  notificationGroup: {
    marginBottom: 20,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginBottom: 10,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 25,
    padding: 10,
    marginBottom: 10,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#232323',
    marginBottom: 4,
  },
  notificationDate: {
    fontSize: 14,
    color: '#666666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#ffffff',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default NotificationsScreen;
