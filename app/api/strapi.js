// Definir la URL de Strapi según el entorno
const STRAPI_URL = 'http://localhost:1337'; // Para iOS simulator
// const STRAPI_URL = 'http://********:1337'; // Para Android emulator

// Helper function to handle API responses
const handleResponse = async (response) => {
  const text = await response.text();
  let data;
  try {
    data = JSON.parse(text);
  } catch (e) {
    data = text;
  }

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status} ${JSON.stringify(data)}`);
  }

  return data;
};

/**
 * @deprecated Use pvpEventApi.fetchPVPEvents() instead
 * This function will be removed in a future version
 */
export const fetchGames = async () => {
  console.warn('Deprecated: Use pvpEventApi.fetchPVPEvents() instead');
  try {
    const gamesUrl = `${STRAPI_URL}/api/games?populate=*`;    
    const response = await fetch(gamesUrl);
    const data = await handleResponse(response);
    return data;
  } catch (error) {
    console.error('Error fetching games:', error);
    return { data: [] };
  }
};

/**
 * @deprecated Use pvpEventApi.fetchUserPVPEvents() instead
 * This function will be removed in a future version
 */
export const fetchUserGames = async (userId) => {
  console.warn('Deprecated: Use pvpEventApi.fetchUserPVPEvents() instead');
  try {
    if (!userId) {
      throw new Error('userId is required');
    }

    console.log('[fetchUserGames] Fetching games for userId:', userId);
    const userGamesUrl = `${STRAPI_URL}/api/user-games/by-user/${userId}?populate[game][populate]=*&publicationState=preview`;    
    const response = await fetch(userGamesUrl);
    const data = await handleResponse(response);

    console.log('[fetchUserGames] Number of games received:', data.data?.length);
    console.log('[fetchUserGames] Game IDs:', data.data?.map(game => ({
      id: game.id,
      gameId: game.attributes?.game?.data?.id,
      title: game.attributes?.game?.data?.attributes?.title
    })));

    if (!data.data || !Array.isArray(data.data)) {
      console.log('[fetchUserGames] No valid data array found');
      return [];
    }

    return data.data;
  } catch (error) {
    console.error('[fetchUserGames] Error:', error);
    throw error;
  }
};

/**
 * @deprecated Use pvpEventApi.joinPVPEvent() instead
 * This function will be removed in a future version
 */
export const assignGameByCode = async (userId, code) => {
  console.warn('Deprecated: Use pvpEventApi.joinPVPEvent() instead');
  try {
    if (!userId || !code) {
      throw new Error('userId and code are required');
    }

    console.log('[assignGameByCode] Assigning game with code:', code, 'to user:', userId);
    const response = await fetch(`${STRAPI_URL}/api/user-games/assign`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        code: code.toUpperCase(),
      }),
    });

    const data = await handleResponse(response);
    console.log('[assignGameByCode] Response:', data);
    return data;
  } catch (error) {
    console.error('[assignGameByCode] Error:', error);
    throw error;
  }
};

export default {
  /** @deprecated Use pvpEventApi.fetchPVPEvents() instead */
  fetchGames,
  /** @deprecated Use pvpEventApi.fetchUserPVPEvents() instead */
  fetchUserGames,
  /** @deprecated Use pvpEventApi.joinPVPEvent() instead */
  assignGameByCode,
};