import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContextNest';
import Toast from 'react-native-toast-message';
import LeagueCard from '../components/LeagueCard';
import RetoDelMes from '../components/RetoDelMes';
import BatallaCard from '../components/BatallaCard';
import ligasApi from '../api/ligasApi';

const HomeScreen = ({ navigation }) => {
  // State variables
  const [proximasLigas, setProximasLigas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadData();
    });

    return unsubscribe;
  }, [navigation]);

  // Función que carga datos reales de la API NestJS
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Obtener ligas próximas desde la API
      const ligasData = await ligasApi.fetchProximasLigas();
      console.log('Ligas próximas obtenidas:', ligasData);
      
      // Adaptar el formato si es necesario para que funcione con nuestro componente
      // Nota: Es importante usar la estructura de datos correcta según la API NestJS
      const ligasFormateadas = ligasData.map(liga => ({
        id: liga.id,
        titulo: liga.nombre,
        fechaInicio: new Date(liga.fechaInicio).toISOString().split('T')[0],
        fechaFin: new Date(liga.fechaFin).toISOString().split('T')[0],
        estado: liga.estado === 'Preparacion' ? 'Inscripciones abiertas' : liga.estado,
        descripcion: liga.descripcion,
        // Usar una imagen por defecto si no tiene
        imagen: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48'
      }));
      
      // Actualizar estado con datos reales
      setProximasLigas(ligasFormateadas);
      console.log('Mostrando datos reales de próximas ligas');
    } catch (error) {
      console.error('Error en loadData:', error);
      // En caso de error, mostrar mensaje al usuario
      Toast.show({
        type: 'error',
        text1: 'Error al cargar ligas',
        text2: 'No se pudieron cargar las próximas ligas'
      });
      setProximasLigas([]);
    } finally {
      setLoading(false);
    }
  };

  // Refresh control handler
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadData().finally(() => setRefreshing(false));
  }, []);

  // Handler for league card press
  const handleLigaPress = (liga) => {
    // TODO: Navigate to league details screen
    console.log('Liga seleccionada:', liga);
    Toast.show({
      type: 'info',
      text1: 'Próximamente',
      text2: 'Detalles de liga en desarrollo',
    });
  };

  // Handler for monthly challenge press
  const handleRetoPress = () => {
    // TODO: Navigate to monthly challenge details screen
    console.log('Reto del mes seleccionado');
    Toast.show({
      type: 'info',
      text1: 'Próximamente',
      text2: 'Reto del mes en desarrollo',
    });
  };

  // Handler for battle card press
  const handleBatallaPress = (tipo) => {
    // TODO: Navigate to the corresponding battle screen based on type
    console.log(`Batalla de tipo ${tipo} seleccionada`);
    Toast.show({
      type: 'info',
      text1: 'Próximamente',
      text2: `Batallas ${tipo} en desarrollo`,
    });
  };

  // Component to render the upcoming leagues section
  const renderProximasLigas = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Próximas Ligas</Text>
      {loading ? (
        <ActivityIndicator size="large" color="#e0fe10" style={styles.loader} />
      ) : proximasLigas.length > 0 ? (
        <FlatList
          data={proximasLigas}
          renderItem={({ item }) => (
            <LeagueCard
              liga={item} // Pasamos el objeto completo, LeagueCard gestionará la estructura
              onPress={() => handleLigaPress(item)}
            />
          )}
          keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.ligasList}
        />
      ) : (
        <Text style={styles.emptyText}>
          No hay próximas ligas disponibles. ¡Estate atento a nuevas competiciones!
        </Text>
      )}
    </View>
  );

  // Component to render the monthly challenge section
  const renderRetoDelMes = () => (
    <View style={styles.section}>
      <RetoDelMes onPress={handleRetoPress} />
    </View>
  );

  // Component to render the battles section
  const renderBatallas = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Batallas</Text>
      <View style={styles.batallasContainer}>
        <BatallaCard
          batalla={{
            titulo: 'Reta A Un Amigo',
            imagen: 'https://images.unsplash.com/photo-1552674605-db6ffd4facb5?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80'
          }}
          onPress={() => handleBatallaPress('personales')}
        />
        <BatallaCard
          batalla={{
            titulo: 'Reta A La Comunidad',
            imagen: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80'
          }}
          onPress={() => handleBatallaPress('comunidad')}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor="#232323" />
      <View style={styles.container}>
        {/* Header with greeting and icons */}
        <View style={styles.header}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.greeting}>Hola, {user?.displayName || 'Atleta'}</Text>
            <Text style={styles.subGreeting}>Es hora de desafiar tus límites.</Text>
          </View>
          <View style={styles.headerIconsContainer}>
            <TouchableOpacity style={styles.iconButton}>
              <Icon name="notifications" size={24} color="#e0fe10" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => navigation.navigate('Profile')}
            >
              <Icon name="person" size={24} color="#e0fe10" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Main content */}
        {loading && !refreshing ? (
          <ActivityIndicator size="large" color="#e0fe10" style={{ marginTop: 20 }} />
        ) : (
          <FlatList
            data={[1]} // Dummy data for FlatList structure
            renderItem={() => (
              <>
                {renderProximasLigas()}
                {renderRetoDelMes()}
                {renderBatallas()}
              </>
            )}
            keyExtractor={() => 'home-content'}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#e0fe10']}
                tintColor="#e0fe10"
              />
            }
            contentContainerStyle={styles.contentContainer}
          />
        )}

        <Toast />
      </View>
    </SafeAreaView>
  );
};

// Styles
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#232323',
  },
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  contentContainer: {
    paddingBottom: 80,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerIconsContainer: {
    flexDirection: 'row',
  },
  iconButton: {
    padding: 8,
    marginLeft: 10,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#598392',
    marginBottom: 4,
  },
  subGreeting: {
    fontSize: 14,
    color: '#cccccc',
  },
  section: {
    marginBottom: 15,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginBottom: 15,
  },
  ligasList: {
    paddingVertical: 5,
    paddingLeft: 5,
    paddingRight: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#cccccc',
    textAlign: 'center',
    marginVertical: 20,
  },
  loader: {
    marginVertical: 20,
  },
  batallasContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 15,
  },
});

export default HomeScreen;