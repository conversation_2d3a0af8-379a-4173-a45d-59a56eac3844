import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { ClasificacionJob } from './clasificacion.job';
import { OutliersJob } from './outliers.job';
import { ClasificacionesModule } from '../modules/clasificaciones/clasificaciones.module';
import { ResultadosModule } from '../modules/resultados/resultados.module';
import { LigasModule } from '../modules/ligas/ligas.module';
import { WebsocketsModule } from '../websockets/websockets.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ClasificacionesModule,
    ResultadosModule,
    LigasModule,
    WebsocketsModule,
  ],
  providers: [ClasificacionJob, OutliersJob],
})
export class JobsModule {}
