import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
  Animated,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';
import { useUser } from '../context/UserContext';
import { NESTJS_URL } from '../config';
import CustomScrollBar from '../components/CustomScrollBar';
import SelectPicker from '../components/SelectPicker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import Toast from 'react-native-toast-message';

const EditProfileScreen = ({ navigation }) => {
  // Obtenemos los datos y funciones del contexto de autenticación
  const { user } = useAuth();
  
  // Obtenemos los datos y funciones del nuevo contexto de usuario
  const { 
    userData, 
    userStats,
    isLoading, 
    error,
    updateUserProfile,
    uploadProfilePhoto,
    refreshUserData
  } = useUser();
  
  // Estados locales para la edición
  const [saving, setSaving] = useState(false);
  const [localError, setLocalError] = useState(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  
  // Estados para los campos editables (usando nombres de campos del backend)
  const [nombre, setNombre] = useState('');
  const [alias, setAlias] = useState('');
  const [email, setEmail] = useState('');
  const [genero, setGenero] = useState('');
  const [nivel, setNivel] = useState('');
  const [edad, setEdad] = useState('');

  // Opciones para los selectores
  const generoOptions = ['Masculino', 'Femenino'];
  const nivelOptions = ['Escalado', 'Intermedio', 'RX'];

  // Referencias y estados para el scroll personalizado
  const scrollViewRef = useRef(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);

  // Datos originales del usuario para comparar cambios
  const [originalData, setOriginalData] = useState(null);
  
  // Estadísticas calculadas desde userStats
  const stats = {
    matches: userStats?.partidasJugadas || userStats?.matches || 0,
    victories: userStats?.victorias || userStats?.victories || 0,
    ratio: '0.0'
  };
  
  // Calcular ratio de victorias
  if (stats.matches > 0) {
    stats.ratio = ((stats.victories / stats.matches) * 100).toFixed(1);
  }

  /**
   * Effect to load user data when the component mounts
   * Uses data from the UserContext
   */
  useEffect(() => {
    // Verify we have authenticated user
    if (user && !userData && !isLoading) {
      // Request data refresh from the context
      refreshUserData(false);
    }

    // If we have user data, populate the form
    if (userData) {
      populateFormWithUserData();
      updateProfileImage();
    }
  }, [user, userData, isLoading]);

  /**
   * Effect to handle errors from the user context
   */
  useEffect(() => {
    if (error) {
      console.error('Error from user context:', error);
      setLocalError(error);
      
      // Show error message
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error,
      });
    }
  }, [error]);
  
  /**
   * Function to populate form fields with user data
   */
  const populateFormWithUserData = () => {
    if (!userData) return;
    
    // Set form fields using field names from backend
    setNombre(userData.nombre || '');
    setAlias(userData.alias || '');
    setEmail(userData.email || '');
    
    // Handle gender value with proper capitalization for display
    const generoValue = userData.genero ?
      userData.genero.charAt(0).toUpperCase() + userData.genero.slice(1) : '';
    setGenero(generoValue);
    
    // Set user level/category
    setNivel(userData.nivel || 'Sin Asignar');
    
    // Set age as string for the input field
    setEdad(userData.edad ? userData.edad.toString() : '');
    
    // Store original data for comparison when saving
    setOriginalData(userData);
  };
  
  /**
   * Update profile image from user data
   */
  const updateProfileImage = () => {
    if (!userData) return;
    
    // Check for profile image in different possible fields
    const photoUrl = userData.photoUrl || userData.foto || userData.avatar;
    
    if (photoUrl) {
      // Format URL with cache-busting
      const timestamp = new Date().getTime();
      const isAbsoluteUrl = photoUrl.startsWith('http');
      const formattedPath = !isAbsoluteUrl && !photoUrl.startsWith('/') ? 
        `/${photoUrl}` : photoUrl;
      
      const imageUrl = isAbsoluteUrl
        ? `${photoUrl}?t=${timestamp}`
        : `${NESTJS_URL}${formattedPath}?t=${timestamp}`;
      
      // Update UI
      setProfileImage({ uri: imageUrl });
    }
  };

  /**
   * Request gallery permissions when component mounts
   */
  useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Toast.show({
          type: 'info',
          text1: 'Permiso denegado',
          text2: 'Necesitamos permisos para acceder a tu galería de imágenes'
        });
      }
    })();
  }, []);

  /**
   * Handle profile image selection from gallery
   */
  const handleProfileImageUpdate = async () => {
    if (isUploadingImage) return;
    
    try {
      setIsUploadingImage(true);
      setLocalError(null);
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        mediaTypes: ImagePicker.MediaTypeOptions.Images
      });
      
      // Check if user canceled
      if (result.canceled || !result.assets || result.assets.length === 0) {
        return;
      }
      
      // Process and upload image
      await processAndUploadImage(result.assets[0].uri);
      
    } catch (error) {
      console.error('Error selecting image:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo acceder a la galería'
      });
    } finally {
      setIsUploadingImage(false);
    }
  };
  
  /**
   * Process and upload profile image
   */
  const processAndUploadImage = async (imageUri) => {
    try {
      // 1. Validate image
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!validateImage(imageUri, fileInfo.size)) {
        return false;
      }
      
      // 2. Optimize image
      const optimizedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 512 } }],
        { compress: 0.6, format: ImageManipulator.SaveFormat.JPEG }
      );
      
      // 3. Convert to base64
      const base64Image = await FileSystem.readAsStringAsync(optimizedImage.uri, {
        encoding: FileSystem.EncodingType.Base64
      });
      
      // 4. Prepare image data
      const fileType = 'jpeg';
      const mimeType = 'image/jpeg';
      const username = alias || 'user';
      const filename = generateSafeFilename(username);
      const fullFileName = `${filename}.${fileType}`;
      const dataUri = `data:${mimeType};base64,${base64Image}`;
      
      // 5. Upload through context
      await uploadProfilePhoto(dataUri, fullFileName);
      
      // Update local image after successful upload
      // Wait a moment for the server to process the image
      setTimeout(() => {
        refreshUserData(false);
      }, 1000);
      
      return true;
    } catch (error) {
      console.error('Image processing error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Error al procesar la imagen'
      });
      return false;
    }
  };
  
  /**
   * Validate image format and size
   */
  const validateImage = (uri, fileSize) => {
    // Check extension
    const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
    const extension = uri.split('.').pop().toLowerCase();
    
    if (!validExtensions.includes(extension)) {
      Toast.show({
        type: 'error',
        text1: 'Formato no válido',
        text2: 'Por favor, selecciona una imagen en formato JPG, PNG o WebP.'
      });
      return false;
    }
    
    // Check size (max 5MB)
    const MAX_SIZE = 5 * 1024 * 1024;
    if (fileSize && fileSize > MAX_SIZE) {
      Toast.show({
        type: 'error',
        text1: 'Imagen demasiado grande',
        text2: 'La imagen debe ser menor de 5MB'
      });
      return false;
    }
    
    return true;
  };
  
  /**
   * Generate a safe filename with timestamp
   */
  const generateSafeFilename = (username) => {
    const validUsername = (username && typeof username === 'string') ? username : 'user';
    const sanitizedUsername = validUsername.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${sanitizedUsername}_${timestamp}`;
  };

  /**
   * Save profile changes to the server
   */
  const handleSave = async () => {
    try {
      // Basic validation
      if (!nombre || !alias) {
        Toast.show({
          type: 'error',
          text1: 'Campos requeridos',
          text2: 'Nombre completo y alias son obligatorios'
        });
        return;
      }

      // Category validation
      if (!nivel) {
        Toast.show({
          type: 'error',
          text1: 'Selecciona un nivel',
          text2: 'Debes seleccionar un nivel para continuar'
        });
        return;
      }

      setSaving(true);

      // Prepare data for server - using field names from backend
      const updatedData = {
        nombre,
        alias,
        edad: edad ? parseInt(edad, 10) : null,
        // Convert gender to lowercase for consistency
        genero: genero.toLowerCase(),
        nivel
      };

      // Check for changes before saving
      const hasChanges = Object.keys(updatedData).some(key => {
        // Handle age field that's a number on server but string in state
        if (key === 'edad') {
          return updatedData[key] !== (originalData[key] || null);
        }
        // Handle gender field that might have different capitalization
        if (key === 'genero') {
          return updatedData[key] !== (originalData[key] || '').toLowerCase();
        }
        // Compare other fields
        return updatedData[key] !== (originalData[key] || '');
      });

      if (!hasChanges) {
        setSaving(false);
        Toast.show({
          type: 'info',
          text1: 'Sin cambios',
          text2: 'No hay cambios para guardar'
        });
        return;
      }

      // Update profile using context
      const success = await updateUserProfile(updatedData);

      setSaving(false);

      if (success) {
        Toast.show({
          type: 'success',
          text1: 'Perfil actualizado',
          text2: 'Tus datos han sido actualizados correctamente'
        });
        
        // Return to profile screen
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudo actualizar el perfil'
        });
      }
    } catch (error) {
      console.error('Error saving profile changes:', error);
      setSaving(false);
      
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Ocurrió un error al actualizar el perfil'
      });
    }
  };

  /**
   * Helper to get combined error state
   */
  const getError = () => error || localError;
  
  /**
   * Render loading state
   */
  if (isLoading && !userData) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#e0fe10" />
        <Text style={styles.loadingText}>Cargando datos...</Text>
      </View>
    );
  }
  
  /**
   * Render error state
   */
  if (getError() && !userData) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.errorText}>{getError()}</Text>
        <TouchableOpacity 
          style={styles.retryButton}
          onPress={() => refreshUserData(true)}
        >
          <Text style={styles.retryButtonText}>Reintentar</Text>
        </TouchableOpacity>
      </View>
    );
  }

  /**
   * Main render function
   */
  return (
    <View style={styles.container}>
      {/* Header - with loading indicator */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          disabled={saving}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Editar Perfil</Text>
        {isLoading && userData && (
          <ActivityIndicator 
            size="small" 
            color="#e0fe10" 
            style={styles.headerLoader}
          />
        )}
      </View>

      <View style={styles.profileContainer}>
        {/* Profile Image */}
        <View style={styles.avatarContainer}>
          {profileImage ? (
            <Image
              source={profileImage}
              style={styles.avatarImage}
              resizeMode="cover"
            />
          ) : (
            <Text style={styles.avatarText}>
              {alias ? alias.charAt(0).toUpperCase() : 'U'}
            </Text>
          )}
          <TouchableOpacity
            style={styles.editAvatarButton}
            onPress={handleProfileImageUpdate}
            disabled={isUploadingImage || saving}
          >
            {isUploadingImage ? (
              <ActivityIndicator size="small" color="#000" />
            ) : (
              <Ionicons name="camera" size={18} color="#000" />
            )}
          </TouchableOpacity>
        </View>

        {/* Form Container */}
      <View style={styles.formContainer}>
        <CustomScrollBar
          scrollViewRef={scrollViewRef}
          scrollY={scrollY}
          contentHeight={contentHeight}
          scrollViewHeight={scrollViewHeight}
        />

        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { y: scrollY } } }],
            { useNativeDriver: false }
          )}
          scrollEventThrottle={16}
          onLayout={(event) => {
            const { height } = event.nativeEvent.layout;
            setScrollViewHeight(height);
          }}
          onContentSizeChange={(width, height) => {
            setContentHeight(height);
          }}
        >
          {/* Form Fields - using field names from backend */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Nombre completo</Text>
            <TextInput
              style={styles.input}
              value={nombre}
              onChangeText={setNombre}
              placeholder="Nombre completo"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Alias</Text>
            <TextInput
              style={styles.input}
              value={alias}
              onChangeText={setAlias}
              placeholder="Alias"
              placeholderTextColor="#999"
            />
          </View>
          scrollY={scrollY}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#232323',
  },
  loadingText: {
    fontSize: 16,
    color: '#e0fe10',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: '#598392',
  },
  profileContainer: {
    backgroundColor: '#598392',
    alignItems: 'center',
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 8,
  },
  scrollContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingHorizontal: 24,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    borderWidth: 3,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
  },
  avatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#598392',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#e0fe10',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#232323',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  email: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    marginBottom: 5,
  },
  category: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginTop: 5,
    marginBottom: 25,
  },
  statsBarContainer: {
    width: '100%',
    paddingHorizontal: 15,
    marginTop: -30,
  },
  statsBar: {
    flexDirection: 'row',
    backgroundColor: '#2e2e2e',
    paddingVertical: 15,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 3,
  },
  statLabel: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
  },
  statDivider: {
    width: 1,
    height: '60%',
    backgroundColor: '#fff',
  },
  formContainer: {
    width: '100%',
    paddingTop: 20,
  },
  fieldContainer: {
    marginBottom: 16,
    width: '90%',
    alignSelf: 'center',
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginBottom: 8,
  },
  input: {
    height: 50,
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#232323',
    width: '100%',
  },
  disabledInput: {
    backgroundColor: '#2e2e2e',
    color: '#888',
    paddingRight: 30, // Espacio para el icono
  },
  emailContainer: {
    position: 'relative',
    width: '100%',
  },
  lockIcon: {
    position: 'absolute',
    right: 12,
    top: 15,
  },

  updateButton: {
    backgroundColor: '#e0fe10',
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
    width: '90%',
    alignSelf: 'center',
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#232323',
  },
});

export default EditProfileScreen;
