import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  ActivityIndicator,
  Platform,
  Animated,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';
import { useUser } from '../context/UserContext';
import { NESTJS_URL } from '../config';
import CustomScrollBar from '../components/CustomScrollBar';
import SelectPicker from '../components/SelectPicker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import Toast from 'react-native-toast-message';

/**
 * Pantalla de edición de perfil
 * Permite al usuario editar su información personal y foto de perfil
 * Usa el nuevo UserContext para persistencia de datos
 */
const EditProfileScreen = ({ navigation }) => {
  // Obtenemos los datos y funciones del contexto de autenticación
  const { user } = useAuth();

  // Obtenemos los datos y funciones del nuevo contexto de usuario
  const {
    userData,
    userStats,
    isLoading,
    error,
    updateUserData,
    uploadProfilePhoto,
    refreshUserData
  } = useUser();

  // Estados locales para la edición
  const [saving, setSaving] = useState(false);
  const [localError, setLocalError] = useState(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [profileImage, setProfileImage] = useState(null);

  // Estados para los campos editables (usando nombres de campos del backend)
  const [nombre, setNombre] = useState('');
  const [alias, setAlias] = useState('');
  const [email, setEmail] = useState('');
  const [genero, setGenero] = useState('');
  const [nivel, setNivel] = useState('');
  const [edad, setEdad] = useState('');

  // Opciones para los selectores - Estos valores DEBEN coincidir EXACTAMENTE con los de la base de datos
  const generoOptions = ['Masculino', 'Femenino'];
  const nivelOptions = ['Escalado', 'Intermedio', 'RX'];

  // Referencias y estados para el scroll personalizado
  const scrollViewRef = useRef(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);

  // Datos originales del usuario para comparar cambios
  const [originalData, setOriginalData] = useState(null);

  // Estadísticas calculadas desde userStats
  const stats = {
    matches: userStats?.partidasJugadas || userStats?.matches || '---',
    victories: userStats?.victorias || userStats?.victories || '---',
    ratio: '---'
  };

  // Calcular ratio de victorias solo cuando hay datos reales
  if (typeof stats.matches === 'number' && typeof stats.victories === 'number' && stats.matches > 0) {
    stats.ratio = ((stats.victories / stats.matches) * 100).toFixed(1);
  }

  /**
   * Effect to load user data when the component mounts
   * Uses data from the UserContext
   */
  useEffect(() => {
    // Verify we have authenticated user
    if (user && !userData && !isLoading) {
      // Request data refresh from the context
      refreshUserData(false);
    }

    // If we have user data, populate the form
    if (userData) {
      populateFormWithUserData();
      updateProfileImage();
    }
  }, [user, userData, isLoading]);

  /**
   * Effect to handle errors from the user context
   */
  useEffect(() => {
    if (error) {
      console.error('Error from user context:', error);
      setLocalError(error);

      // Show error message
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error,
      });
    }
  }, [error]);

  /**
   * Function to populate form fields with user data
   */
  const populateFormWithUserData = () => {
    if (!userData) return;

    // Set form fields using field names from backend
    setNombre(userData.nombre || '');
    setAlias(userData.alias || '');
    setEmail(userData.email || '');

    // Solución simplificada: forzar valor exacto según enum de backend
    if (userData.genero === 'Masculino') {
      setGenero('Masculino');
    } else if (userData.genero === 'Femenino') {
      setGenero('Femenino');
    }

    // Set user level/category
    setNivel(userData.nivel || 'Sin Asignar');

    // Set age as string for the input field
    if (userData.edad !== undefined && userData.edad !== null) {
      const edadStr = userData.edad.toString();
      setEdad(edadStr);
    } else {
      setEdad('');
    }

    // Store original data for comparison when saving
    setOriginalData(userData);
  };

  /**
   * Update profile image from user data
   */
  const updateProfileImage = () => {
    if (!userData) return;

    // Check for profile image in different possible fields
    const photoUrl = userData.photoUrl || userData.foto || userData.avatar;

    if (photoUrl) {
      // Format URL with cache-busting
      const timestamp = new Date().getTime();
      const isAbsoluteUrl = photoUrl.startsWith('http');
      const formattedPath = !isAbsoluteUrl && !photoUrl.startsWith('/') ?
        `/${photoUrl}` : photoUrl;

      const imageUrl = isAbsoluteUrl
        ? `${photoUrl}?t=${timestamp}`
        : `${NESTJS_URL}${formattedPath}?t=${timestamp}`;

      // Update UI
      setProfileImage({ uri: imageUrl });
    }
  };

  /**
   * Request gallery permissions when component mounts
   */
  useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Toast.show({
          type: 'info',
          text1: 'Permiso denegado',
          text2: 'Necesitamos permisos para acceder a tu galería de imágenes'
        });
      }
    })();
  }, []);

  /**
   * Handle profile image selection from gallery
   */
  const handleProfileImageUpdate = async () => {
    if (isUploadingImage) return;

    try {
      setIsUploadingImage(true);
      setLocalError(null);

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        mediaTypes: ImagePicker.MediaTypeOptions.Images
      });

      // Check if user canceled
      if (result.canceled || !result.assets || result.assets.length === 0) {
        setIsUploadingImage(false);
        return;
      }

      // Process and upload image
      await processAndUploadImage(result.assets[0].uri);

    } catch (error) {
      console.error('Error selecting image:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo acceder a la galería'
      });
      setIsUploadingImage(false);
    }
  };

  /**
   * Process and upload profile image
   */
  const processAndUploadImage = async (imageUri) => {
    try {
      // 1. Validate image
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!validateImage(imageUri, fileInfo.size)) {
        setIsUploadingImage(false);
        return false;
      }

      // 2. Optimize image
      const optimizedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 512 } }],
        { compress: 0.6, format: ImageManipulator.SaveFormat.JPEG }
      );

      // 3. Convert to base64
      const base64Image = await FileSystem.readAsStringAsync(optimizedImage.uri, {
        encoding: FileSystem.EncodingType.Base64
      });

      // 4. Prepare image data
      const fileType = 'jpeg';
      const mimeType = 'image/jpeg';
      const username = alias || 'user';
      const filename = generateSafeFilename(username);
      const fullFileName = `${filename}.${fileType}`;
      const dataUri = `data:${mimeType};base64,${base64Image}`;

      // 5. Upload through context
      await uploadProfilePhoto(dataUri, fullFileName);

      // Update local image after successful upload
      // Wait a moment for the server to process the image
      setTimeout(() => {
        refreshUserData(false);
      }, 1000);

      setIsUploadingImage(false);
      return true;
    } catch (error) {
      console.error('Image processing error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Error al procesar la imagen'
      });
      setIsUploadingImage(false);
      return false;
    }
  };

  /**
   * Validate image format and size
   */
  const validateImage = (uri, fileSize) => {
    // Check extension
    const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
    const extension = uri.split('.').pop().toLowerCase();

    if (!validExtensions.includes(extension)) {
      Toast.show({
        type: 'error',
        text1: 'Formato no válido',
        text2: 'Por favor, selecciona una imagen en formato JPG, PNG o WebP.'
      });
      return false;
    }

    // Check size (max 5MB)
    const MAX_SIZE = 5 * 1024 * 1024;
    if (fileSize && fileSize > MAX_SIZE) {
      Toast.show({
        type: 'error',
        text1: 'Imagen demasiado grande',
        text2: 'La imagen debe ser menor de 5MB'
      });
      return false;
    }

    return true;
  };

  /**
   * Generate a safe filename with timestamp
   */
  const generateSafeFilename = (username) => {
    const validUsername = (username && typeof username === 'string') ? username : 'user';
    const sanitizedUsername = validUsername.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${sanitizedUsername}_${timestamp}`;
  };

  /**
   * Save profile changes to the server
   */
  const handleSave = async () => {
    try {
      // Basic validation
      if (!nombre || !alias) {
        Toast.show({
          type: 'error',
          text1: 'Campos requeridos',
          text2: 'Nombre completo y alias son obligatorios'
        });
        return;
      }

      // Category validation
      if (!nivel) {
        Toast.show({
          type: 'error',
          text1: 'Selecciona un nivel',
          text2: 'Debes seleccionar un nivel para continuar'
        });
        return;
      }

      setSaving(true);

      // Prepare data for server - using field names from backend
      const updatedData = {
        nombre,
        alias,
        edad: edad ? parseInt(edad, 10) : null,
        // Mantener formato específico requerido por el backend
        genero: genero === 'Masculino' ? 'Masculino' : 'Femenino',
        nivel
      };

      // Check for changes before saving
      const hasChanges = Object.keys(updatedData).some(key => {
        // Handle age field that's a number on server but string in state
        if (key === 'edad') {
          return updatedData[key] !== (originalData[key] || null);
        }
        // Handle gender field that might have different capitalization
        if (key === 'genero') {
          return updatedData[key] !== (originalData[key] || '').toLowerCase();
        }
        // Compare other fields
        return updatedData[key] !== (originalData[key] || '');
      });

      if (!hasChanges) {
        setSaving(false);
        Toast.show({
          type: 'info',
          text1: 'Sin cambios',
          text2: 'No hay cambios para guardar'
        });
        return;
      }

      // Update profile using context
      const success = await updateUserData(updatedData);

      setSaving(false);

      if (success) {
        Toast.show({
          type: 'success',
          text1: 'Perfil actualizado',
          text2: 'Tus datos han sido actualizados correctamente'
        });

        // Return to profile screen
        setTimeout(() => {
          navigation.goBack();
        }, 1000);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No se pudo actualizar el perfil'
        });
      }
    } catch (error) {
      console.error('Error saving profile changes:', error);
      setSaving(false);

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Ocurrió un error al actualizar el perfil'
      });
    }
  };

  /**
   * Helper to get combined error state
   */
  const getError = () => error || localError;

  /**
   * Render loading state
   */
  if (isLoading && !userData) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#e0fe10" />
        <Text style={styles.loadingText}>Cargando datos...</Text>
      </View>
    );
  }

  /**
   * Render error state
   */
  if (getError() && !userData) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.errorText}>{getError()}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => refreshUserData(true)}
        >
          <Text style={styles.retryButtonText}>Reintentar</Text>
        </TouchableOpacity>
      </View>
    );
  }

  /**
   * Main render function
   */
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          disabled={saving}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Editar Perfil</Text>
        {isLoading && userData && (
          <ActivityIndicator
            size="small"
            color="#e0fe10"
            style={styles.headerLoader}
          />
        )}
      </View>

      {/* Main Content */}
      <View style={styles.mainContainer}>
        {/* Profile section - photo and name */}
        <View style={styles.profileSection}>
          {/* Profile Image */}
          <View style={styles.avatarContainer}>
            {profileImage ? (
              <Image
                source={profileImage}
                style={styles.avatarImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.avatarText}>
                {alias ? alias.charAt(0).toUpperCase() : 'U'}
              </Text>
            )}
            <TouchableOpacity
              style={styles.editAvatarButton}
              onPress={handleProfileImageUpdate}
              disabled={isUploadingImage || saving}
            >
              {isUploadingImage ? (
                <ActivityIndicator size="small" color="#000" />
              ) : (
                <Ionicons name="camera" size={18} color="#000" />
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats bar - Datos físicos */}


        {/* Stats bar - Estadísticas de juego */}
        <View style={styles.statsBarContainer}>
          <View style={styles.statsBar}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.matches}</Text>
              <Text style={styles.statLabel}>Partidas</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.victories}</Text>
              <Text style={styles.statLabel}>Victorias</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.ratio === '---' ? stats.ratio : `${stats.ratio}%`}</Text>
              <Text style={styles.statLabel}>Ratio</Text>
            </View>
          </View>
        </View>

        {/* Form section */}
        <View style={styles.menuOrFormContainer}>
          <View style={styles.scrollContainer}>
            <ScrollView
              ref={scrollViewRef}
              style={styles.scrollView}
              contentContainerStyle={styles.formScrollContent}
              showsVerticalScrollIndicator={Platform.OS === 'android'}
              indicatorStyle="white"
              persistentScrollbar={Platform.OS === 'android'}
              alwaysBounceVertical={false}
              directionalLockEnabled={true}
              overScrollMode="always"
              onScroll={Animated.event(
                [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                { useNativeDriver: false }
              )}
              scrollEventThrottle={16}
              onContentSizeChange={(width, height) => {
                setContentHeight(height);
              }}
              onLayout={(event) => {
                const { height } = event.nativeEvent.layout;
                setScrollViewHeight(height);
              }}
            >
              <Text style={styles.formTitle}>Nombre completo</Text>
              <View style={styles.formGroup}>
                <TextInput
                  style={styles.input}
                  value={nombre}
                  onChangeText={setNombre}
                  placeholder="Nombre completo"
                  placeholderTextColor="#999"
                />
              </View>

              <Text style={styles.formTitle}>Usuario</Text>
              <View style={styles.formGroup}>
                <TextInput
                  style={styles.input}
                  value={alias}
                  onChangeText={setAlias}
                  placeholder="Nombre de usuario"
                  placeholderTextColor="#999"
                />
              </View>

              <Text style={styles.formTitle}>Email</Text>
              <View style={styles.formGroup}>
                <TextInput
                  style={[styles.input, { backgroundColor: '#2e2e2e', color: '#aaa' }]}
                  value={email}
                  editable={false}
                  placeholder="Email no editable"
                  placeholderTextColor="#777"
                />
                <Text style={styles.inputHint}>El email no se puede modificar</Text>
              </View>

              <Text style={styles.formTitle}>Categoría</Text>
              <View style={styles.formGroup}>
                <SelectPicker
                  value={nivel}
                  onValueChange={(value) => setNivel(value)}
                  options={nivelOptions}
                  placeholder="Selecciona tu nivel"
                />
              </View>

              <Text style={styles.formTitle}>Sexo</Text>
              <View style={styles.formGroup}>
                <SelectPicker
                  value={genero}
                  onValueChange={(value) => {
                    setGenero(value);
                  }}
                  options={generoOptions}
                  placeholder="Selecciona tu género"
                />
              </View>

              <Text style={styles.formTitle}>Edad</Text>
              <View style={styles.formGroup}>
                <TextInput
                  style={styles.input}
                  value={edad}
                  onChangeText={(text) => {
                    const numericValue = text.replace(/[^0-9]/g, '');
                    setEdad(numericValue);
                  }}
                  placeholder="Edad"
                  placeholderTextColor="#777"
                  keyboardType="number-pad"
                  maxLength={3}
                />
              </View>
              {/* Save Button */}
              <TouchableOpacity
                style={[styles.saveButton, saving && styles.saveButtonDisabled]}
                onPress={handleSave}
                disabled={saving || isLoading}
              >
                {saving ? (
                  <ActivityIndicator color="#000" size="small" />
                ) : (
                  <Text style={styles.saveButtonText}>Actualizar</Text>
                )}
              </TouchableOpacity>
            </ScrollView>
            {Platform.OS === 'ios' && (
              <CustomScrollBar
                scrollViewHeight={scrollViewHeight}
                contentHeight={contentHeight}
                scrollY={scrollY}
              />
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#232323',
  },
  loadingText: {
    color: '#e0fe10',
    marginTop: 10,
    fontSize: 16,
  },
  errorText: {
    color: '#ff6b6b',
    marginHorizontal: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#e0fe10',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#232323',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 15,
    paddingHorizontal: 15,
    backgroundColor: '#598392',
  },
  headerLoader: {
    marginLeft: 'auto',
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#598392',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: '#232323',
    position: 'relative',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#232323',
  },
  fakeScrollbar: {
    position: 'absolute',
    right: 1,
    top: 4,
    bottom: 4,
    width: 3,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  fakeScrollIndicator: {
    width: '100%',
    height: '30%',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 3,
  },
  formScrollContent: {
    paddingVertical: 20,
    paddingHorizontal: 0,
  },
  profileSection: {
    backgroundColor: '#598392',
    paddingBottom: 40,
    alignItems: 'center',
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  statsBarContainer: {
    paddingHorizontal: 15,
    marginTop: -50,
  },
  statsBar: {
    flexDirection: 'row',
    backgroundColor: '#2e2e2e',
    paddingVertical: 15,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginTop: 15,
    marginBottom: -30,
    zIndex: 1,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: '60%',
    backgroundColor: '#fff',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 3,
  },
  statLabel: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    borderWidth: 2,
    borderColor: '#000',
    backgroundColor: 'transparent',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
  },
  avatarText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#e0fe10',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#e0fe10',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#232323',
  },
  formContainer: {
    flex: 1,
    width: '100%',
    backgroundColor: '#333',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
    width: '100%',
    marginTop: 25,
  },
  menuOrFormContainer: {
    flex: 1,
    backgroundColor: '#232323',
    width: '100%',
  },
  formGroup: {
    paddingLeft: 20,
    paddingRight: 20,
    marginBottom: 20,
    width: '100%',
  },
  formTitle: {
    color: '#e0fe10',
    fontSize: 16,
    marginLeft: 20,
    marginBottom: 5,
    fontWeight: 'bold',
  },
  label: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 8,
    fontWeight: 'bold',
    marginLeft: 2,
  },
  input: {
    backgroundColor: '#ffffff',
    color: '#232323',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  inputHint: {
    color: '#aaa',
    fontSize: 12,
    marginTop: 5,
    marginLeft: 5,
  },
  saveButton: {
    backgroundColor: '#e0fe10',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    width: '90%',
    alignSelf: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#999',
  },
  saveButtonText: {
    color: '#232323',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EditProfileScreen;
