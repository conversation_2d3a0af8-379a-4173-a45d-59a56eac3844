import { 
  List, 
  Datagrid, 
  TextField, 
  Edit, 
  SimpleForm, 
  TextInput,
  Create,
  ImageField,
  ImageInput,
  ReferenceArrayField,
  SingleFieldList,
  ChipField,
  ReferenceArrayInput,
  AutocompleteArrayInput,
  required,
  ReferenceInput,
  AutocompleteInput,
  SelectInput,
  TabbedForm,
  FormTab
} from 'react-admin';

// Lista de Boxes
export const BoxList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="nombre" />
      <TextField source="ubicacion" label="Ubicación" />
      <TextField source="estado" />
      <ReferenceArrayField source="miembros" reference="users" label="Atletas">
        <SingleFieldList>
          <ChipField source="nombre" />
        </SingleFieldList>
      </ReferenceArrayField>
    </Datagrid>
  </List>
);

// Componente de edición de boxes
export const BoxEdit = () => (
  <Edit>
    <TabbedForm>
      <FormTab label="Información básica" icon={<span>🏢</span>}>
        <TextInput disabled source="id" />
        <TextInput source="nombre" validate={[required()]} fullWidth />
        <TextInput source="ubicacion" label="Ubicación" validate={[required()]} multiline rows={3} fullWidth />
        <TextInput source="descripcion" multiline rows={5} fullWidth />
      </FormTab>

      <FormTab label="Imagen y estado" icon={<span>📷</span>}>
        <ImageInput 
          source="logo" 
          label="Logo" 
          accept={{
            'image/*': []
          }}
        >
          <ImageField source="src" title="title" />
        </ImageInput>
        
        <SelectInput source="estado" choices={[
          { id: 'Activo', name: 'Activo' },
          { id: 'Inactivo', name: 'Inactivo' },
        ]} defaultValue="Activo" fullWidth />
      </FormTab>

      <FormTab label="Miembros" icon={<span>👥</span>}>
        <ReferenceInput source="ownerId" reference="users" label="Dueño del box" required>
          <AutocompleteInput optionText="nombre" fullWidth />
        </ReferenceInput>
        
        <ReferenceArrayInput source="miembros" reference="users" label="Atletas">
          <AutocompleteArrayInput optionText="nombre" fullWidth />
        </ReferenceArrayInput>
      </FormTab>
    </TabbedForm>
  </Edit>
);

// Componente de creación de boxes
export const BoxCreate = () => (
  <Create>
    <TabbedForm>
      <FormTab label="Información básica" icon={<span>🏢</span>}>
        <TextInput source="nombre" validate={[required()]} fullWidth />
        <TextInput source="ubicacion" label="Ubicación" validate={[required()]} multiline rows={3} fullWidth />
        <TextInput source="descripcion" multiline rows={5} fullWidth />
      </FormTab>

      <FormTab label="Imagen y estado" icon={<span>📷</span>}>
        <ImageInput 
          source="logo" 
          label="Logo" 
          accept={{
            'image/*': []
          }}
        >
          <ImageField source="src" title="title" />
        </ImageInput>
        
        <SelectInput source="estado" choices={[
          { id: 'Activo', name: 'Activo' },
          { id: 'Inactivo', name: 'Inactivo' },
        ]} defaultValue="Activo" fullWidth />
      </FormTab>

      <FormTab label="Miembros" icon={<span>👥</span>}>
        <ReferenceInput source="ownerId" reference="users" label="Dueño del box" required>
          <AutocompleteInput optionText="nombre" fullWidth />
        </ReferenceInput>
        
        <ReferenceArrayInput source="miembros" reference="users" label="Atletas">
          <AutocompleteArrayInput optionText="nombre" fullWidth />
        </ReferenceArrayInput>
      </FormTab>
    </TabbedForm>
  </Create>
);
