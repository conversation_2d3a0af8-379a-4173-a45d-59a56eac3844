import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const ParticipantCard = ({ participant, rank, onPress }) => {
  const renderScore = () => {
    if (!participant.score) return <Text style={styles.score}>No score</Text>;

    const { scoring_method } = participant.pvp_event?.data?.attributes || participant.pvp_event;
    const score = participant.score;
    
    switch (scoring_method) {
      case 'Time':
        return (
          <Text style={styles.score}>
            {score.time || 'Not completed'}
          </Text>
        );
      case 'Rounds + Reps':
        return (
          <Text style={styles.score}>
            {score.rounds} rounds {score.reps} reps
          </Text>
        );
      case 'Total Reps':
        return (
          <Text style={styles.score}>
            {score.reps} reps
          </Text>
        );
      case 'Weight':
        return (
          <Text style={styles.score}>
            {score.weight}kg
          </Text>
        );
      case 'Distance':
        return (
          <Text style={styles.score}>
            {score.distance}m
          </Text>
        );
      default:
        return <Text style={styles.score}>-</Text>;
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.rankContainer}>
        <Text style={styles.rank}>#{rank}</Text>
      </View>

      <Image
        source={
          participant.profile_pic_url
            ? { uri: participant.profile_pic_url }
            : require('../assets/default-game.png')
        }
        style={styles.avatar}
      />

      <View style={styles.infoContainer}>
        <Text style={styles.name}>{participant.participant_name}</Text>
        <Text style={styles.sport}>{participant.sport || 'CrossFit'}</Text>
      </View>

      <View style={styles.scoreContainer}>
        {renderScore()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  rankContainer: {
    width: 30,
    alignItems: 'center',
  },
  rank: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginHorizontal: 12,
  },
  infoContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  sport: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  scoreContainer: {
    minWidth: 80,
    alignItems: 'flex-end',
  },
  score: {
    fontSize: 14,
    fontWeight: '500',
    color: '#eb8d28',
  },
});

export default ParticipantCard;
