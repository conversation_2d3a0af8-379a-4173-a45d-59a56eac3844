import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import ParticipantCard from '../components/ParticipantCard';
import pvpEventApi from '../api/pvpEventApi';
import Toast from 'react-native-toast-message';

const ParticipantsScreen = ({ route, navigation }) => {
  const { eventId } = route.params;
  const [participants, setParticipants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadParticipants = async () => {
    try {
      setLoading(true);
      const data = await pvpEventApi.fetchParticipants(eventId);
      setParticipants(data);
    } catch (error) {
      console.error('Error loading participants:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load participants. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadParticipants();
    setRefreshing(false);
  };

  useEffect(() => {
    loadParticipants();
  }, [eventId]);

  const handleParticipantPress = (participant) => {
    navigation.navigate('ScoreSubmission', {
      eventId,
      participantId: participant.id,
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#eb8d28" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={participants}
        renderItem={({ item }) => (
          <ParticipantCard
            participant={item}
            onPress={() => handleParticipantPress(item)}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#eb8d28']}
            tintColor="#eb8d28"
          />
        }
        ListEmptyComponent={
          <Text style={styles.emptyText}>
            No participants found for this event
          </Text>
        }
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    flexGrow: 1,
    padding: 16,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginTop: 24,
  },
});

export default ParticipantsScreen;
