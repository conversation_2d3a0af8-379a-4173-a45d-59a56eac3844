# 📊 The WOD League - Resumen de Funcionalidades y Tests (NestJS)

## 📱 Funcionalidades de la Aplicación y Endpoints Requeridos

Este documento presenta un resumen completo de todas las funcionalidades que los usuarios pueden realizar en The WOD League, agrupadas por módulos, junto con los endpoints RESTful que el frontend necesitaría para interactuar con el backend NestJS. Los endpoints siguen las mejores prácticas de NestJS, incluyendo la organización por recursos y el uso de DTOs para la validación de datos.

> **NOTA**: Este documento distingue entre funcionalidades de implementación actual (Módulos de Autenticación, Usuarios, Boxes, Ligas, WODs, Resultados, Clasificaciones, Inscripciones y PVP) y funcionalidades planificadas para futuras releases (marcadas en la última sección).

## Colores
- e0fe10
- 2e2e2e
- ffffff
- 232323
- 598392

## 📌 IMPLEMENTACIÓN ACTUAL

### 🧑‍💼 Módulo de Autenticación

#### Gestión de Cuenta y Autenticación

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Estado del servicio | `/api/auth` | GET | Verificar el estado del servicio de autenticación |
| Registro de usuario | `/api/auth/register` | POST | Crear una nueva cuenta de usuario con datos básicos |
| Inicio de sesión | `/api/auth/login` | POST | Autenticar usuario y obtener token JWT |
| Envío de email de verificación | `/api/auth/send-verification-email` | POST | Enviar un correo de verificación al usuario |
| Verificación de email | `/api/auth/verify-email` | POST | Verificar cuenta mediante token recibido por email |
| Estado de verificación | `/api/auth/email-verification-status` | GET | Verificar si el email del usuario está confirmado |
| Estado de verificación (alt) | `/api/auth/verify-status` | GET | Endpoint alternativo para verificar estado de email |
| Recuperación de contraseña | `/api/auth/forgot-password` | POST | Solicitar un email de recuperación de contraseña |
| Reseteo de contraseña | `/api/auth/reset-password` | POST | Establecer una nueva contraseña mediante token |
| Cambiar contraseña | `/api/auth/change-password` | POST | Modificar contraseña actual estando autenticado |

### 🧑‍💼 Módulo de Usuarios

#### Gestión de Usuarios

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear usuario | `/api/usuarios` | POST | Crear un nuevo usuario (solo admins) |
| Listar usuarios | `/api/usuarios` | GET | Obtener lista de todos los usuarios (solo admins) |
| Obtener usuario | `/api/usuarios/:id` | GET | Obtener información de un usuario específico |
| Actualizar usuario | `/api/usuarios/:id` | PATCH | Actualizar datos de un usuario específico |
| Eliminar usuario | `/api/usuarios/:id` | DELETE | Eliminar un usuario (solo admins) |

#### Gestión de Perfil

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Obtener perfil | `/api/perfil` | GET | Obtener datos del perfil del usuario autenticado |
| Actualizar perfil | `/api/perfil` | PATCH | Actualizar datos del perfil del usuario autenticado |
| Actualizar foto de perfil | `/api/perfil/photo` | POST | Subir o actualizar foto de perfil |



## 📌 IMPLEMENTACIÓN ACTUAL

### 🏋️ Módulo de Boxes

#### Gestión de Boxes

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear box | `/api/boxes` | POST | Crear un nuevo box de CrossFit |
| Listar boxes | `/api/boxes` | GET | Obtener lista de todos los boxes registrados |
| Obtener box | `/api/boxes/:id` | GET | Obtener información detallada de un box específico |
| Actualizar box | `/api/boxes/:id` | PATCH | Actualizar información de un box específico |
| Eliminar box | `/api/boxes/:id` | DELETE | Eliminar un box del sistema |
| Boxes por propietario | `/api/boxes/owner/:ownerId` | GET | Obtener boxes de un propietario específico |
| Miembros de un box | `/api/boxes/:id/miembros` | GET | Obtener lista de miembros de un box específico |



### 🏆 Módulo de Ligas

#### Gestión de Ligas

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear liga | `/api/ligas` | POST | Crear una nueva liga de competición (solo admins) |
| Listar ligas | `/api/ligas` | GET | Obtener todas las ligas disponibles |
| Ligas activas | `/api/ligas/activas` | GET | Obtener ligas actualmente en curso |
| Ligas próximas | `/api/ligas/proximas` | GET | Obtener ligas programadas a futuro |
| Obtener liga | `/api/ligas/:id` | GET | Obtener información detallada de una liga específica |
| Actualizar liga | `/api/ligas/:id` | PATCH | Modificar datos de una liga (solo admins) |
| Eliminar liga | `/api/ligas/:id` | DELETE | Eliminar una liga (solo admins) |
| Activar liga | `/api/ligas/:id/activar` | POST | Cambiar estado de liga a activa (solo admins) |
| Finalizar liga | `/api/ligas/:id/finalizar` | POST | Marcar una liga como finalizada (solo admins) |

### 🏅 Módulo de Clasificaciones

#### Gestión de Clasificaciones

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Obtener clasificación | `/api/clasificaciones/liga/:ligaId/categoria/:categoria` | GET | Obtener clasificación actual de una liga por categoría |
| Actualizar clasificación | `/api/clasificaciones/liga/:ligaId/categoria/:categoria/actualizar` | POST | Recalcular clasificación (solo admins) |
| Clasificación histórica | `/api/clasificaciones/liga/:ligaId/categoria/:categoria/historica` | GET | Obtener evolución histórica de clasificación |
| Clasificación por usuario | `/api/clasificaciones/usuario/:usuarioId` | GET | Obtener clasificaciones históricas de un usuario |
| Actualizar todas | `/api/clasificaciones/actualizar-todas` | POST | Actualizar todas las clasificaciones (solo admins) |

### 🏋️ Módulo de WODs

#### Gestión de WODs

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear WOD | `/api/wods` | POST | Crear un nuevo WOD para una liga (solo admins) |
| Listar WODs | `/api/wods` | GET | Obtener todos los WODs registrados |
| WODs activos | `/api/wods/activos` | GET | Obtener WODs actualmente abiertos |
| WODs por liga | `/api/wods/liga/:ligaId` | GET | Obtener WODs de una liga específica |
| Obtener WOD | `/api/wods/:id` | GET | Obtener información detallada de un WOD específico |
| Actualizar WOD | `/api/wods/:id` | PATCH | Modificar datos de un WOD (solo admins) |
| Eliminar WOD | `/api/wods/:id` | DELETE | Eliminar un WOD (solo admins) |
| Publicar WOD | `/api/wods/:id/publicar` | POST | Hacer un WOD visible para los atletas (solo admins) |
| Cerrar WOD | `/api/wods/:id/cerrar` | POST | Cerrar un WOD para nuevos resultados (solo admins) |

### 📊 Módulo de Resultados

#### Gestión de Resultados

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Registrar resultado | `/api/resultados` | POST | Registrar el resultado de un atleta en un WOD |
| Listar resultados | `/api/resultados` | GET | Obtener todos los resultados registrados |
| Obtener resultado | `/api/resultados/:id` | GET | Obtener detalles de un resultado específico |
| Actualizar resultado | `/api/resultados/:id` | PATCH | Modificar un resultado existente |
| Eliminar resultado | `/api/resultados/:id` | DELETE | Eliminar un resultado (solo admins) |
| Validar resultado | `/api/resultados/:id/validar` | POST | Validar un resultado (admins o dueños de box) |
| Marcar resultado | `/api/resultados/:id/flag` | POST | Reportar un resultado como sospechoso |
| Calcular puntuación | `/api/resultados/:id/calcular-puntuacion` | POST | Calcular puntuación de un resultado (solo admins) |
| Detectar outliers | `/api/resultados/detectar-outliers` | POST | Identificar resultados estadísticamente anómalos (solo admins) |

#### Consultas de Resultados

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Por usuario | `/api/resultados/usuario/:usuarioId` | GET | Obtener resultados de un usuario específico |
| Por WOD | `/api/resultados/wod/:wodId` | GET | Obtener todos los resultados de un WOD específico |
| Por usuario y WOD | `/api/resultados/usuario/:usuarioId/wod/:wodId` | GET | Obtener el resultado de un usuario en un WOD específico |

### 📝 Módulo de Inscripciones

#### Gestión de Inscripciones

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear inscripción | `/api/inscripciones` | POST | Inscribir a un usuario en una liga |
| Listar inscripciones | `/api/inscripciones` | GET | Obtener todas las inscripciones (solo admins) |
| Ver inscripción | `/api/inscripciones/:id` | GET | Obtener detalles de una inscripción específica |
| Actualizar inscripción | `/api/inscripciones/:id` | PATCH | Modificar datos de una inscripción (solo admins) |
| Eliminar inscripción | `/api/inscripciones/:id` | DELETE | Eliminar una inscripción (solo admins) |
| Confirmar pago | `/api/inscripciones/:id/confirmar-pago` | POST | Confirmar pago de una inscripción (solo admins) |
| Cancelar inscripción | `/api/inscripciones/:id/cancelar` | POST | Cancelar una inscripción |
| Rechazar inscripción | `/api/inscripciones/:id/rechazar` | POST | Rechazar una inscripción (solo admins) |

#### Consultas de Inscripciones

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Por estado | `/api/inscripciones/estado/:estado` | GET | Filtrar inscripciones por estado (solo admins) |
| Pendientes de pago | `/api/inscripciones/pendientes` | GET | Listar inscripciones pendientes de pago (solo admins) |
| Inscripciones recientes | `/api/inscripciones/recientes` | GET | Listar inscripciones recientes (solo admins) |
| Por usuario | `/api/inscripciones/usuario/:usuarioId` | GET | Obtener inscripciones de un usuario específico |
| Por liga | `/api/inscripciones/liga/:ligaId` | GET | Obtener inscripciones para una liga específica (solo admins) |
| Por usuario y liga | `/api/inscripciones/usuario/:usuarioId/liga/:ligaId` | GET | Verificar inscripción de un usuario en una liga específica |
| Estadísticas por liga | `/api/inscripciones/liga/:ligaId/estadisticas` | GET | Obtener estadísticas de inscripciones por liga (solo admins) |
| Por categoría | `/api/inscripciones/liga/:ligaId/categorias` | GET | Contar inscripciones por categoría (solo admins) |



## 🧪 Pruebas de Backend

Este proyecto sigue buenas prácticas de testing siguiendo el patrón AAA (Arrange-Act-Assert) e implementando pruebas con bases de datos reales para asegurar que los endpoints documentados arriba funcionen correctamente en un entorno similar a producción.

Las pruebas están diseñadas para garantizar:

- **Aislamiento**: Cada test es completamente independiente
- **Uso de base de datos real**: Se evitan mocks y simulaciones
- **Limpieza de datos**: Después de cada test para mantener el aislamiento
- **Detección de problemas de integración**: Al probar con conexiones reales

## 🗂️ Observaciones y Recomendaciones

- Los tests de Box están completamente implementados
- Se ha avanzado significativamente en los tests de Resultados y Clasificación
- Las funcionalidades de PVP, Battle Box y Eventos Especiales no tienen tests implementados
- Se recomienda priorizar los tests de Flujo Completo para garantizar la integración correcta de los módulos
- La implementación de los tests de Usuario debe completarse, especialmente los relacionados con autenticación
- Los tests de Utilidades son fundamentales para garantizar el correcto funcionamiento de los cálculos y validaciones

## 🔜 FUTURAS RELEASES

### 🥊 Battle Box

#### Gestión de Batalla entre Boxes

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear desafío entre boxes | `/api/battle-box` | POST | Crear un nuevo desafío entre boxes |
| Listar desafíos | `/api/battle-box` | GET | Obtener todos los desafíos disponibles |
| Obtener desafío | `/api/battle-box/:id` | GET | Obtener detalles de un desafío específico |
| Actualizar desafío | `/api/battle-box/:id` | PATCH | Modificar un desafío existente |
| Eliminar desafío | `/api/battle-box/:id` | DELETE | Eliminar un desafío (solo admins) |
| Aceptar desafío | `/api/battle-box/:id/aceptar` | POST | Aceptar un desafío recibido |
| Rechazar desafío | `/api/battle-box/:id/rechazar` | POST | Rechazar un desafío recibido |
| Enviar resultado | `/api/battle-box/:id/resultado` | POST | Enviar resultado de un box para el desafío |

### 🎖️ Módulo de Eventos Especiales y Badges

#### Gestión de Eventos

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear evento especial | `/api/eventos` | POST | Crear un nuevo evento temático (solo admins) |
| Listar eventos | `/api/eventos` | GET | Obtener todos los eventos disponibles |
| Obtener evento | `/api/eventos/:id` | GET | Obtener detalles de un evento específico |
| Actualizar evento | `/api/eventos/:id` | PATCH | Modificar un evento existente (solo admins) |
| Eliminar evento | `/api/eventos/:id` | DELETE | Eliminar un evento (solo admins) |
| Inscribirse a evento | `/api/eventos/:id/inscribir` | POST | Inscribirse en un evento especial |
| Enviar resultado | `/api/eventos/:id/resultado` | POST | Enviar resultado para un evento |
| Lista de participantes | `/api/eventos/:id/participantes` | GET | Obtener lista de participantes en un evento |

#### Gestión de Badges

| Funcionalidad | Endpoint | Método | Descripción |
|---------------|----------|--------|-------------|
| Crear badge | `/api/badges` | POST | Crear un nuevo badge (solo admins) |
| Listar badges | `/api/badges` | GET | Obtener todos los badges disponibles |
| Obtener badge | `/api/badges/:id` | GET | Obtener detalles de un badge específico |
| Actualizar badge | `/api/badges/:id` | PATCH | Modificar un badge existente (solo admins) |
| Eliminar badge | `/api/badges/:id` | DELETE | Eliminar un badge (solo admins) |
| Asignar badge | `/api/badges/:id/asignar/:usuarioId` | POST | Asignar un badge a un usuario (solo admins) |
| Badges de un usuario | `/api/usuarios/:id/badges` | GET | Obtener todos los badges de un usuario |

Este documento se actualizará a medida que se implementen nuevos tests y funcionalidades.
