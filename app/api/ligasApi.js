import { NESTJS_URL } from '../config';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Obtener token de autenticación
 * @returns {Promise<string>} Token JWT
 */
const getAuthToken = async () => {
  try {
    const auth = await AsyncStorage.getItem('auth');
    if (!auth) {
      console.warn('No se encontró token de autenticación');
      throw new Error('No hay token disponible');
    }
    
    const parsed = JSON.parse(auth);
    return parsed.token;
  } catch (error) {
    console.error('Error obteniendo token de autenticación:', error);
    throw error;
  }
};

/**
 * Helper function to handle API responses
 * @param {Response} response - Respuesta HTTP
 * @returns {Promise<Object>} Datos procesados
 */
const handleResponse = async (response) => {
  if (!response.ok) {
    const error = await response.text();
    console.error('API Error Response:', error);
    throw new Error(error);
  }
  const data = await response.json();
  return data;
};

/**
 * Fetch all upcoming leagues
 * @returns {Promise<Array>} Array of upcoming leagues
 */
const fetchProximasLigas = async () => {
  try {
    // Intentar obtener token de autenticación si está disponible
    let headers = {
      'Content-Type': 'application/json',
    };
    
    try {
      const token = await getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (authError) {
      // La autenticación es opcional para este endpoint
      console.log('No hay token disponible, continuando sin autenticación');
    }
    
    const response = await fetch(
      `${NESTJS_URL}/api/ligas/proximas`,
      {
        method: 'GET',
        headers,
      }
    );
    const data = await handleResponse(response);
    return data || [];
  } catch (error) {
    console.error('Error fetching próximas ligas:', error);
    throw error;
  }
};

/**
 * Fetch all active leagues 
 * @returns {Promise<Array>} Array of active leagues
 */
const fetchLigasActivas = async () => {
  try {
    // Intentar obtener token de autenticación si está disponible
    let headers = {
      'Content-Type': 'application/json',
    };
    
    try {
      const token = await getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (authError) {
      // La autenticación es opcional para este endpoint
      console.log('No hay token disponible, continuando sin autenticación');
    }
    
    const response = await fetch(
      `${NESTJS_URL}/api/ligas/activas`,
      {
        method: 'GET',
        headers,
      }
    );
    const data = await handleResponse(response);
    return data || [];
  } catch (error) {
    console.error('Error fetching ligas activas:', error);
    throw error;
  }
};

/**
 * Fetch a specific league by ID
 * @param {string} ligaId - League ID
 * @returns {Promise<Object>} League data
 */
const fetchLigaById = async (ligaId) => {
  try {
    // Intentar obtener token de autenticación si está disponible
    let headers = {
      'Content-Type': 'application/json',
    };
    
    try {
      const token = await getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (authError) {
      // La autenticación es opcional para este endpoint
      console.log('No hay token disponible, continuando sin autenticación');
    }
    
    console.log('Fetching liga by ID:', ligaId);
    const response = await fetch(
      `${NESTJS_URL}/api/ligas/${ligaId}`,
      {
        method: 'GET',
        headers,
      }
    );
    const data = await handleResponse(response);
    console.log('Liga detail response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching liga:', error);
    throw error;
  }
};

/**
 * Fetch the currently active league (single one)
 * This would need to be implemented in the NestJS backend
 * @returns {Promise<Object>} Current active league data
 */
const fetchLigaActual = async () => {
  try {
    // Intentar obtener token de autenticación si está disponible
    let headers = {
      'Content-Type': 'application/json',
    };
    
    try {
      const token = await getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (authError) {
      // La autenticación es opcional para este endpoint
      console.log('No hay token disponible, continuando sin autenticación');
    }
    
    // Por ahora, obtener la primera liga activa
    const ligas = await fetchLigasActivas();
    if (ligas && ligas.length > 0) {
      return ligas[0];
    }
    return null;
  } catch (error) {
    console.error('Error fetching liga actual:', error);
    throw error;
  }
};

export default {
  fetchProximasLigas,
  fetchLigasActivas,
  fetchLigaById,
  fetchLigaActual,
};
