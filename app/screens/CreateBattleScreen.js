import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Share, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STRAPI_URL } from '../config';

const CreateBattleScreen = ({ route, navigation }) => {
  const { wod } = route.params;
  const [isCreating, setIsCreating] = useState(false);

  const getStrapiToken = async (email, password) => {
    try {
      const response = await axios.post(`${STRAPI_URL}/api/auth/local`, {
        identifier: email,
        password: password
      });
      return response.data.jwt;
    } catch (error) {
      console.error('Error getting Strapi token:', error.response?.data || error);
      throw error;
    }
  };

  const createBattle = async () => {
    try {
      setIsCreating(true);

      // Obtener todos los datos almacenados
      const keys = ['userEmail', 'userPassword', 'userName'];
      const results = await AsyncStorage.multiGet(keys);
      const storedData = Object.fromEntries(results);

      console.log('Retrieved stored data:', {
        email: storedData.userEmail,
        userName: storedData.userName,
        hasPassword: !!storedData.userPassword
      });

      if (!storedData.userEmail || !storedData.userPassword) {
        throw new Error('No se encontraron las credenciales necesarias');
      }

      const strapiJwt = await getStrapiToken(storedData.userEmail, storedData.userPassword);
      const battleData = {
        data: {
          titulo: `Batalla vs ${storedData.userName}`,
          wodTitle: wod.title,
          wod: wod.id  // Usar el ID directamente de la API
        }
      };

      console.log('Creating battle with data:', battleData);
      const response = await axios.post(`${STRAPI_URL}/api/battles`, battleData, {
        headers: {
          'Authorization': `Bearer ${strapiJwt}`,
          'Content-Type': 'application/json'
        }
      });

      const battleId = response.data.data.id;
      const battleLink = `thewodleague://battle/${battleId}`;
      shareBattle(battleLink, wod.title, battleId);
    } catch (error) {
      console.error('Error creating battle:', error.response?.data || error.message);
      Alert.alert(
        'Error',
        error.response?.data?.error?.message || 'No se pudo crear la batalla. Inténtalo de nuevo.'
      );
    } finally {
      setIsCreating(false);
    }
  };

  const shareBattle = async (battleLink, wodTitle, battleId) => {
    try {
      const result = await Share.share({
        message: `¡Te desafío a una batalla en The Wod League! ${wodTitle}\n\nÚnete aquí: ${battleLink}`,
      });

      if (result.action === Share.sharedAction) {
        navigation.navigate('BattleDetails', { battleId: battleId });
      }
    } catch (error) {
      Alert.alert('Error', 'No se pudo compartir la batalla');
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4a90e2', '#357abd']}
        style={styles.header}
      >
        <Text style={styles.title}>Nueva Batalla</Text>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.wodInfo}>
          <Text style={styles.wodTitle}>{wod.title}</Text>
          <Text style={styles.wodDetails}>Nivel: {wod.level}</Text>
          <Text style={styles.wodDetails}>Duración: {wod.duration} minutos</Text>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoText}>
            Al crear una batalla, podrás desafiar a otros atletas a completar este WOD.
            La batalla estará activa durante 7 días.
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.createButton, isCreating && styles.createButtonDisabled]}
          onPress={createBattle}
          disabled={isCreating}
        >
          <Text style={styles.createButtonText}>
            {isCreating ? 'Creando batalla...' : 'Crear y compartir batalla'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    padding: 20,
  },
  wodInfo: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  wodTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  wodDetails: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  infoSection: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  createButton: {
    backgroundColor: '#eb8d28',
    borderRadius: 25,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default CreateBattleScreen;
