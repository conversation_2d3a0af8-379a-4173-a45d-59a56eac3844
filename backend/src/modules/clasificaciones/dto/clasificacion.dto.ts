import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsString, IsUUID } from 'class-validator';

export class ClasificacionDto {
  @IsNotEmpty()
  @IsUUID()
  usuarioId: string;

  @IsNotEmpty()
  @IsString()
  nombre: string;

  @IsNotEmpty()
  @IsString()
  alias: string;

  @IsNotEmpty()
  @IsString()
  box: string;

  @IsNotEmpty()
  @IsNumber()
  puntuacionTotal: number;

  @IsNotEmpty()
  @IsNumber()
  bonusConsistencia: number;

  @IsNotEmpty()
  @IsNumber()
  wodsCompletados: number;

  @IsNotEmpty()
  @IsNumber()
  wodsTotales: number;

  @IsNotEmpty()
  @IsNumber()
  posicion: number;

  @IsNotEmpty()
  @IsNumber()
  posicionAnterior: number;
}
