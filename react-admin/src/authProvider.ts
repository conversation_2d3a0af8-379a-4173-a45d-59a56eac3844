import { AuthProvider } from 'react-admin';

// URL base de la API
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

const authProvider: AuthProvider = {
    // Método para iniciar sesión
    login: ({ username, password }) => {
        const request = new Request(`${API_URL}/api/auth/login`, {
            method: 'POST',
            body: JSON.stringify({ email: username, password }),
            headers: new Headers({ 'Content-Type': 'application/json' }),
        });
        return fetch(request)
            .then(response => {
                if (response.status < 200 || response.status >= 300) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .then(auth => {
                // Almacenar el token JWT en localStorage
                localStorage.setItem('auth', JSON.stringify(auth));
                localStorage.setItem('token', auth.access_token);
                return Promise.resolve();
            })
            .catch(() => {
                throw new Error('Error de autenticación');
            });
    },
    
    // Método para cerrar sesión
    logout: () => {
        localStorage.removeItem('auth');
        localStorage.removeItem('token');
        return Promise.resolve();
    },
    
    // Comprobar error de autenticación
    checkError: (error) => {
        const status = error.status;
        if (status === 401 || status === 403) {
            localStorage.removeItem('auth');
            localStorage.removeItem('token');
            return Promise.reject();
        }
        return Promise.resolve();
    },
    
    // Comprobar autenticación al cargar una página
    checkAuth: () => {
        return localStorage.getItem('token')
            ? Promise.resolve()
            : Promise.reject();
    },
    
    // Obtener permisos del usuario
    getPermissions: () => {
        const auth = localStorage.getItem('auth');
        if (!auth) {
            return Promise.reject('No auth');
        }
        const { roles } = JSON.parse(auth);
        return Promise.resolve(roles);
    }
};

export default authProvider;
