import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LigasService } from './ligas.service';
import { LigasController } from './ligas.controller';
import { Liga } from './entities/liga.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Liga])],
  controllers: [LigasController],
  providers: [LigasService],
  exports: [LigasService],
})
export class LigasModule {}
