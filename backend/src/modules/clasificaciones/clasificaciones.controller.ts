import { Controller, Get, Post, Param, UseGuards, Query } from '@nestjs/common';
import { ClasificacionesService } from './clasificaciones.service';
import { ClasificacionDto } from './dto/clasificacion.dto';
import { Clasificacion } from './entities/clasificacion.entity';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RolUsuario } from '../usuarios/entities/usuario.entity';

@Controller('clasificaciones')
export class ClasificacionesController {
  constructor(private readonly clasificacionesService: ClasificacionesService) {}

  @Get('liga/:ligaId/categoria/:categoria')
  obtenerClasificacion(
    @Param('ligaId') ligaId: string,
    @Param('categoria') categoria: string,
  ): Promise<ClasificacionDto[]> {
    return this.clasificacionesService.obtenerClasificacion(ligaId, categoria);
  }

  @Post('liga/:ligaId/categoria/:categoria/actualizar')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  actualizarClasificacion(
    @Param('ligaId') ligaId: string,
    @Param('categoria') categoria: string,
  ): Promise<void> {
    return this.clasificacionesService.actualizarClasificacion(ligaId, categoria);
  }

  @Get('liga/:ligaId/categoria/:categoria/historica')
  obtenerClasificacionHistorica(
    @Param('ligaId') ligaId: string,
    @Param('categoria') categoria: string,
    @Query('semana') semana?: number,
  ): Promise<Clasificacion[]> {
    return this.clasificacionesService.obtenerClasificacionHistorica(ligaId, categoria, semana);
  }

  @Get('usuario/:usuarioId')
  obtenerClasificacionUsuario(@Param('usuarioId') usuarioId: string): Promise<Clasificacion[]> {
    return this.clasificacionesService.obtenerClasificacionUsuario(usuarioId);
  }

  @Post('actualizar-todas')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RolUsuario.ADMIN)
  actualizarTodasLasClasificaciones(): Promise<void> {
    return this.clasificacionesService.actualizarTodasLasClasificaciones();
  }
}
