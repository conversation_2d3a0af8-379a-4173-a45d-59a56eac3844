import { Logger, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../../modules/auth/auth.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from '../../modules/auth/dto/login.dto';
import { DatabaseTestHelper, UsuariosTestHelper, AuthTestHelper, usuariosFixtures } from '../helper';
import * as bcrypt from 'bcrypt';

/**
 * Login Tests
 * 
 * These tests verify the user authentication and login process
 * using a real database connection with DatabaseTestHelper
 */
describe('Iniciar Sesión Tests', () => {
  let authService: AuthService;
  let usuariosService: UsuariosService;
  let jwtService: JwtService;
  let testUserId: string;
  const testPassword = 'TestPassword123!';
  const logger = new Logger('IniciarSesionTest');
  
  // Usamos los fixtures para datos de prueba
  const testUserData = {
    ...usuariosFixtures.defaultUser,
    email: '<EMAIL>',
    nombre: 'Login Test User',
    alias: 'logintest',
  };

  beforeAll(async () => {
    // Arrange: Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Crear primero el módulo base con servicios de usuarios usando el helper
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);
    
    // Crear módulo de autenticación usando el helper especializado
    const authModule = await AuthTestHelper.createAuthTestingModule(usuariosService);
    
    // Obtener las referencias de los servicios
    authService = authModule.get<AuthService>(AuthService);
    jwtService = authModule.get<JwtService>(JwtService);
    
    // Limpiar cualquier usuario de prueba previo
    await UsuariosTestHelper.deleteTestUser(
      usuariosService['usuariosRepository'],
      { email: testUserData.email }
    );
    
    // Crear un usuario de prueba con el helper
    const createdUser = await UsuariosTestHelper.createTestUser(
      usuariosService['usuariosRepository'],
      { ...testUserData, password: testPassword }
    );
    
    testUserId = createdUser.id;
    logger.log(`Usuario de prueba creado con ID: ${testUserId}`);
  });
  
  afterAll(async () => {
    // Limpiar datos de prueba usando el helper
    await UsuariosTestHelper.deleteTestUser(
      usuariosService['usuariosRepository'],
      { id: testUserId }
    );
    logger.log('Datos de prueba limpiados correctamente');
  });

  // Test para iniciar sesión con credenciales válidas
  it('debe permitir iniciar sesión con credenciales válidas', async () => {
    // Arrange: Preparar credenciales de login
    const loginDto: LoginDto = {
      email: testUserData.email,
      password: testPassword,
    };

    // Act: Intentar iniciar sesión con credenciales válidas
    const result = await authService.login(loginDto);

    // Assert: Verificar que el inicio de sesión fue exitoso
    expect(result).toBeDefined();
    expect(result.access_token).toBeDefined();
    expect(typeof result.access_token).toBe('string');
    expect(result.access_token.length).toBeGreaterThan(10); // El token debe ser una cadena sustancial
    
    // Verificar datos del usuario en la respuesta
    expect(result.user).toBeDefined();
    expect(result.user.id).toBe(testUserId);
    expect(result.user.email).toBe(testUserData.email);
    expect(result.user.nombre).toBe(testUserData.nombre);
    expect(result.user.rol).toBe(testUserData.rol);
    
    // Verificar que los campos de on-boarding estén presentes en la respuesta del usuario
    expect(result.user).toHaveProperty('emailVerificado');
    expect(result.user).toHaveProperty('setupCompleted');
    expect(result.user).toHaveProperty('onBoarding');
    expect(typeof result.user.setupCompleted).toBe('boolean');
    expect(typeof result.user.onBoarding).toBe('boolean');
    
    // Verificar la estructura del payload del token decodificado
    const decoded = jwtService.decode(result.access_token);
    expect(decoded).toBeDefined();
    expect(decoded['sub']).toBe(testUserId);
    expect(decoded['email']).toBe(testUserData.email);
    
    // Verificar que los campos de on-boarding estén presentes en el token
    expect(decoded).toHaveProperty('emailVerificado');
    expect(decoded).toHaveProperty('setupCompleted');
    expect(decoded).toHaveProperty('onBoarding');
    expect(typeof decoded['setupCompleted']).toBe('boolean');
    expect(typeof decoded['onBoarding']).toBe('boolean');
  });

  // Test para iniciar sesión con credenciales inválidas
  it('debe rechazar el inicio de sesión con credenciales inválidas', async () => {
    // Arrange: Preparar credenciales con contraseña incorrecta
    const loginDto: LoginDto = {
      email: testUserData.email,
      password: 'PasswordIncorrecto123!',
    };

    // Act & Assert: Verificar que se rechaza con la excepción correcta
    await expect(authService.login(loginDto)).rejects.toThrow(UnauthorizedException);
  });

  // Test para iniciar sesión con usuario no existente
  it('debe rechazar el inicio de sesión cuando el usuario no existe', async () => {
    // Arrange: Preparar credenciales con email inexistente
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: testPassword,
    };

    // Act & Assert: Verificar que se rechaza con la excepción correcta
    await expect(authService.login(loginDto)).rejects.toThrow(UnauthorizedException);
  });
  
  // Test para verificar que la última conexión se actualiza al iniciar sesión
  it('debe actualizar la fecha de última conexión al iniciar sesión correctamente', async () => {
    // Arrange: Preparar credenciales y obtener datos iniciales del usuario
    const loginDto: LoginDto = {
      email: testUserData.email,
      password: testPassword,
    };
    
    // Obtener el usuario antes del login para capturar su última conexión
    const userBefore = await usuariosService.findByEmail(testUserData.email);
    if (!userBefore) {
      throw new Error('No se pudo encontrar el usuario de prueba antes del login');
    }
    const initialLastLogin = userBefore.ultimaConexion;
    
    // Esperar un momento para garantizar la diferencia de timestamp
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Act: Realizar login
    await authService.login(loginDto);
    
    // Assert: Verificar que la fecha de última conexión se actualizó
    const userAfter = await usuariosService.findByEmail(testUserData.email);
    
    // Verificar que se encontró el usuario después del login
    expect(userAfter).not.toBeNull();
    if (!userAfter) {
      throw new Error('No se pudo encontrar el usuario de prueba después del login');
    }
    
    // La fecha de última conexión debe haber cambiado
    expect(userAfter.ultimaConexion).not.toEqual(initialLastLogin);
    
    // La nueva fecha debe ser posterior a la antigua
    if (initialLastLogin && userAfter.ultimaConexion) {
      expect(userAfter.ultimaConexion.getTime()).toBeGreaterThan(initialLastLogin.getTime());
    }
  });
});
