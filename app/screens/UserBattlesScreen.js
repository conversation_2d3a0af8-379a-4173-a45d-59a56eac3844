import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../context/AuthContextNest';
import userApi from '../api/userApi';

const BattleListItem = ({ battle }) => {
  return (
    <View style={styles.battleItem}>
      <Text style={styles.battleName}>{battle.name}</Text>
      <View style={styles.participantsContainer}>
        <View style={styles.participant}>
          <Text style={[
            styles.participantName,
            battle.winner === battle.player1 && styles.winnerText
          ]}>
            {battle.player1}
          </Text>
          {battle.winner === battle.player1 && (
            <Icon name="crown" size={16} color="#FFD700" style={styles.winnerIcon} />
          )}
        </View>
        <Text style={styles.vsText}>vs</Text>
        <View style={styles.participant}>
          <Text style={[
            styles.participantName,
            battle.winner === battle.player2 && styles.winnerText
          ]}>
            {battle.player2}
          </Text>
          {battle.winner === battle.player2 && (
            <Icon name="crown" size={16} color="#FFD700" style={styles.winnerIcon} />
          )}
        </View>
      </View>
      <Text style={styles.score}>Puntuación: {battle.score}</Text>
    </View>
  );
};

const UserBattlesScreen = () => {
  const [battles, setBattles] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const fetchBattles = async () => {
      try {
        if (!user?.uid) return;
        const userBattles = await userApi.getUserBattles(user.uid);
        setBattles(userBattles);
      } catch (error) {
        console.error('Error fetching battles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBattles();
  }, [user]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4a90e2" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={battles}
        renderItem={({ item }) => <BattleListItem battle={item} />}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  battleItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    padding: 16,
    marginBottom: 12,
  },
  battleName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  participantsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  participant: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    color: '#666',
  },
  winnerText: {
    color: '#4a90e2',
    fontWeight: 'bold',
  },
  winnerIcon: {
    marginLeft: 4,
  },
  vsText: {
    fontSize: 14,
    color: '#999',
    marginHorizontal: 8,
  },
  score: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 8,
  },
});

export default UserBattlesScreen;
