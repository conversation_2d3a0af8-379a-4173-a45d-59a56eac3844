import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authApiNest from '../api/authApiNest';
import Toast from 'react-native-toast-message';

const DeleteAccountScreen = ({ navigation }) => {
  const { user } = useAuth();

  // Estados para el proceso de eliminación
  const [password, setPassword] = useState('');
  const [confirmText, setConfirmText] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Función para eliminar la cuenta
  const handleDeleteAccount = async () => {
    // Validar la confirmación de texto
    if (confirmText !== 'ELIMINAR') {
      Alert.alert('Error', 'Por favor escribe "ELIMINAR" para confirmar');
      return;
    }

    // Validar que se haya ingresado una contraseña
    if (!password) {
      Alert.alert('Error', 'Por favor, introduce tu contraseña para confirmar');
      return;
    }

    // Mostrar alerta de confirmación
    Alert.alert(
      'Eliminar cuenta',
      '¿Estás seguro de que quieres eliminar tu cuenta? Esta acción no se puede deshacer.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            // Iniciar proceso de eliminación
            setIsLoading(true);
            
            try {
              // Llamar al API para eliminar la cuenta
              await authApiNest.deleteAccount(password);
              
              // Limpiar datos locales
              await AsyncStorage.removeItem('auth-token');
              await AsyncStorage.removeItem('refresh-token');
              await AsyncStorage.removeItem('userProfile');
              await AsyncStorage.removeItem('userEmail');
              
              // Navegar a la pantalla de inicio de sesión
              // Usamos navigation.navigate y navigation.reset como respaldo
              try {
                // Opción 1: Reset completo de navegación
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Auth' }]
                });
              } catch (navError) {
                console.log('Error al resetear navegación:', navError);
                
                try {
                  // Opción 2: Navegación simple
                  navigation.navigate('Login');
                } catch (navError2) {
                  console.log('Error alternativo de navegación:', navError2);
                  
                  // Opción 3: Último intento con nombre alternativo
                  try {
                    navigation.navigate('Auth');
                  } catch (finalError) {
                    console.log('No se pudo navegar de ninguna forma:', finalError);
                  }
                }
              }
              
              // Mostrar mensaje de éxito
              Toast.show({
                type: 'success',
                text1: 'Cuenta eliminada',
                text2: 'Tu cuenta ha sido eliminada correctamente',
                visibilityTime: 4000
              });
            } catch (error) {
              console.error('Error al eliminar cuenta:', error);
              
              // Mostrar mensaje de error apropiado
              let errorMessage = 'No se pudo eliminar la cuenta. Inténtalo de nuevo.';
              
              if (error.response) {
                const status = error.response.status;
                
                if (status === 401) {
                  errorMessage = 'La contraseña es incorrecta';
                } else if (status === 403) {
                  errorMessage = 'En este momento no puedes eliminar tu cuenta. Por favor, contacta con soporte.';
                  console.log('Error de permisos:', error.response.data);
                } else if (error.response.data?.message) {
                  errorMessage = error.response.data.message;
                }
              }
              
              Alert.alert('Error', errorMessage);
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Eliminar cuenta</Text>
        </TouchableOpacity>
      </View>

      {/* Delete Account Content */}
      <View style={styles.contentContainer}>
        <View style={styles.warningContainer}>
          <Ionicons name="warning-outline" size={48} color="#e0fe10" />
          <Text style={styles.warningTitle}>Atención</Text>
          <Text style={styles.warningText}>
            Estás a punto de eliminar tu cuenta. Esta acción eliminará permanentemente todos tus datos,
            incluyendo tu perfil, estadísticas y resultados. Esta acción no se puede deshacer.
          </Text>
        </View>

        {/* Campo para escribir ELIMINAR */}
        <View style={styles.passwordContainer}>
          <Text style={styles.passwordLabel}>Escribe "ELIMINAR" para confirmar</Text>
          <View style={styles.passwordInputContainer}>
            <TextInput
              style={styles.passwordInput}
              value={confirmText}
              onChangeText={setConfirmText}
              placeholderTextColor="#999"
              placeholder="ELIMINAR"
            />
          </View>
        </View>

        {/* Campo para la contraseña */}
        <View style={styles.passwordContainer}>
          <Text style={styles.passwordLabel}>Introduce tu contraseña para confirmar</Text>
          <View style={styles.passwordInputContainer}>
            <TextInput
              style={styles.passwordInput}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              placeholderTextColor="#999"
              placeholder="Contraseña"
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Ionicons
                name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                size={24}
                color="#999"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Botón de eliminar cuenta */}
        <TouchableOpacity
          style={[styles.deleteButton, 
            (confirmText !== 'ELIMINAR' || !password || isLoading) && styles.disabledButton]}
          onPress={handleDeleteAccount}
          disabled={confirmText !== 'ELIMINAR' || !password || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <Text style={styles.deleteButtonText}>Eliminar mi cuenta</Text>
          )}
        </TouchableOpacity>
      </View>


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 20,
  },
  warningContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  warningTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginVertical: 10,
  },
  warningText: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    lineHeight: 24,
  },
  passwordContainer: {
    marginBottom: 30,
  },
  passwordLabel: {
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 10,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingHorizontal: 15,
    height: 50,
  },
  passwordInput: {
    flex: 1,
    color: '#232323',
    fontSize: 16,
  },
  eyeIcon: {
    padding: 5,
  },
  deleteButton: {
    backgroundColor: '#ff3b30',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  deleteButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },

});

export default DeleteAccountScreen;
