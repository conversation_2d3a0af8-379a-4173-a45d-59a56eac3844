import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const MiniBattleCard = ({ battle, onPress }) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.card}>
        <View style={styles.wodInfo}>
          <View style={styles.wodHeader}>
            <Text style={styles.wodTitle} numberOfLines={1}>
              {battle.wod?.title || 'WOD no asignado'}
            </Text>
            <View style={styles.levelContainer}>
              <Icon name="fitness-center" size={14} color="#4a90e2" />
              <Text style={styles.levelText}>{battle.wod?.level || 'Nivel'}</Text>
            </View>
          </View>
        </View>

        <View style={styles.content}>
          <View style={styles.playerContainer}>
            <Text style={[styles.playerName, battle.winner === battle.creatorId && styles.winnerText]} numberOfLines={1}>
              {battle.creatorName || 'Player'}
            </Text>
            {battle.winner === battle.creatorId && (
              <Icon name="emoji-events" size={16} color="#FFD700" style={styles.winnerIcon} />
            )}
          </View>

          <View style={styles.vsContainer}>
            <Text style={styles.vsText}>VS</Text>
          </View>

          <View style={styles.playerContainer}>
            <Text style={[styles.playerName, battle.winner === battle.participants?.[0]?.id && styles.winnerText]} numberOfLines={1}>
              {battle.participants?.[0]?.username || 'Waiting...'}
            </Text>
            {battle.winner === battle.participants?.[0]?.id && (
              <Icon name="emoji-events" size={16} color="#FFD700" style={styles.winnerIcon} />
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 250,
    marginHorizontal: 6,
    marginVertical: 6,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  wodInfo: {
    marginBottom: 12,
  },
  wodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  wodTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  levelText: {
    fontSize: 12,
    color: '#4a90e2',
    marginLeft: 4,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  playerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  playerName: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  winnerText: {
    color: '#4a90e2',
    fontWeight: 'bold',
  },
  winnerIcon: {
    marginLeft: 4,
  },
  vsContainer: {
    marginHorizontal: 8,
  },
  vsText: {
    fontSize: 12,
    color: '#999',
  },
});

export default MiniBattleCard;
