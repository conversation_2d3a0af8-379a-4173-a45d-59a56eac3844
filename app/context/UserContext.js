/**
 * UserContext.js
 * Central context for managing user data throughout the application.
 * Implements the single-load pattern with session persistence.
 */
import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NESTJS_URL } from '../config';
import userService from '../services/userService';
import Toast from 'react-native-toast-message';

// Create the context
const UserContext = createContext(null);

// Cache configuration
const CACHE_CONFIG = {
  PROFILE_KEY: 'user_profile',
  STATS_KEY: 'user_stats',
  PROFILE_TTL: 3600000, // 1 hour in milliseconds
  STATS_TTL: 300000,    // 5 minutes in milliseconds
};

/**
 * UserProvider component that wraps the application
 * Manages loading, storing, and refreshing user data
 */
export const UserProvider = ({ children }) => {
  // Main states for user data
  const [userData, setUserData] = useState(null);
  const [userStats, setUserStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Timestamp tracking for cache invalidation
  const [profileLastUpdated, setProfileLastUpdated] = useState(null);
  const [statsLastUpdated, setStatsLastUpdated] = useState(null);

  /**
   * Load initial user data when component mounts
   */
  useEffect(() => {
    loadInitialData();
  }, []);

  /**
   * Load initial user data from storage first, then refresh from API
   */
  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Try to load from AsyncStorage first for immediate display
      await loadFromCache();
      
      // Then fetch fresh data in background
      await refreshUserData(false);
    } catch (error) {
      console.error('Error in initial data loading:', error);
      setError('Error al cargar datos iniciales');
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Load user data from local cache
   */
  const loadFromCache = async () => {
    try {
      // Load profile data
      const profileData = await AsyncStorage.getItem(CACHE_CONFIG.PROFILE_KEY);
      const profileTimestamp = await AsyncStorage.getItem(`${CACHE_CONFIG.PROFILE_KEY}_timestamp`);
      
      if (profileData && profileTimestamp) {
        setUserData(JSON.parse(profileData));
        setProfileLastUpdated(profileTimestamp);
        console.log('✅ User profile loaded from cache');
      }
      
      // Load stats data
      const statsData = await AsyncStorage.getItem(CACHE_CONFIG.STATS_KEY);
      const statsTimestamp = await AsyncStorage.getItem(`${CACHE_CONFIG.STATS_KEY}_timestamp`);
      
      if (statsData && statsTimestamp) {
        setUserStats(JSON.parse(statsData));
        setStatsLastUpdated(statsTimestamp);
        console.log('✅ User stats loaded from cache');
      }
      
      return !!profileData || !!statsData;
    } catch (error) {
      console.error('Error loading from cache:', error);
      return false;
    }
  };

  /**
   * Refresh user data from the API
   * @param {boolean} showFeedback - Whether to show loading/success feedback to user
   */
  const refreshUserData = async (showFeedback = true) => {
    try {
      if (showFeedback) {
        setIsLoading(true);
      }
      
      // Get authentication token
      const authToken = await AsyncStorage.getItem('auth-token');
      if (!authToken) {
        throw new Error('No authentication token found');
      }
      
      // Check if we should refresh profile (based on TTL)
      const now = Date.now();
      const shouldRefreshProfile = !profileLastUpdated || 
        now - new Date(profileLastUpdated).getTime() > CACHE_CONFIG.PROFILE_TTL;
      
      // Check if we should refresh stats (based on TTL)
      const shouldRefreshStats = !statsLastUpdated || 
        now - new Date(statsLastUpdated).getTime() > CACHE_CONFIG.STATS_TTL;
      
      // Refresh profile if needed
      if (shouldRefreshProfile) {
        const freshUserData = await userService.getProfile(true);
        
        if (freshUserData) {
          setUserData(freshUserData);
          const timestamp = new Date().toISOString();
          await AsyncStorage.setItem(CACHE_CONFIG.PROFILE_KEY, JSON.stringify(freshUserData));
          await AsyncStorage.setItem(`${CACHE_CONFIG.PROFILE_KEY}_timestamp`, timestamp);
          setProfileLastUpdated(timestamp);
          console.log('🔄 User profile refreshed from server');
        }
      }
      
      // Refresh stats if needed
      if (shouldRefreshStats) {
        const freshUserStats = await userService.getUserStats(true);
        
        if (freshUserStats) {
          setUserStats(freshUserStats);
          const timestamp = new Date().toISOString();
          await AsyncStorage.setItem(CACHE_CONFIG.STATS_KEY, JSON.stringify(freshUserStats));
          await AsyncStorage.setItem(`${CACHE_CONFIG.STATS_KEY}_timestamp`, timestamp);
          setStatsLastUpdated(timestamp);
        }
      }
      
      if (showFeedback) {
        Toast.show({
          type: 'success',
          text1: 'Datos actualizados',
          text2: 'La información del perfil ha sido actualizada',
          position: 'bottom',
          visibilityTime: 2000,
        });
      }
      
      setError(null);
    } catch (error) {
      console.error('Error refreshing user data:', error);
      
      if (showFeedback) {
        Toast.show({
          type: 'error',
          text1: 'Error de actualización',
          text2: 'No se pudieron actualizar los datos del perfil',
          position: 'bottom',
          visibilityTime: 3000,
        });
      }
    } finally {
      if (showFeedback) {
        setIsLoading(false);
      }
    }
  };

  /**
   * Update specific user data and sync with server
   * @param {Object} updates - User profile fields to update
   */
  const updateUserData = useCallback(async (updates) => {
    try {
      setIsLoading(true);
      
      // Send updates to API
      const updatedProfile = await userService.updateProfile(updates);
      
      if (updatedProfile) {
        // Update local state with new data
        const newUserData = { ...userData, ...updatedProfile };
        setUserData(newUserData);
        
        // Update cache
        const timestamp = new Date().toISOString();
        await AsyncStorage.setItem(CACHE_CONFIG.PROFILE_KEY, JSON.stringify(newUserData));
        await AsyncStorage.setItem(`${CACHE_CONFIG.PROFILE_KEY}_timestamp`, timestamp);
        setProfileLastUpdated(timestamp);
        
        // Show success feedback
        Toast.show({
          type: 'success',
          text1: 'Perfil actualizado',
          text2: 'Tus cambios han sido guardados correctamente',
          position: 'bottom',
          visibilityTime: 3000,
        });
        
        return updatedProfile;
      }
    } catch (error) {
      console.error('Error updating user data:', error);
      
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudieron guardar los cambios',
        position: 'bottom',
        visibilityTime: 3000,
      });
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userData]);

  /**
   * Upload a new profile photo
   * @param {string} imageData - Base64 encoded image data
   * @param {string} fileName - Name for the image file
   */
  const uploadProfilePhoto = useCallback(async (imageData, fileName) => {
    try {
      setIsLoading(true);
      
      // Upload photo to server
      const response = await userService.uploadProfilePhoto(imageData, fileName);
      
      if (response) {
        // Extract photo URL
        const photoUrl = response.foto || response.photoUrl || response.imageUrl;
        
        if (photoUrl) {
          // Update local state with new photo URL
          const updatedUserData = { 
            ...userData, 
            photoUrl: photoUrl,
            foto: photoUrl
          };
          
          setUserData(updatedUserData);
          
          // Update cache
          const timestamp = new Date().toISOString();
          await AsyncStorage.setItem(CACHE_CONFIG.PROFILE_KEY, JSON.stringify(updatedUserData));
          await AsyncStorage.setItem(`${CACHE_CONFIG.PROFILE_KEY}_timestamp`, timestamp);
          setProfileLastUpdated(timestamp);
          
          Toast.show({
            type: 'success',
            text1: 'Foto actualizada',
            text2: 'Tu foto de perfil ha sido actualizada',
            position: 'bottom',
            visibilityTime: 3000,
          });
          
          return photoUrl;
        }
      }
      
      throw new Error('No se recibió URL de foto válida');
    } catch (error) {
      console.error('Error uploading profile photo:', error);
      
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo actualizar la foto de perfil',
        position: 'bottom',
        visibilityTime: 3000,
      });
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userData]);

  /**
   * Clear all user data from cache and memory
   */
  const clearUserData = useCallback(async () => {
    try {
      // Clear from AsyncStorage
      await AsyncStorage.removeItem(CACHE_CONFIG.PROFILE_KEY);
      await AsyncStorage.removeItem(`${CACHE_CONFIG.PROFILE_KEY}_timestamp`);
      await AsyncStorage.removeItem(CACHE_CONFIG.STATS_KEY);
      await AsyncStorage.removeItem(`${CACHE_CONFIG.STATS_KEY}_timestamp`);
      
      // Clear from state
      setUserData(null);
      setUserStats(null);
      setProfileLastUpdated(null);
      setStatsLastUpdated(null);
      
      console.log('🧹 User data cleared');
    } catch (error) {
      console.error('Error clearing user data:', error);
    }
  }, []);

  // Value to be provided to consumers
  const contextValue = {
    // Data
    userData,
    userStats,
    isLoading,
    error,
    
    // Timestamps
    profileLastUpdated,
    statsLastUpdated,
    
    // Methods
    refreshUserData,
    updateUserData,
    uploadProfilePhoto,
    clearUserData,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

/**
 * Custom hook to use the UserContext
 * @returns {Object} User context value
 */
export const useUser = () => {
  const context = useContext(UserContext);
  
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  
  return context;
};

export default UserContext;
