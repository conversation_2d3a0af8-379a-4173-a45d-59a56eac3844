import { Admin, Resource, Login, defaultTheme } from 'react-admin';
import { CustomLoginPage } from './components/CustomLoginPage';
// Importar componentes de todos los recursos
import { UserList, UserEdit, UserCreate } from './resources/users';
import { BoxList, BoxEdit, BoxCreate } from './resources/boxes';
import { LeagueList, LeagueEdit, LeagueCreate } from './resources/leagues';
import { WodList, WodEdit, WodCreate } from './resources/wods';
import { MovimientoList, MovimientoEdit, MovimientoCreate } from './resources/movimientos';
import { RegistrationList, RegistrationEdit, RegistrationCreate } from './resources/registrations';
import { ResultList, ResultEdit, ResultCreate } from './resources/results';
import { RankingList, RankingEdit, RankingCreate } from './resources/rankings';
import { ResultadoList } from './resources/resultados';
import { ClasificacionList } from './resources/clasificaciones';
import authProvider from './authProvider';
import dataProvider from './dataProvider';

// Iconos
import PeopleIcon from '@mui/icons-material/People';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import SportsMartialArtsIcon from '@mui/icons-material/SportsMartialArts';
import FitnessCenterOutlinedIcon from '@mui/icons-material/FitnessCenterOutlined';
import HowToRegIcon from '@mui/icons-material/HowToReg';
import ScoreIcon from '@mui/icons-material/Score';
import LeaderboardIcon from '@mui/icons-material/Leaderboard';
import Business from '@mui/icons-material/Business';
import Assessment from '@mui/icons-material/Assessment';

// Personalización del tema
const theme = {
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#ff9800',
    },
  },
};

// Página de login personalizada
const MyLoginPage = () => <CustomLoginPage />

// Componente principal de la aplicación
const App = () => (
  <Admin
    dataProvider={dataProvider}
    authProvider={authProvider}
    title="Panel de Administración - The WOD League"
    theme={theme}
    loginPage={MyLoginPage}
    requireAuth
  >
    {/* Todos los recursos disponibles */}
    <Resource
      name="users"
      list={UserList}
      edit={UserEdit}
      create={UserCreate}
      icon={PeopleIcon}
      options={{ label: 'Usuarios' }}
    />
    <Resource
      name="boxes"
      list={BoxList}
      edit={BoxEdit}
      create={BoxCreate}
      icon={Business}
      options={{ label: 'Boxes' }}
    />
    <Resource
      name="leagues"
      list={LeagueList}
      edit={LeagueEdit}
      create={LeagueCreate}
      icon={EmojiEventsIcon}
      options={{ label: 'Ligas' }}
    />
    <Resource
      name="wods"
      list={WodList}
      edit={WodEdit}
      create={WodCreate}
      icon={SportsMartialArtsIcon}
      options={{ label: 'WODs' }}
    />
    <Resource
      name="movimientos"
      list={MovimientoList}
      edit={MovimientoEdit}
      create={MovimientoCreate}
      icon={FitnessCenterOutlinedIcon}
      options={{ label: 'Movimientos' }}
    />
    <Resource
      name="registrations"
      list={RegistrationList}
      edit={RegistrationEdit}
      create={RegistrationCreate}
      icon={HowToRegIcon}
      options={{ label: 'Inscripciones' }}
    />
    <Resource
      name="results"
      list={ResultList}
      edit={ResultEdit}
      create={ResultCreate}
      icon={ScoreIcon}
      options={{ label: 'Resultados' }}
    />
    <Resource
      name="rankings"
      list={RankingList}
      edit={RankingEdit}
      create={RankingCreate}
      icon={LeaderboardIcon}
      options={{ label: 'Clasificaciones' }}
    />
  </Admin>
);

export default App;
