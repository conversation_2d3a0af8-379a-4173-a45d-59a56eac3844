import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesGuard } from './roles.guard';
import { RolUsuario } from '../../usuarios/entities/usuario.entity';

/**
 * Tests para el RolesGuard
 * Verifica que el guard permita o deniegue acceso basado en roles
 */
describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn()
          }
        }
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('debe estar definido', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    it('debe permitir acceso cuando no hay roles requeridos', () => {
      // Arrange
      const mockContext = createMockExecutionContext('user', RolUsuario.USUARIO);
      jest.spyOn(reflector, 'get').mockReturnValue(undefined);

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
      expect(reflector.get).toHaveBeenCalled();
    });

    it('debe permitir acceso cuando el usuario tiene el rol requerido', () => {
      // Arrange
      const mockContext = createMockExecutionContext('user', RolUsuario.ADMIN);
      jest.spyOn(reflector, 'get').mockReturnValue([RolUsuario.ADMIN]);

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
    });

    it('debe permitir acceso cuando el usuario tiene uno de los roles requeridos', () => {
      // Arrange
      const mockContext = createMockExecutionContext('user', RolUsuario.BOX_OWNER);
      jest.spyOn(reflector, 'get').mockReturnValue([RolUsuario.ADMIN, RolUsuario.BOX_OWNER]);

      // Act
      const result = guard.canActivate(mockContext as any);

      // Assert
      expect(result).toBe(true);
    });

    it('debe denegar acceso cuando el usuario no tiene el rol requerido', () => {
      // Arrange
      const mockContext = createMockExecutionContext('user', RolUsuario.USUARIO);
      jest.spyOn(reflector, 'get').mockReturnValue([RolUsuario.ADMIN]);

      // Act & Assert
      expect(() => guard.canActivate(mockContext as any)).toThrow(UnauthorizedException);
    });

    it('debe denegar acceso cuando no hay usuario', () => {
      // Arrange
      const mockContext = createMockExecutionContext(null, null);
      jest.spyOn(reflector, 'get').mockReturnValue([RolUsuario.ADMIN]);

      // Act & Assert
      expect(() => guard.canActivate(mockContext as any)).toThrow(UnauthorizedException);
    });
  });
});

/**
 * Crea un contexto de ejecución simulado para los tests
 */
function createMockExecutionContext(property: string | null, role: RolUsuario | null) {
  const user = property ? { 
    [property]: { 
      rol: role 
    } 
  } : {};

  return {
    switchToHttp: () => ({
      getRequest: () => user
    }),
    getHandler: () => ({}),
    getClass: () => ({})
  };
}
