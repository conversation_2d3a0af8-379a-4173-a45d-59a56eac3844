import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WodsService } from './wods.service';
import { WodsController } from './wods.controller';
import { WOD } from './entities/wod.entity';
import { LigasModule } from '../ligas/ligas.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([WOD]),
    LigasModule,
  ],
  controllers: [WodsController],
  providers: [WodsService],
  exports: [WodsService],
})
export class WodsModule {}
