import { 
  List, 
  Datagrid, 
  TextField, 
  ReferenceField,
  Edit, 
  SimpleForm, 
  SelectInput,
  Create,
  ReferenceInput,
  AutocompleteInput,
  DateTimeInput,
  TextInput,
  required
} from 'react-admin';

// Lista de Inscripciones
export const RegistrationList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <ReferenceField source="usuarioId" reference="users" label="Usuario">
        <TextField source="nombre" />
      </ReferenceField>
      <ReferenceField source="ligaId" reference="leagues" label="Liga">
        <TextField source="nombre" />
      </ReferenceField>
      <TextField source="estado" />
      <TextField source="fechaInscripcion" />
    </Datagrid>
  </List>
);

// Componente de edición de inscripciones
export const RegistrationEdit = () => (
  <Edit>
    <SimpleForm>
      <TextInput disabled source="id" />
      <ReferenceInput source="usuarioId" reference="users" label="Usuario">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="ligaId" reference="leagues" label="Liga">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <SelectInput source="estado" choices={[
        { id: 'pending', name: 'Pendiente' },
        { id: 'approved', name: 'Aprobada' },
        { id: 'rejected', name: 'Rechazada' },
        { id: 'cancelled', name: 'Cancelada' },
      ]} />
      <DateTimeInput source="fechaInscripcion" disabled />
    </SimpleForm>
  </Edit>
);

// Componente de creación de inscripciones
export const RegistrationCreate = () => (
  <Create>
    <SimpleForm>
      <ReferenceInput source="usuarioId" reference="users" label="Usuario">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <ReferenceInput source="ligaId" reference="leagues" label="Liga">
        <AutocompleteInput optionText="nombre" />
      </ReferenceInput>
      <SelectInput source="estado" choices={[
        { id: 'pending', name: 'Pendiente' },
        { id: 'approved', name: 'Aprobada' },
        { id: 'rejected', name: 'Rechazada' },
        { id: 'cancelled', name: 'Cancelada' },
      ]} defaultValue="pending" />
      <DateTimeInput source="fechaInscripcion" defaultValue={new Date()} />
    </SimpleForm>
  </Create>
);
