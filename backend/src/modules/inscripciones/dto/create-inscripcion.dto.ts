import { IsBoolean, IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { EstadoInscripcion, MetodoPago } from '../entities/inscripcion.entity';

export class CreateInscripcionDto {
  @IsNotEmpty()
  @IsUUID()
  usuarioId: string;

  @IsNotEmpty()
  @IsUUID()
  ligaId: string;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  fechaInscripcion: Date;

  @IsOptional()
  @IsEnum(EstadoInscripcion)
  estado?: EstadoInscripcion;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  fechaPago?: Date;

  @IsOptional()
  @IsEnum(MetodoPago)
  metodoPago?: MetodoPago;

  @IsOptional()
  @IsString()
  referenciaPago?: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  montoPagado?: number;

  @IsOptional()
  @IsBoolean()
  esEarly?: boolean;

  @IsNotEmpty()
  @IsString()
  categoria: string;

  @IsOptional()
  @IsString()
  comentarios?: string;
}
