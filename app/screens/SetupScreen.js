import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ScrollView,
  Dimensions,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../context/AuthContextNest';
import Colors from '../constants/colors';
import axios from 'axios';
import { NESTJS_URL } from '../config';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import Toast from 'react-native-toast-message';

const SetupScreen = ({ navigation }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [userData, setUserData] = useState({
    gender: '',
    age: 28,
    activityLevel: '',
    fullName: '',
    username: '',
    email: '',
    profileImage: null,
  });
  const [isUploading, setIsUploading] = useState(false);
  const { user } = useAuth();

  const totalSteps = 3;

  // Función para validar entradas de texto
  const validateInput = (text, field) => {
    // Patrones según el tipo de campo
    const patterns = {
      fullName: /^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,50}$/,
      username: /^[a-zA-Z0-9_]{3,20}$/
    };

    if (!patterns[field] || !patterns[field].test(text)) {
      let message = '';

      switch (field) {
        case 'fullName':
          message = 'El nombre solo debe contener letras y espacios (2-50 caracteres)';
          break;
        case 'username':
          message = 'El usuario solo debe contener letras, números y guiones bajos (3-20 caracteres)';
          break;
        default:
          message = 'Formato inválido';
      }

      Alert.alert("Formato inválido", message);
      return false;
    }
    return true;
  };

  // Función para sanitizar texto
  const sanitizeText = (text) => {
    if (!text) return '';
    return text.trim().replace(/[<>&"']/g, (c) => {
      return {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#x27;'
      }[c];
    });
  };

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      completeSetup();
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSelectGender = (gender) => {
    setUserData({ ...userData, gender });
  };

  const handleAgeChange = (direction) => {
    if (direction === 'up' && userData.age < 99) {
      setUserData({
        ...userData,
        age: userData.age + 1
      });
    } else if (direction === 'down' && userData.age > 16) {
      setUserData({
        ...userData,
        age: userData.age - 1
      });
    }
  };

  // Solicitar permisos para acceder a la galería
  useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          "Permiso denegado",
          "Necesitamos permisos para acceder a tu galería de imágenes"
        );
      }
    })();
  }, []);

  // Función para seleccionar imagen de la galería
  const pickImage = async () => {
    try {
      // Versión compatible con todas las versiones de expo-image-picker
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setIsUploading(true);

        try {
          // Optimizar la imagen antes de subirla
          const optimizedImage = await ImageManipulator.manipulateAsync(
            result.assets[0].uri,
            [{ resize: { width: 500 } }],
            { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
          );

          // Guardar temporalmente la imagen en userData
          setUserData({
            ...userData,
            profileImage: optimizedImage.uri
          });
        } catch (error) {
          console.error('Error al procesar la imagen:', error);
          // En caso de error en la manipulación, usar la imagen original
          setUserData({
            ...userData,
            profileImage: result.assets[0].uri
          });

          Toast.show({
            type: 'info',
            text1: 'Aviso',
            text2: 'Imagen seleccionada sin optimización'
          });
        } finally {
          setIsUploading(false);
        }
      }
    } catch (error) {
      console.error('Error al seleccionar imagen:', error);
      setIsUploading(false);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo acceder a la galería'
      });
    }
  };

  // Función para validar el tipo y tamaño de la imagen
  const validateImage = (uri, fileSize) => {
    // Verificar extensión permitida (JPEG, PNG, WebP)
    const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
    const extension = uri.split('.').pop().toLowerCase();

    if (!validExtensions.includes(extension)) {
      Alert.alert(
        'Formato no válido',
        'Por favor, selecciona una imagen en formato JPG, PNG o WebP.'
      );
      return false;
    }

    // Verificar tamaño máximo (5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB en bytes
    if (fileSize && fileSize > MAX_SIZE) {
      Alert.alert(
        'Imagen demasiado grande',
        'La imagen debe ser menor de 5MB. Por favor, selecciona una imagen más pequeña.'
      );
      return false;
    }

    return true;
  };

  // Función para generar un nombre de archivo seguro y descriptivo
  const generateSafeFilename = (username) => {
    const sanitizedUsername = username.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();

    // Obtener solo la fecha en formato YYYY-MM-DD
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Los meses en JS son 0-11
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;

    return `${sanitizedUsername}-${dateStr}`;
  };

  // Función para optimizar la imagen antes de subirla
  const optimizeImage = async (imageUri) => {
    try {
      console.log('Optimizando imagen...');

      // Obtener información de la imagen
      const imageInfo = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        { format: 'jpeg' }
      );

      // Calcular factores de redimensionamiento basados en resolución máxima (1024x1024)
      const MAX_DIMENSION = 1024;
      let width = imageInfo.width;
      let height = imageInfo.height;
      let resizeActions = [];

      if (width > MAX_DIMENSION || height > MAX_DIMENSION) {
        if (width > height) {
          const aspectRatio = height / width;
          width = MAX_DIMENSION;
          height = Math.round(MAX_DIMENSION * aspectRatio);
        } else {
          const aspectRatio = width / height;
          height = MAX_DIMENSION;
          width = Math.round(MAX_DIMENSION * aspectRatio);
        }

        resizeActions.push({ resize: { width, height } });
      }

      // Generar 3 tamaños diferentes (pequeño para avatares, medio para listas, grande para perfil)
      const thumbnails = {};

      // 1. Imagen principal optimizada
      const optimizedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        resizeActions,
        { compress: 0.8, format: 'jpeg' }
      );

      // 2. Miniatura pequeña (64x64)
      const smallThumbnail = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 64, height: 64 } }],
        { compress: 0.8, format: 'jpeg' }
      );

      thumbnails.small = smallThumbnail.uri;

      // 3. Miniatura mediana (256x256)
      const mediumThumbnail = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: 256, height: 256 } }],
        { compress: 0.8, format: 'jpeg' }
      );

      thumbnails.medium = mediumThumbnail.uri;

      console.log('Imagen optimizada correctamente');

      return {
        optimizedUri: optimizedImage.uri,
        width: optimizedImage.width,
        height: optimizedImage.height,
        thumbnails
      };
    } catch (error) {
      console.error('Error al optimizar imagen:', error);
      // Si falla la optimización, devolver la imagen original
      return { optimizedUri: imageUri, thumbnails: {} };
    }
  };

  // Función para subir imagen al backend NestJS
  const uploadProfileImage = async (imageUri, token) => {
    try {
      console.log('📸 Preparando imagen para subir a NestJS...');

      // TODO: Implement AWS S3 + CloudFront for image storage in production
      // TODO: Generate temporary signed URLs for image access with short expiration
      // TODO: Use CDN for efficient delivery of images across different regions
      // TODO: Apply proper caching policies with automatic invalidation when updated
      // TODO: Implement automated cleanup for old/unused images

      // 1. Verificar que el usuario esté autenticado
      if (!token) {
        throw new Error('No autorizado: El usuario debe estar autenticado');
      }

      // 2. Obtener información de la imagen
      const fileInfo = await FileSystem.getInfoAsync(imageUri);

      // 3. Validar la imagen (formato y tamaño)
      if (!validateImage(imageUri, fileInfo.size)) {
        throw new Error('Validación de imagen fallida');
      }

      // 4. Optimizar imagen
      const { optimizedUri, width, height } = await optimizeImage(imageUri);

      // 5. Extraer datos de la imagen optimizada
      const filename = generateSafeFilename(userData.username);
      const fileType = optimizedUri.split('.').pop().toLowerCase();

      // Determinar el tipo MIME basado en la extensión
      let type = 'image/jpeg';
      if (fileType === 'png') {
        type = 'image/png';
      } else if (fileType === 'webp') {
        type = 'image/webp';
      }

      // 6. Crear FormData para la subida
      const formData = new FormData();
      // En NestJS, el nombre usado aquí debe coincidir con el primer parámetro de FileInterceptor
      formData.append('file', {
        uri: optimizedUri,
        name: `${filename}.${fileType}`,
        type
      });
      
      // Añadir metadatos opcionales (según ProfilePhotoDto)
      formData.append('caption', 'Foto de perfil');
      formData.append('altText', 'Imagen de perfil de usuario');

      console.log('🚀 Subiendo imagen al servidor NestJS...');

      // 7. Incluir el token JWT para autenticación
      const uploadResponse = await axios.post(
        `${NESTJS_URL}/api/perfil/photo`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
        }
      );

      if (uploadResponse.data && uploadResponse.data.imageUrl) {
        // Obtener URL de la imagen subida
        const imageUrl = uploadResponse.data.imageUrl;
        console.log('✅ Imagen subida con éxito, URL:', imageUrl);

        // Guardar referencia a la imagen en AsyncStorage para acceso rápido
        try {
          await AsyncStorage.setItem('profileImageUrl', imageUrl);
        } catch (err) {
          console.warn('⚠️ No se pudo guardar URL de imagen en AsyncStorage:', err);
        }

        return imageUrl;
      } else {
        throw new Error('No se recibió respuesta válida del servidor');
      }
    } catch (error) {
      console.error('❌ Error al subir imagen a NestJS:', error.response?.data || error);

      // Información detallada para errores específicos
      if (error.response?.status === 413) {
        Alert.alert('Error', 'La imagen es demasiado grande para el servidor. Intenta con una imagen más pequeña.');
      } else if (error.response?.status === 403) {
        Alert.alert('Error de permisos', 'No tienes permiso para subir esta imagen. Inicia sesión nuevamente.');
      } else if (error.response?.status === 415) {
        Alert.alert('Formato no soportado', 'El formato de imagen no es compatible con el servidor.');
      } else {
        Alert.alert('Error', 'Ha ocurrido un problema al subir la imagen. Por favor, inténtalo nuevamente.');
      }

      throw error;
    }
  };

  const completeSetup = async () => {
    try {
      // Sanitizar y validar datos antes de guardar
      const sanitizedName = sanitizeText(userData.fullName);
      const sanitizedUsername = sanitizeText(userData.username);

      if (!validateInput(sanitizedName, 'fullName') ||
        !validateInput(sanitizedUsername, 'username')) {
        return; // Detener si la validación falla
      }

      // Verificar que el usuario esté autenticado
      if (!user || !user.id) {
        console.error('Error: Usuario no autenticado');
        return;
      }

      // Prepare data to save in NestJS according to the data model
      const userDataToSave = {
        nombre: sanitizedName,
        alias: sanitizedUsername, // Changed from 'usuario' to 'alias' to match data model
        email: userData.email || user.email,
        // Format gender with first letter uppercase to match enum {Masculino, Femenino}
        genero: userData.gender ? userData.gender.charAt(0).toUpperCase() + userData.gender.slice(1).toLowerCase() : null,
        // Add nivel which is required according to data model
        nivel: 'Intermedio', // Default value, can be changed based on user selection
        // Incluimos la edad que sí está en el modelo de datos
        edad: userData.age || null,
        setupCompleted: true,
        onBoarding: true
        // No necesitamos enviar updated_at porque TypeORM lo actualiza automáticamente
      };

      console.log('Guardando datos de perfil en NestJS:', userDataToSave);

      try {
        // Obtener token JWT de autenticación
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
          throw new Error('No se encontró token de autenticación');
        }
        
        // Enviar actualización al backend de NestJS
        const response = await axios.patch(
          `${NESTJS_URL}/api/perfil`, // Todas las rutas de la API necesitan el prefijo /api/
          userDataToSave,
          {
            headers: { Authorization: `Bearer ${token}` }
          }
        );

        console.log('Perfil actualizado con éxito en NestJS:', response.data);

        // Si hay una imagen de perfil, subirla al servidor
        if (userData.profileImage) {
          try {
            await uploadProfileImage(userData.profileImage, token);
          } catch (imageError) {
            console.error('Error al subir imagen de perfil:', imageError);
            // Continuamos aunque falle la subida de imagen
            Toast.show({
              type: 'error',
              text1: 'Error al subir imagen',
              text2: 'No pudimos subir tu imagen de perfil. Podrás intentarlo más tarde.',
              visibilityTime: 4000,
            });
          }
        }

        // Marcar el setup como completado en el almacenamiento local
        await AsyncStorage.setItem('setupCompleted', 'true');

        // Mostrar mensaje de éxito
        Toast.show({
          type: 'success',
          text1: '¡Perfil configurado!',
          text2: 'Tu perfil ha sido configurado correctamente.',
          visibilityTime: 3000,
        });

        // Redirigir a la pantalla principal
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }]
        });
      } catch (error) {
        console.error('Error al actualizar perfil en NestJS:', error.response?.data || error.message);
        
        // Mostrar mensaje de error
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No pudimos guardar tu perfil. Inténtalo de nuevo.',
          visibilityTime: 4000,
        });
      }
    } catch (error) {
      console.error('Error general al completar el setup:', error);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>¿Cuál Es Tu Sexo?</Text>

            <View style={styles.infoBox}>
              <Text style={styles.infoText}>
                Esta información nos ayuda a personalizar tu experiencia y calcular métricas más precisas.
              </Text>
            </View>

            <View style={styles.genderOptionsContainer}>
              <TouchableOpacity
                style={[
                  styles.genderCard,
                  userData.gender === 'masculino' && styles.selectedGenderCard
                ]}
                onPress={() => handleSelectGender('masculino')}
              >
                <View style={[
                  styles.genderIconCircle,
                  userData.gender === 'masculino' ? styles.selectedGenderIconCircle : {}
                ]}>
                  <Text style={[
                    styles.genderIcon,
                    userData.gender === 'masculino' ? styles.selectedGenderIcon : {}
                  ]}>♂</Text>
                </View>
                <Text style={styles.genderText}>Masculino</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.genderCard,
                  userData.gender === 'femenino' && styles.selectedGenderCard
                ]}
                onPress={() => handleSelectGender('femenino')}
              >
                <View style={[
                  styles.genderIconCircle,
                  userData.gender === 'femenino' ? styles.selectedGenderIconCircle : {}
                ]}>
                  <Text style={[
                    styles.genderIcon,
                    userData.gender === 'femenino' ? styles.selectedGenderIcon : {}
                  ]}>♀</Text>
                </View>
                <Text style={styles.genderText}>Femenino</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>¿Qué Edad Tienes?</Text>

            <View style={styles.infoBox}>
              <Text style={styles.infoText}>
                Tu edad nos ayuda a adaptar los entrenamientos y retos a tu etapa de vida.
              </Text>
            </View>

            <View style={styles.ageSelectorContainer}>
              {/* Número grande arriba */}
              <Text style={styles.ageDisplayText}>{userData.age}</Text>

              {/* Triángulo indicador estático */}
              <View style={styles.ageTriangle}>
                <Ionicons
                  name="caret-down"
                  size={24}
                  color={Colors.PRIMARY || '#e0fe10'}
                />
              </View>

              {/* Selector de edad tipo rueda */}
              <View style={styles.ageWheelContainer}>
                <ScrollView
                  ref={ageScrollRef}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  snapToInterval={ITEM_WIDTH}
                  snapToAlignment="center"
                  decelerationRate={0.99}
                  contentContainerStyle={{
                    paddingHorizontal: (screenWidth - ITEM_WIDTH) / 2,
                    alignItems: 'center'
                  }}
                  onMomentumScrollEnd={handleAgeScroll}
                  scrollEventThrottle={18}
                  pagingEnabled={false}
                  directionalLockEnabled={true}
                >
                  {/* Números de edad (16-99) */}
                  {Array.from({ length: 84 }, (_, i) => i + 16).map((age) => (
                    <TouchableOpacity
                      key={age}
                      style={{
                        width: ITEM_WIDTH,
                        height: 60,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: userData.age === age ? '#598392' : 'transparent',
                        borderWidth: userData.age === age ? 1 : 0,
                        borderColor: userData.age === age ? '#fff' : 'transparent',
                        borderRadius: 4,
                        shadowColor: userData.age === age ? '#000' : 'transparent',
                        shadowOffset: userData.age === age ? { width: 0, height: 2 } : { width: 0, height: 0 },
                        shadowOpacity: userData.age === age ? 0.3 : 0,
                        shadowRadius: userData.age === age ? 3 : 0,
                        elevation: userData.age === age ? 5 : 0,
                        transform: userData.age === age ? [{ scale: 1.05 }] : [{ scale: 1 }],
                      }}
                      onPress={() => {
                        setUserData({ ...userData, age });
                        if (ageScrollRef.current) {
                          const minAge = 16;
                          const index = age - minAge;
                          ageScrollRef.current.scrollTo({
                            x: index * ITEM_WIDTH,
                            animated: true,
                            duration: 50
                          });
                        }
                      }}
                    >
                      <Text style={{
                        fontSize: userData.age === age ? 30 : 26,
                        color: userData.age === age ? '#fff' : '#888',
                        fontWeight: userData.age === age ? 'bold' : '400',
                        textAlign: 'center',
                      }}>
                        {age}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          </View>
        );

      case 3:
        return renderProfileSetup();

      default:
        return null;
    }
  };

  const renderProfileSetup = () => {
    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Completa tu perfil</Text>
        <View style={styles.profileContainer}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              {userData.profileImage ? (
                <Image
                  source={{ uri: userData.profileImage }}
                  style={styles.avatarImage}
                  resizeMode="cover"
                />
              ) : (
                <Ionicons name="person" size={64} color="white" />
              )}
            </View>
            <TouchableOpacity
              style={styles.editAvatarButton}
              onPress={pickImage}
              disabled={isUploading}
            >
              {isUploading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Ionicons name="pencil" size={18} color="white" />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.profileForm}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nombre completo</Text>
              <TextInput
                style={styles.input}
                placeholder="Carlos Lopez"
                placeholderTextColor="#808080"
                value={userData.fullName}
                maxLength={50}
                onChangeText={(text) => {
                  setUserData({ ...userData, fullName: text });
                }}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Usuario</Text>
              <TextInput
                style={styles.input}
                placeholder="Carlitos"
                placeholderTextColor="#808080"
                value={userData.username}
                maxLength={20}
                onChangeText={(text) => {
                  // Solo permitir caracteres válidos para username
                  const sanitizedText = text.replace(/[^a-zA-Z0-9_]/g, '');
                  setUserData({ ...userData, username: sanitizedText });
                }}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={[styles.input, styles.disabledInput]}
                placeholder="<EMAIL>"
                placeholderTextColor="#808080"
                keyboardType="email-address"
                value={userData.email}
                editable={false}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  // Get screen width for calculations
  const screenWidth = Dimensions.get('window').width;
  // Width of each age item in the wheel
  const ITEM_WIDTH = screenWidth / 5; // Ajustado para mostrar 5 elementos (2 a cada lado del seleccionado)
  // Reference to the age scroll wheel
  const ageScrollRef = useRef(null);

  // Handle age change when scrolling the wheel
  const handleAgeScroll = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    // Calculate the selected age based on scroll position
    const selectedIndex = Math.round(offsetX / ITEM_WIDTH);
    const minAge = 16;
    const newAge = minAge + selectedIndex;

    // Update only if within valid range
    if (newAge >= 16 && newAge <= 99) {
      setUserData({
        ...userData,
        age: newAge
      });
    }
  };

  // Scroll to the current age when component mounts
  useEffect(() => {
    if (ageScrollRef.current) {
      const minAge = 16;
      const initialIndex = userData.age - minAge;

      // Ajuste para asegurar que el scroll se posicione correctamente
      // Use requestAnimationFrame para garantizar que la UI esté renderizada
      requestAnimationFrame(() => {
        ageScrollRef.current.scrollTo({
          x: initialIndex * ITEM_WIDTH,
          animated: false
        });
      });
    }
  }, [currentStep]); // Añadimos currentStep como dependencia para que se ejecute cuando cambia el paso

  useEffect(() => {
    // Si tenemos un usuario y su email, asignarlo al estado
    if (user && user.email) {
      setUserData(prevData => ({
        ...prevData,
        email: user.email
      }));
      console.log('Email cargado automáticamente:', user.email);
    }
  }, [user]);

  function getStepName(step) {
    switch (step) {
      case 1: return 'Sexo';
      case 2: return 'Edad';
      case 3: return 'Perfil';
      default: return 'Setup';
    }
  }

  function shouldDisableNext() {
    switch (currentStep) {
      case 1: return !userData.gender;
      case 2: return false;
      case 3:
        const isComplete = userData.fullName && userData.username && userData.email;
        return !isComplete;
      default: return false;
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            {currentStep > 1 && (
              <TouchableOpacity style={styles.backButton} onPress={handlePrevStep}>
                <Ionicons name="arrow-back" size={24} color={Colors.PRIMARY} />
              </TouchableOpacity>
            )}

            <View style={styles.progressContainer}>
              {[...Array(totalSteps)].map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.progressDot,
                    currentStep > index ? styles.progressDotActive : {}
                  ]}
                />
              ))}
            </View>
          </View>
        </View>

        {renderStepContent()}

        <View style={styles.navigationButtons}>
          <TouchableOpacity
            style={[
              styles.nextButton,
              !shouldDisableNext() ? styles.activeNextButton : null,
              shouldDisableNext() ? styles.disabledButton : null
            ]}
            onPress={handleNextStep}
            disabled={shouldDisableNext()}
          >
            <Text style={[
              styles.nextButtonText,
              !shouldDisableNext() && styles.activeNextButtonText
            ]}>
              {currentStep === totalSteps ? 'Empezar' : 'Siguiente'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND || '#232323',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 10,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    paddingVertical: 10,
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#444',
    marginHorizontal: 8,
    borderWidth: 1,
    borderColor: '#555',
  },
  progressDotActive: {
    backgroundColor: Colors.PRIMARY || '#e0fe10',
    borderColor: Colors.PRIMARY || '#e0fe10',
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: 0,
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  infoBox: {
    backgroundColor: '#598392',
    borderRadius: 5,
    padding: 15,
    width: '100%',
    marginBottom: 30,
  },
  infoText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
  },
  genderOptionsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 40,
  },
  genderCard: {
    alignItems: 'center',
    marginBottom: 30,
  },
  genderIconCircle: {
    width: 150,
    height: 150,
    borderRadius: 110,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  selectedGenderIconCircle: {
    backgroundColor: Colors.PRIMARY || '#e0fe10',
    borderColor: Colors.PRIMARY || '#e0fe10',
  },
  genderIcon: {
    fontSize: 70,
    color: '#fff',
  },
  selectedGenderIcon: {
    color: '#000',
  },
  genderText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
    fontWeight: 'bold',
  },
  ageSelectorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  ageDisplayText: {
    fontSize: 70,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  ageTriangle: {
    alignSelf: 'center',
    marginBottom: 10,
  },
  ageWheelContainer: {
    position: 'relative',
    width: '100%',
    height: 60,
    overflow: 'hidden',
    backgroundColor: '#232323',
  },
  profileContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  avatarContainer: {
    position: 'relative',
    width: 120,
    height: 120,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.ACCENT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileForm: {
    width: '100%',
    marginTop: 10,
  },
  inputGroup: {
    marginBottom: 20,
    paddingHorizontal: 30,
  },
  inputLabel: {
    color: Colors.PRIMARY || '#e0fe10',
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    backgroundColor: Colors.WHITE,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    color: '#333',
  },
  disabledInput: {
    backgroundColor: '#f0f0f0',
    color: '#666',
  },
  emailHint: {
    color: Colors.PRIMARY,
    fontSize: 12,
    marginTop: 5,
    fontStyle: 'italic',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 30,
    marginBottom: 30,
  },
  nextButton: {
    backgroundColor: Colors.DARK || '#333',
    padding: 15,
    borderRadius: 25,
    width: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderWidth: 1,
    color: Colors.WHITE || '#fff',
    borderColor: Colors.WHITE || '#fff',
  },
  activeNextButton: {
    backgroundColor: Colors.PRIMARY || '#e0fe10',
    borderColor: 'transparent',
  },
  disabledButton: {
    opacity: 0.5,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.WHITE || '#fff',
  },
  activeNextButtonText: {
    color: Colors.DARK || '#232323',
  },
});

export default SetupScreen;
