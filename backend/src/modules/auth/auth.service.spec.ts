import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsuariosService } from '../usuarios/usuarios.service';
import { NivelUsuario, GeneroUsuario, RolUsuario, EstadoUsuario } from '../usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';

// Mock de bcrypt
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
}));

describe('AuthService', () => {
  let service: AuthService;
  let usuariosService: UsuariosService;
  let jwtService: JwtService;

  const mockUsuario = {
    id: '1',
    nombre: 'Test User',
    alias: 'testuser',
    email: '<EMAIL>',
    password: 'hashedPassword',
    nivel: NivelUsuario.RX,
    genero: GeneroUsuario.MASCULINO,
    fechaRegistro: new Date(),
    puntosPvp: 20,
    rol: RolUsuario.USUARIO,
    estado: EstadoUsuario.ACTIVO,
  };

  const mockUsuariosService = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    updateLastLogin: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockReturnValue('jwt-token'),
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue('secret'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsuariosService,
          useValue: mockUsuariosService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usuariosService = module.get<UsuariosService>(UsuariosService);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should return user without password if validation is successful', async () => {
      // Arrange
      mockUsuariosService.findByEmail.mockResolvedValueOnce(mockUsuario);
      (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true);
      
      // Act
      const result = await service.validateUser('<EMAIL>', 'password');
      
      // Assert
      expect(result).toEqual({
        id: '1',
        nombre: 'Test User',
        alias: 'testuser',
        email: '<EMAIL>',
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
        fechaRegistro: expect.any(Date),
        puntosPvp: 20,
        rol: RolUsuario.USUARIO,
        estado: EstadoUsuario.ACTIVO,
      });
      expect(bcrypt.compare).toHaveBeenCalledWith('password', 'hashedPassword');
    });

    it('should return null if password is invalid', async () => {
      // Arrange
      mockUsuariosService.findByEmail.mockResolvedValueOnce(mockUsuario);
      (bcrypt.compare as jest.Mock).mockResolvedValueOnce(false);
      
      // Act
      const result = await service.validateUser('<EMAIL>', 'wrongpassword');
      
      // Assert
      expect(result).toBeNull();
    });

    it('should return null if user is not found', async () => {
      // Arrange
      mockUsuariosService.findByEmail.mockRejectedValueOnce(new Error());
      
      // Act
      const result = await service.validateUser('<EMAIL>', 'password');
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should return access token and user data if login is successful', async () => {
      // Arrange
      const loginDto = {
        email: '<EMAIL>',
        password: 'password',
      };
      
      const user = {
        id: '1',
        email: '<EMAIL>',
        nombre: 'Test User',
        alias: 'testuser',
        rol: RolUsuario.USUARIO,
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
      };
      
      jest.spyOn(service, 'validateUser').mockResolvedValueOnce(user);
      
      // Act
      const result = await service.login(loginDto);
      
      // Assert
      expect(result).toEqual({
        access_token: 'jwt-token',
        user: {
          id: '1',
          nombre: 'Test User',
          alias: 'testuser',
          email: '<EMAIL>',
          rol: RolUsuario.USUARIO,
          nivel: NivelUsuario.RX,
          genero: GeneroUsuario.MASCULINO,
        },
      });
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: '1',
        email: '<EMAIL>',
        rol: RolUsuario.USUARIO,
      });
      expect(mockUsuariosService.updateLastLogin).toHaveBeenCalledWith('1');
    });

    it('should throw UnauthorizedException if credentials are invalid', async () => {
      // Arrange
      const loginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };
      
      jest.spyOn(service, 'validateUser').mockResolvedValueOnce(null);
      
      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    it('should register a new user and return access token and user data', async () => {
      // Arrange
      const createUsuarioDto = {
        nombre: 'Test User',
        alias: 'testuser',
        email: '<EMAIL>',
        password: 'password',
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
      };
      
      mockUsuariosService.create.mockResolvedValueOnce(mockUsuario);
      
      // Act
      const result = await service.register(createUsuarioDto);
      
      // Assert
      expect(result).toEqual({
        access_token: 'jwt-token',
        user: {
          id: '1',
          nombre: 'Test User',
          alias: 'testuser',
          email: '<EMAIL>',
          rol: RolUsuario.USUARIO,
          nivel: NivelUsuario.RX,
          genero: GeneroUsuario.MASCULINO,
        },
      });
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: '1',
        email: '<EMAIL>',
        rol: RolUsuario.USUARIO,
      });
    });
  });
});
