/**
 * Interfaz para estandarizar las respuestas de la API
 * Todas las respuestas seguirán este formato para mayor consistencia
 */
export interface ApiResponse<T> {
  /**
   * Indica si la operación fue exitosa
   * @example true
   */
  success: boolean;
  
  /**
   * Mensaje descriptivo sobre el resultado de la operación
   * @example "Usuario actualizado correctamente"
   */
  message: string;
  
  /**
   * Datos de respuesta (solo presente cuando success es true)
   */
  data?: T;
  
  /**
   * Lista de errores (solo presente cuando success es false)
   */
  errors?: string[];
}
