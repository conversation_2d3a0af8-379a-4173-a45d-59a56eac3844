import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Modal,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ActivityIndicator
} from 'react-native';
import Toast from 'react-native-toast-message';

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../context/AuthContextNest';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Colors from '../constants/colors';
import authApiNest from '../api/authApiNest';
import userApi from '../api/userApi';

const LoginScreen = ({ route }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Test..12345678');
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotModal, setShowForgotModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const navigation = useNavigation();
  const { login } = useAuth();

  // Mostrar mensaje de verificación si viene de registro
  useEffect(() => {
    if (route.params?.showVerificationMessage) {
      Toast.show({
        type: 'info',
        text1: 'Verifica tu email',
        text2: 'Te hemos enviado un correo de verificación',
        visibilityTime: 6000,
      });
    }
  }, [route.params]);

  // TODO: Quitar este botón y función al finalizar la app
  const resetOnboarding = async () => {
    try {
      // 1. Actualizar estado local (AsyncStorage)
      await AsyncStorage.removeItem('onboardingCompleted');
      
      // 2. Actualizar estado en la base de datos
      const token = await AsyncStorage.getItem('auth-token');
      if (token) {
        // Solo intentar actualizar en backend si hay sesión iniciada
        try {
          await userApi.resetOnboarding();
          console.log('Estado de onboarding actualizado en backend');
        } catch (apiError) {
          console.error('Error al actualizar onboarding en backend:', apiError);
          // Continuamos aunque falle la actualización en el backend
          // para mantener al menos la actualización local
        }
      } else {
        console.warn('No hay sesión activa, solo se actualizó el estado local');
      }
      
      Toast.show({
        type: 'success',
        text1: 'Onboarding reseteado',
        text2: 'Reinicia la app para ver el onboarding de nuevo'
      });
    } catch (error) {
      console.error('Error al resetear onboarding:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'No se pudo resetear el onboarding'
      });
    }
  };

  const handleLogin = async () => {
    setIsLoggingIn(true);
    try {
      console.log(`Iniciando sesión para usuario: ${email}`);
      
      // 1. Login directo con NestJS
      const isFirstLogin = await login(email, password);
      
      try {
        // Verificar estado de verificación de email
        const verificationStatus = await authApiNest.checkEmailVerification();
        const isEmailVerified = verificationStatus?.verified === true;
        
        console.log('Estado de verificación:', {
          emailVerified: isEmailVerified
        });
        
        // Si el email no está verificado, redirigir a pantalla de verificación
        if (!isEmailVerified) {
          // Usar reset en lugar de navigate para evitar que sea sobrescrito
          navigation.reset({
            index: 0,
            routes: [{ name: 'EmailVerification' }]
          });
          
          // Restablecer estado de carga y salir de la función
          setIsLoggingIn(false);
          return;
        }
      } catch (verificationError) {
        console.error('Error al verificar estado del email:', verificationError);
        // Si hay error al verificar, mejor redireccionar a verificación por precaución
        navigation.reset({
          index: 0,
          routes: [{ name: 'EmailVerification' }]
        });
        setIsLoggingIn(false);
        return;
      }
      
      // Email verificado, mostrar mensaje de bienvenida
      const userName = await AsyncStorage.getItem('userName');
      
      Toast.show({
        type: 'success',
        text1: 'Iniciado sesión correctamente',
        text2: `Bienvenido, ${userName || 'usuario'}`
      });
      
      // Navegación condicional: Setup si es primer login, Main si no
      if (isFirstLogin) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Setup' }]
        });
      } else {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }]
        });
      }
      
    } catch (error) {
      setIsLoggingIn(false);
      
      // Analizar tipo de error para mostrar mensaje adecuado
      let errorMessage = 'Error desconocido';
      
      // Determinar tipo de error según la respuesta del servidor
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        switch (status) {
          case 401:
            errorMessage = 'Credenciales inválidas. Verifica tu email y contraseña';
            break;
          case 403:
            errorMessage = 'Tu cuenta ha sido deshabilitada';
            break;
          case 404:
            // Usar el mensaje personalizado que viene de la API
            errorMessage = errorData?.message || 'Usuario no encontrado. Verifica tu email o regístrate';
            break;
          case 429:
            errorMessage = 'Demasiados intentos fallidos. Por favor, intenta más tarde';
            break;
          default:
            errorMessage = errorData?.message || 'Error al iniciar sesión';
        }
      } else if (error.request) {
        // Sin respuesta del servidor
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      } else {
        // Error en la solicitud
        errorMessage = error.message || 'Error al procesar la solicitud';
      }
      
      Toast.show({
        type: 'error',
        text1: 'Error al iniciar sesión',
        text2: errorMessage
      });
    }
  };

  const handleForgotPassword = () => {
    setResetEmail(email); // Usar el email del formulario si está disponible
    setShowForgotModal(true);
  };

  const handleResetPassword = async () => {
    if (!resetEmail || !resetEmail.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Por favor, introduce tu email',
      });
      return;
    }

    try {
      // Usar el nuevo API de NestJS para solicitar restablecimiento de contraseña
      await authApiNest.requestPasswordReset(resetEmail);
      
      Toast.show({
        type: 'success',
        text1: 'Email enviado',
        text2: 'Revisa tu bandeja de entrada para restablecer tu contraseña',
      });
      setShowForgotModal(false);
    } catch (error) {
      console.log('Error al restablecer contraseña:', error);
      let errorMessage = 'Error al enviar el email';

      // Analizar error según respuesta del servidor
      if (error.response) {
        const status = error.response.status;
        
        switch (status) {
          case 400:
            errorMessage = 'El formato del email no es válido';
            break;
          case 404:
            errorMessage = 'No existe ninguna cuenta con este email';
            break;
          case 429:
            errorMessage = 'Demasiados intentos. Inténtalo más tarde';
            break;
          default:
            errorMessage = error.response.data?.message || 'Error al enviar el email de recuperación';
        }
      } else if (error.request) {
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      }

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: errorMessage,
      });
    }
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidView}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Log In</Text>
          </View>

          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeTitle}>¡Bienvenido!</Text>
            <Text style={styles.welcomeText}>
              Accede a tu cuenta para ver tus WODs, batallas y conectar con otros atletas.
            </Text>
          </View>

          <View style={styles.formBackground}>
            <View style={styles.formContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="<EMAIL>"
                  placeholderTextColor="#6c6c6c"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  value={email}
                  onChangeText={setEmail}
                />
                <View style={styles.checkIcon}>
                  <Ionicons
                    name="checkmark-circle"
                    size={20}
                    color={Colors.SECONDARY}
                    style={{ opacity: email.trim() ? 1 : 0.3 }}
                  />
                </View>
              </View>

              <Text style={styles.inputLabel}>Contraseña</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="••••••••••"
                  placeholderTextColor="#6c6c6c"
                  secureTextEntry={!showPassword}
                  value={password}
                  onChangeText={setPassword}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-off-outline" : "eye-outline"}
                    size={20}
                    color="#6c6c6c"
                  />
                </TouchableOpacity>
              </View>

              <View style={styles.forgotPasswordContainer}>
                <TouchableOpacity onPress={handleForgotPassword}>
                  <Text style={styles.forgotPasswordText}>¿Olvidaste tu contraseña?</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={[styles.emptyComponent, { height: 30 }]} />

          <TouchableOpacity
            style={[styles.loginButton, isLoggingIn && styles.disabledButton]}
            onPress={handleLogin}
            disabled={isLoggingIn}
          >
            {isLoggingIn ? (
              <View style={styles.spinnerContainer}>
                <ActivityIndicator color={Colors.PRIMARY} size="large" />
              </View>
            ) : (
              <Text style={styles.loginButtonText}>Log In</Text>
            )}
          </TouchableOpacity>

          {/* Empty component */}
          <View style={[styles.emptyComponent, { height: 30 }]} />

          <Text style={styles.orText}>o regístrate con</Text>

          <View style={styles.socialButtonsContainer}>
            <TouchableOpacity style={styles.socialButton}>
              <Ionicons name="logo-google" size={22} color={Colors.WHITE} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.socialButton}>
              <Ionicons name="logo-instagram" size={22} color={Colors.WHITE} />
            </TouchableOpacity>
          </View>

          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>
              ¿No tienes cuenta? <Text style={styles.registerLink} onPress={handleRegister}>Regístrate</Text>
            </Text>
          </View>

          {/* TODO: Quitar este botón al finalizar la app */}
          <TouchableOpacity
            style={[styles.devButton, { width: 200 }]}
            onPress={resetOnboarding}
          >
            <Text style={styles.devButtonText}>Reset OnBoarding 🔄</Text>
          </TouchableOpacity>

          {/* Modal de Recuperación de Contraseña */}
          <Modal
            animationType="slide"
            transparent={true}
            visible={showForgotModal}
            onRequestClose={() => setShowForgotModal(false)}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Restablecer Contraseña</Text>
                <Text style={styles.modalText}>
                  Introduce tu email y te enviaremos un enlace para restablecer tu contraseña.
                </Text>
                <TextInput
                  style={styles.modalInput}
                  placeholder="Email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  value={resetEmail}
                  onChangeText={setResetEmail}
                />
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => setShowForgotModal(false)}
                  >
                    <Text style={styles.modalCancelText}>Cancelar</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.resetButton}
                    onPress={handleResetPassword}
                  >
                    <Text style={styles.resetButtonText}>Enviar</Text>
                  </TouchableOpacity>
                </View>
                
                {/* Separador */}
                <View style={styles.separator}>
                  <View style={styles.separatorLine} />
                  <Text style={styles.separatorText}>o</Text>
                  <View style={styles.separatorLine} />
                </View>
                
                {/* Botón alternativo para ir directamente a la pantalla de reset */}
                <TouchableOpacity
                  style={styles.directResetButton}
                  onPress={async () => {
                    // Cerrar modal primero
                    setShowForgotModal(false);
                    
                    // Mostrar indicador de carga
                    setIsLoggingIn(true); // Usamos el estado de login existente
                    
                    try {
                      // Usar un email fijo para pruebas, independiente del formulario
                      const testEmail = '<EMAIL>';
                      console.log(`🔄 Solicitando token dinámico para: ${testEmail}`);
                      
                      // Solicitar un token real al backend
                      const response = await authApiNest.requestPasswordReset(testEmail);
                      console.log('✅ Token solicitado correctamente:', response);
                      
                      // Extraer el token de la respuesta de debug
                      if (response && response.debug && response.debug.resetPasswordUrl) {
                        const urlString = response.debug.resetPasswordUrl;
                        const token = urlString.split('token=')[1];
                        
                        if (token) {
                          console.log(`🎯 Token dinámico obtenido: ${token.substring(0, 10)}...`);
                          
                          // IMPORTANTE: Pequeño retraso para asegurar que la navegación funciona
                          // después de cerrar el modal
                          setTimeout(() => {
                            navigation.navigate('ResetPassword', { token });
                          }, 300);
                        } else {
                          throw new Error('No se pudo extraer el token de la URL');
                        }
                      } else {
                        console.log('⚠️ No se encontró información de debug en la respuesta:', response);
                        Alert.alert('Información', 'Se ha enviado un email con instrucciones para restablecer tu contraseña. Revisa tu bandeja de entrada.');
                      }
                    } catch (error) {
                      console.error('❌ Error al solicitar token:', error);
                      Alert.alert('Error', 'No se pudo solicitar el token de recuperación. Por favor, inténtalo de nuevo.');
                    } finally {
                      setIsLoggingIn(false); // Ocultar el indicador de carga
                    }
                  }}
                >
                  <Ionicons name="construct-outline" size={18} color="#000" style={{marginRight: 8}} />
                  <Text style={styles.directResetButtonText}>Ir directamente a restablecer (modo prueba)</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  keyboardAvoidView: {
    flex: 1,
    paddingTop: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 25,
    paddingTop: 20,
    marginBottom: 40,
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    textAlign: 'center',
  },
  welcomeContainer: {
    paddingHorizontal: 25,
    marginBottom: 30,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.WHITE,
    marginBottom: 10,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: 16,
    color: '#CACACA',
    lineHeight: 22,
    textAlign: 'center',
  },
  formBackground: {
    backgroundColor: '#2e2e2e',
    borderRadius: 0,
    padding: 15,
    paddingBottom: 0,
    marginHorizontal: 0,
    marginBottom: 20,
  },
  formContainer: {
    paddingHorizontal: 15,
  },
  inputLabel: {
    fontSize: 14,
    color: Colors.PRIMARY,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: 8,
    paddingHorizontal: 15,
    marginBottom: 20,
    height: 50,
    borderWidth: 1,
    borderColor: '#2e2e2e',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#232323',
    height: '100%',
  },
  eyeIcon: {
    padding: 5,
  },
  checkIcon: {
    padding: 5,
  },
  forgotPasswordContainer: {
    marginTop: 15,
  },
  forgotPasswordText: {
    color: Colors.PRIMARY,
    alignSelf: 'flex-end',
    fontSize: 14,
    marginBottom: 8,
  },
  resetPasswordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(224, 254, 16, 0.15)',
    marginTop: 5,
  },
  resetPasswordButtonText: {
    color: '#e0fe10',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  loginButton: {
    backgroundColor: '#303030',
    borderColor: Colors.WHITE,
    borderWidth: 0.5,
    borderRadius: 25,
    height: 50,
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    alignSelf: 'center',
  },
  disabledButton: {
    opacity: 0.7,
    borderColor: Colors.PRIMARY,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.WHITE,
  },
  spinnerContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  orText: {
    fontSize: 14,
    color: '#CACACA',
    textAlign: 'center',
    marginBottom: 15,
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  socialButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#303030',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  registerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  registerText: {
    fontSize: 14,
    color: '#CACACA',
  },
  registerLink: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 14,
    color: '#555',
    marginVertical: 15,
    textAlign: 'center',
  },
  separator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 15,
    width: '100%',
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e5e5e5',
  },
  separatorText: {
    marginHorizontal: 10,
    color: '#888',
    fontSize: 14,
  },
  directResetButton: {
    flexDirection: 'row',
    backgroundColor: 'rgba(224, 254, 16, 0.15)',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  directResetButtonText: {
    color: '#000',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  modalCancelText: {
    color: '#666',
    fontWeight: 'bold',
  },
  resetButton: {
    backgroundColor: '#232323',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  resetButtonText: {
    color: Colors.PRIMARY,
    fontSize: 16,
    fontWeight: 'bold',
  },
  devButton: {
    backgroundColor: '#232323',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'center',
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  devButtonText: {
    color: Colors.PRIMARY,
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default LoginScreen;