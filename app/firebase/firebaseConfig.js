import { initializeApp } from 'firebase/app';
import { initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';

const firebaseConfig = {
  apiKey: "AIzaSyCkA_vaznyg2SIN31nXJ9jYvRBHjrqFeM0",
  authDomain: "thewodleague.firebaseapp.com",
  projectId: "thewodleague",
  storageBucket: "thewodleague.firebasestorage.app",
  messagingSenderId: "1097805141061",
  appId: "1:1097805141061:ios:1f7a5b50943a38855d7f2c",
};

const app = initializeApp(firebaseConfig);
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});
const db = getFirestore(app);

//console.log('Firestore initialized:', db);
console.log('Firestore initialized');

export { app, auth, db };