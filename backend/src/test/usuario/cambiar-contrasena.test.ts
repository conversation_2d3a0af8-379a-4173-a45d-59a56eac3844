import { Test, TestingModule } from '@nestjs/testing';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { DatabaseTestHelper, UsuariosTestHelper } from '../helper';
/**
 * Test de cambio de contraseña
 * 
 * Este test verifica el proceso de cambio de contraseña de un usuario,
 * asegurando que la contraseña anterior se valida correctamente y
 * la nueva contraseña se guarda con hash.
 */
describe('Cambiar Contraseña Tests', () => {
  let usuariosService: UsuariosService;
  let testUserId: string;
  const originalPassword = 'password123';
  let testUser;
  const logger = new Logger('CambiarContraseñaTest');

  beforeAll(async () => {
    // Arrange: Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection('.env.development'))) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Configuración del módulo de testing usando el helper de usuarios
    const module: TestingModule = await UsuariosTestHelper.createUsuariosTestingModule();
    
    usuariosService = module.get<UsuariosService>(UsuariosService);

    // Crear un usuario de prueba
    const hashedPassword = await bcrypt.hash(originalPassword, 10);
    const testUserData = {
      nombre: 'Usuario Cambio Contraseña',
      alias: 'usuario_cambio_pass',
      email: '<EMAIL>',
      password: hashedPassword,
      nivel: NivelUsuario.INTERMEDIO,
      genero: GeneroUsuario.MASCULINO,
    };

    // Limpiar datos anteriores
    try {
      const existingUser = await usuariosService['usuariosRepository'].findOne({
        where: { email: testUserData.email },
      });
      
      if (existingUser) {
        await usuariosService['usuariosRepository'].delete(existingUser.id);
      }
    } catch (error) {
      // Ignoramos errores, el usuario posiblemente no existe
    }

    // Crear usuario nuevo para las pruebas
    testUser = await usuariosService['usuariosRepository'].save(
      usuariosService['usuariosRepository'].create(testUserData)
    );
    
    testUserId = testUser.id;
  });

  afterAll(async () => {
    // Limpiar: eliminar el usuario de prueba usando el helper
    try {
      await DatabaseTestHelper.cleanTestData(usuariosService['usuariosRepository'], { id: testUserId });
      logger.log('Datos de prueba limpiados correctamente');
    } catch (error) {
      logger.error(`Error al limpiar datos de prueba: ${error.message}`);
    }
  });

  /**
   * Implementation of password change functionality for testing
   * This method follows the same logic that would be in a real service
   * 1. Get the user
   * 2. Verify current password
   * 3. Update to new password
   */
  async function cambiarContrasena(
    userId: string, 
    currentPassword: string, 
    newPassword: string
  ) {
    // 1. Obtener el usuario
    const usuario = await usuariosService.findOne(userId);
    
    // 2. Verificar la contraseña actual
    const isPasswordValid = await bcrypt.compare(currentPassword, usuario.password);
    
    if (!isPasswordValid) {
      throw new UnauthorizedException('Contraseña actual incorrecta');
    }
    
    // 3. Actualizar a la nueva contraseña
    const updatedUser = await usuariosService.update(userId, { password: newPassword });
    
    return updatedUser;
  }

  // Test de cambio de contraseña con datos válidos
  it('debe cambiar la contraseña cuando se proporciona la contraseña actual correcta', async () => {
    // Arrange: Preparar datos para el test
    const nuevaContrasena = 'nuevaContrasena456!';
    
    // Act: Cambiar la contraseña
    const updatedUser = await cambiarContrasena(
      testUserId,
      originalPassword,
      nuevaContrasena
    );
    
    // Assert: Verificar que se actualizó el usuario
    expect(updatedUser).toBeDefined();
    expect(updatedUser.id).toBe(testUserId);
    
    // Verificar que la nueva contraseña está hasheada
    expect(updatedUser.password).not.toBe(nuevaContrasena);
    
    // Verificar que la nueva contraseña es válida
    const isNewPasswordValid = await bcrypt.compare(
      nuevaContrasena,
      updatedUser.password
    );
    expect(isNewPasswordValid).toBe(true);
    
    // Verificar que la contraseña antigua ya no es válida
    const isOldPasswordValid = await bcrypt.compare(
      originalPassword,
      updatedUser.password
    );
    expect(isOldPasswordValid).toBe(false);
  });

  // Test de cambio de contraseña con contraseña actual incorrecta
  it('debe rechazar el cambio de contraseña cuando la contraseña actual es incorrecta', async () => {
    // Arrange: Preparar datos para el test
    const contrasenaIncorrecta = 'contrasenaIncorrecta';
    const nuevaContrasena = 'otraNuevaContrasena789!';
    
    // Act & Assert: Intentar cambiar contraseña y verificar que lanza error
    await expect(cambiarContrasena(
      testUserId,
      contrasenaIncorrecta,
      nuevaContrasena
    )).rejects.toThrow(UnauthorizedException);
    
    // Verificar que la contraseña no cambió
    const usuario = await usuariosService.findOne(testUserId);
    const isNewPasswordValid = await bcrypt.compare(
      nuevaContrasena,
      usuario.password
    );
    expect(isNewPasswordValid).toBe(false);
  });

  // Test de robustez de contraseña nueva
  it('debe validar que la nueva contraseña cumple con los requisitos de seguridad', async () => {
    // Arrange: Definir la función de validación y casos de prueba
    
    /**
     * Password security validator function
     * Checks if a password meets the security requirements:  
     * - At least 8 characters
     * - At least one uppercase letter
     * - At least one lowercase letter
     * - At least one special character
     */
    function validarSeguridadContrasena(password: string): boolean {
      const longitudMinima = password.length >= 8;
      const tieneMayuscula = /[A-Z]/.test(password);
      const tieneMinuscula = /[a-z]/.test(password);
      const tieneCaracterEspecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
      
      return longitudMinima && tieneMayuscula && tieneMinuscula && tieneCaracterEspecial;
    }
    
    // Contraseñas a probar
    const contrasenaSegura = 'NuevaContraseña123!';
    const contrasenaMuyCorta = 'Abc1!';
    const contrasenaSinMayuscula = 'contraseña123!';
    const contrasenaSinMinuscula = 'CONTRASEÑA123!';
    const contrasenaSinEspecial = 'Contraseña123';
    
    // Act & Assert: Verificar cada caso de prueba
    expect(validarSeguridadContrasena(contrasenaSegura)).toBe(true);
    expect(validarSeguridadContrasena(contrasenaMuyCorta)).toBe(false);
    expect(validarSeguridadContrasena(contrasenaSinMayuscula)).toBe(false);
    expect(validarSeguridadContrasena(contrasenaSinMinuscula)).toBe(false);
    expect(validarSeguridadContrasena(contrasenaSinEspecial)).toBe(false);
  });
});
