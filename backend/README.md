# The WOD League - Backend

Backend para la plataforma The WOD League, una aplicación para gestionar competiciones de CrossFit.

## Descripción

Este proyecto utiliza [NestJS](https://github.com/nestjs/nest), un framework progresivo de Node.js para construir aplicaciones del lado del servidor eficientes y escalables.

## Configuración del Proyecto

Para configurar rápidamente el proyecto, ejecuta el script de configuración:

```bash
# Dar permisos de ejecución al script
chmod +x setup.sh

# Ejecutar el script de configuración
./setup.sh
```

O configura manualmente:

```bash
# Instalar dependencias
npm install

# Crear archivo .env (ver ejemplo en docs/Guia_Inicio_Rapido_Backend.md)
```

## Compilar y Ejecutar el Proyecto

```bash
# Modo desarrollo
npm run start:dev

# Modo producción
npm run build
npm run start:prod
```

## Ejecutar Tests

```bash
# Tests unitarios
npm test

# Tests específicos
npm test -- src/test/usuario.service.spec.ts

# Cobertura de tests
npm run test:cov
```

## Documentación

Para obtener información detallada sobre cómo configurar y utilizar el backend, consulta:

- [Guía de Inicio Rápido (Español)](../docs/Guia_Inicio_Rapido_Backend.md)
- [Quick Start Guide (English)](../docs/Quick_Start_Guide_Backend.md)

## Características Principales

- **Autenticación**: Registro e inicio de sesión de usuarios con JWT
- **Gestión de Usuarios**: Perfiles de usuario, roles y permisos
- **Gestión de Boxes**: Registro y administración de boxes de CrossFit
- **Gestión de Ligas**: Creación y administración de ligas y competiciones
- **Gestión de WODs**: Creación y publicación de WODs (Workout of the Day)
- **Resultados**: Registro y validación de resultados
- **Clasificaciones**: Cálculo de clasificaciones en tiempo real con Redis
- **WebSockets**: Actualizaciones en tiempo real de clasificaciones
- **Inscripciones**: Gestión de inscripciones a ligas

## Tecnologías Utilizadas

- **NestJS**: Framework de backend
- **TypeScript**: Lenguaje de programación
- **PostgreSQL**: Base de datos principal
- **TypeORM**: ORM para PostgreSQL
- **Redis**: Caché y clasificaciones en tiempo real
- **JWT**: Autenticación y autorización
- **WebSockets**: Comunicación en tiempo real
- **Jest**: Framework de testing

## Estructura del Proyecto

El backend está organizado en módulos:

- **Usuarios**: Gestión de usuarios y perfiles
- **Auth**: Autenticación y autorización
- **Boxes**: Gestión de boxes de CrossFit
- **Ligas**: Gestión de ligas y competiciones
- **WODs**: Gestión de WODs (Workout of the Day)
- **Resultados**: Registro y validación de resultados
- **Clasificaciones**: Cálculo de clasificaciones en tiempo real
- **Inscripciones**: Gestión de inscripciones a ligas

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo LICENSE para más detalles.
