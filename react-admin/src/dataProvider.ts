import { fetchUtils, DataProvider, <PERSON><PERSON><PERSON><PERSON>, Identifier } from 'react-admin';

// URL de la API con el prefijo correcto
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Mapeo de recursos de React Admin a endpoints del backend
type ResourceMap = {
  [key: string]: string;
};

const resourceMapping: ResourceMap = {
  users: 'usuarios',
  boxes: 'boxes',
  leagues: 'ligas',
  wods: 'wods',
  movimientos: 'movimientos',
  registrations: 'inscripciones',
  results: 'resultados',
  rankings: 'clasificaciones',
  resultados: 'resultados',
  clasificaciones: 'clasificaciones'
};

// Configurar el httpClient personalizado para incluir el token JWT
const httpClient = (url: string, options: any = {}) => {
  if (!options.headers) {
    options.headers = new Headers({ Accept: 'application/json' });
  }

  const token = localStorage.getItem('token');
  if (token) {
    options.headers.set('Authorization', `Bearer ${token}`);
  }

  return fetchUtils.fetchJson(url, options);
};

// Crear un dataProvider personalizado
const dataProvider: DataProvider = {
  getList: async (resource, params) => {
    // Mapear el recurso al endpoint correcto del backend
    const backendResource = resourceMapping[resource] || resource;
    
    // Caso especial para clasificaciones (rankings) ya que no tiene un endpoint general
    let url = `${API_URL}/${backendResource}`;
    
    if (resource === 'rankings' || resource === 'clasificaciones') {
      // Para evitar errores cuando no hay datos en la BD, vamos a manejar este caso especial
      try {
        // Primero verificamos si hay ligas disponibles
        const { json: ligas } = await httpClient(`${API_URL}/ligas`);

        if (!Array.isArray(ligas) || ligas.length === 0) {
          console.warn('No hay ligas disponibles en la base de datos');
          return {
            data: [],
            total: 0
          };
        }

        // Si hay ligas, usamos la primera disponible
        const ligaId = params.filter?.ligaId || ligas[0].id || '1';
        const categoria = params.filter?.categoria || 'RX-Masculino';
        url = `${API_URL}/clasificaciones/liga/${ligaId}/categoria/${categoria}`;
      } catch (error) {
        console.error('Error al obtener ligas:', error);
        return {
          data: [],
          total: 0
        };
      }
    }
    
    try {
      // Realizar la petición
      const { json } = await httpClient(url);
      
      // Verificar si json es un array
      const data = Array.isArray(json) ? json : (json.data || []);
      
      // Devolver los datos en el formato esperado por React Admin
      return {
        data: data,
        total: data.length,
      };
    } catch (error) {
      console.error(`Error al obtener datos de ${resource}:`, error);
      return {
        data: [],
        total: 0,
      };
    }
  },
  
  getOne: async (resource, params) => {
    try {
      const backendResource = resourceMapping[resource] || resource;
      const url = `${API_URL}/${backendResource}/${params.id}`;
      
      const { json } = await httpClient(url);
      
      // Asegurarnos de que el objeto tenga un id y todos los datos necesarios
      const responseData = json && typeof json === 'object' ? json : {};

      // Si no hay id, asignamos el que viene de params
      if (!responseData.id && params.id) {
        responseData.id = params.id;
      }

      // Si los datos vienen en formato anidado, los extraemos
      if (responseData.data && typeof responseData.data === 'object') {
        const extractedData = responseData.data;
        // Asegúrate de preservar el id
        if (!extractedData.id && params.id) {
          extractedData.id = params.id;
        }
        return { data: extractedData };
      }
      
      console.log(`Datos obtenidos para ${resource} (${params.id}):`, responseData);
      
      return {
        data: responseData,
      };
    } catch (error) {
      console.error(`Error en getOne para ${resource}:`, error);
      // Devolver un objeto vacío con el id solicitado para evitar errores de UI
      return {
        data: { id: params.id },
      };
    }
  },
  
  getMany: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const url = `${API_URL}/${backendResource}`;
    
    try {
      const { json } = await httpClient(url);
      
      // Asegurar que tengamos un array (puede venir como {data: [...]} o directamente como array)
      const items = Array.isArray(json) ? json : (json.data || []);
      console.log(`getMany para ${resource}:`, { ids: params.ids, recibidos: items.length });
      
      // Filtrar los resultados que coincidan con los IDs solicitados
      const data = items.filter((item: any) => params.ids.includes(item.id));
      
      return {
        data,
      };
    } catch (error) {
      console.error(`Error en getMany para ${resource}:`, error);
      return { data: [] };
    }
  },
  
  getManyReference: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const url = `${API_URL}/${backendResource}`;
    
    try {
      const { json } = await httpClient(url);
      
      // Asegurar que tengamos un array (puede venir como {data: [...]} o directamente como array)
      const items = Array.isArray(json) ? json : (json.data || []);
      console.log(`getManyReference para ${resource}:`, { target: params.target, id: params.id, recibidos: items.length });
      
      // Filtrar los resultados según el parámetro target
      const data = items.filter((item: any) => item[params.target] === params.id);
      
      return {
        data,
        total: data.length,
      };
    } catch (error) {
      console.error(`Error en getManyReference para ${resource}:`, error);
      return { data: [], total: 0 };
    }
  },
  
  create: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const url = `${API_URL}/${backendResource}`;
    
    // Filtramos los campos según el recurso
    let dataToSend = { ...params.data };
    
    if (resource === 'boxes') {
      // Para boxes, solo enviamos los campos permitidos en CreateBoxDto
      dataToSend = {};
      
      // Campos requeridos
      if (params.data.nombre !== undefined) {
        dataToSend.nombre = params.data.nombre;
      }
      if (params.data.ubicacion !== undefined) {
        dataToSend.ubicacion = params.data.ubicacion;
      }
      if (params.data.ownerId !== undefined) {
        dataToSend.ownerId = params.data.ownerId;
      }
      
      // Campos opcionales
      if (params.data.descripcion !== undefined) {
        dataToSend.descripcion = params.data.descripcion;
      }
      if (params.data.logo !== undefined) {
        dataToSend.logo = params.data.logo;
      }
      
      console.log(`Datos filtrados para crear box:`, dataToSend);
    }
    
    const { json } = await httpClient(url, {
      method: 'POST',
      body: JSON.stringify(dataToSend),
    });
    
    return {
      data: { ...params.data, id: json.id } as any,
    };
  },
  
  update: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const url = `${API_URL}/${backendResource}/${params.id}`;
    
    // Preparamos los datos a enviar
    let dataToSend: Record<string, any> = {};
    
    if (resource === 'users') {
      /**
       * USUARIOS: ACTUALIZACIÓN
       * 1. CAMPOS BÁSICOS (texto):
       *    - nombre (✓)
       *    - email (✓)
       *    - alias (✓)
       */
      if (params.data.nombre !== undefined) {
        dataToSend.nombre = params.data.nombre;
      }
      if (params.data.email !== undefined) {
        dataToSend.email = params.data.email;
      }
      if (params.data.alias !== undefined) {
        dataToSend.alias = params.data.alias;
      }
      
      /**
       * 2. CAMPOS NUMÉRICOS:
       *    - edad (✓) - convertir a número
       */
      if (params.data.edad !== undefined) {
        dataToSend.edad = Number(params.data.edad);
      }
      
      /**
       * 3. CAMPOS ENUM:
       *    - nivel (✓) - enum NivelUsuario (RX, Intermedio, Scaled)
       *    - genero (✓) - enum GeneroUsuario (Masculino, Femenino)
       *    - estado (✓) - enum EstadoUsuario (Activo, Inactivo, Suspendido)
       */
      if (params.data.nivel !== undefined) {
        dataToSend.nivel = params.data.nivel;
      }
      if (params.data.genero !== undefined) {
        dataToSend.genero = params.data.genero;
      }
      if (params.data.estado !== undefined) {
        dataToSend.estado = params.data.estado;
      }
      
      /**
       * 4. CAMPOS DE RELACIÓN:
       *    - boxId: asigna un box a un usuario
       * 
       * IMPORTANTE: El campo 'rol' NO debe enviarse en actualizaciones normales.
       * Para cambiar roles debes usar el método especial updateUserRole().
       */
      if (params.data.boxId !== undefined) {
        dataToSend.boxId = params.data.boxId;
      }
      
      // Si se intentó actualizar el rol, ignoramos este campo y mostramos un mensaje
      if (params.data.rol !== undefined) {
        console.warn('IMPORTANTE: El campo "rol" no puede actualizarse mediante el método normal update. ' +
          'Por favor, usa la función updateUserRole(userId, newRole, boxId) que utiliza el endpoint protegido.');
        // NO incluimos dataToSend.rol = params.data.rol; para evitar el error
      }
      
      // Boolean fields (setupCompleted, onBoarding, emailVerificado)
      if (params.data.setupCompleted !== undefined) {
        dataToSend.setupCompleted = Boolean(params.data.setupCompleted);
      }
      if (params.data.onBoarding !== undefined) {
        dataToSend.onBoarding = Boolean(params.data.onBoarding);
      }
      if (params.data.emailVerificado !== undefined) {
        dataToSend.emailVerificado = Boolean(params.data.emailVerificado);
      }
      
      // Eliminar campos vacíos o nulos
      console.log(`Datos enviados:`, dataToSend);
    } else {
      // Para otros recursos, mantenemos la estrategia anterior pero excluimos campos automáticos
      dataToSend = { ...params.data };
      delete dataToSend.id; // Eliminar el ID para evitar conflictos
      delete dataToSend.createdAt; // Eliminar campos de timestamp automáticos
      delete dataToSend.updatedAt; // Eliminar campos de timestamp automáticos
    }
    
    // Si no hay datos para enviar, devolvemos el registro original
    if (Object.keys(dataToSend).length === 0) {
      return {
        data: params.data,
      };
    }
    
    console.log(`Datos mínimos enviados para actualizar ${resource} (${params.id}):`, dataToSend);
    
    try {
      const { json } = await httpClient(url, {
        method: 'PATCH',
        body: JSON.stringify(dataToSend),
      });
      
      console.log(`Actualización exitosa para ${resource} (${params.id})`);
      
      return {
        data: { ...params.previousData, ...dataToSend, id: params.id } as any,
      };
    } catch (error: any) {
      // Capturamos y mostramos más información sobre el error
      console.error(`Error al actualizar ${resource} (${params.id}):`, {
        status: error.status,
        message: error.message,
        body: error.body ? JSON.stringify(error.body) : 'Sin cuerpo de error'
      });
      
      throw error;
    }
  },
  
  updateMany: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const responses = await Promise.all(
      params.ids.map(id =>
        httpClient(`${API_URL}/${backendResource}/${id}`, {
          method: 'PATCH',
          body: JSON.stringify(params.data),
        })
      )
    );
    
    return {
      data: params.ids,
    };
  },
  
  delete: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    const url = `${API_URL}/${backendResource}/${params.id}`;
    
    await httpClient(url, {
      method: 'DELETE',
    });
    
    return {
      data: params.previousData as any,
    };
  },
  
  deleteMany: async (resource, params) => {
    const backendResource = resourceMapping[resource] || resource;
    await Promise.all(
      params.ids.map(id =>
        httpClient(`${API_URL}/${backendResource}/${id}`, {
          method: 'DELETE',
        })
      )
    );
    
    return {
      data: params.ids,
    };
  },
};

/**
 * Método personalizado para actualizar el rol de un usuario
 * Usa el endpoint protegido /usuarios/:id/rol que requiere permisos de Admin
 * 
 * @param userId - ID del usuario a actualizar
 * @param role - Nuevo rol ('Usuario', 'BoxOwner', 'Admin')
 * @param boxId - ID del box si el rol es BoxOwner (opcional)
 * @returns Promesa con el usuario actualizado o error
 */
export const updateUserRole = async (userId: string, role: string, boxId?: string): Promise<any> => {
  // Validamos que si el rol es BoxOwner, debe tener un boxId asignado
  if (role === 'BoxOwner') {
    if (!boxId) {
      return { 
        success: false, 
        error: 'Un BoxOwner debe tener un box asignado' 
      };
    }
    
    // Primero asignamos el boxId al usuario
    try {
      await dataProvider.update('users', {
        id: userId,
        data: { boxId },
        previousData: {},
      });
      console.log(`BoxId ${boxId} asignado exitosamente al usuario ${userId}`);
    } catch (error) {
      console.error('Error al asignar boxId:', error);
      return { success: false, error: 'Error al asignar boxId' };
    }
  }

  // Configurar la petición para actualizar el rol
  const options: any = {
    method: 'PATCH',
    headers: new Headers({ 'Content-Type': 'application/json' }),
    body: JSON.stringify({ rol: role }),
  };

  // Añadir token de autorización
  const token = localStorage.getItem('token');
  if (token) {
    options.headers.set('Authorization', `Bearer ${token}`);
  }

  try {
    // Realizar la petición al endpoint protegido
    const { json } = await fetchUtils.fetchJson(
      `${API_URL}/usuarios/${userId}/rol`, 
      options
    );
    
    console.log(`Rol de usuario actualizado exitosamente a ${role}`);
    
    return {
      data: json,
      success: true 
    };
  } catch (error: unknown) {
    console.error('Error al actualizar rol de usuario:', error);
    
    // Manejo seguro del tipo unknown
    const errorMessage = error instanceof Error 
      ? error.message 
      : 'Error desconocido al actualizar rol de usuario';
      
    return {
      error: errorMessage,
      success: false
    };
  }
};

export default dataProvider;
