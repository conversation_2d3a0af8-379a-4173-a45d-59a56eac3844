import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Switch
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const NotificationSettingsScreen = ({ navigation }) => {
  // Estados para las configuraciones de notificaciones
  const [generalNotifications, setGeneralNotifications] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [screenLockEnabled, setScreenLockEnabled] = useState(false);
  const [remindersEnabled, setRemindersEnabled] = useState(true);

  // Cargar configuraciones guardadas
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await AsyncStorage.getItem('notification-settings');

        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          setGeneralNotifications(settings.general ?? true);
          setSoundEnabled(settings.sound ?? true);
          setScreenLockEnabled(settings.screenLock ?? false);
          setRemindersEnabled(settings.reminders ?? true);
        }
      } catch (error) {
        console.error('Error al cargar configuraciones de notificaciones:', error);
      }
    };

    loadSettings();
  }, []);

  // Guardar configuraciones cuando cambian
  useEffect(() => {
    const saveSettings = async () => {
      try {
        const settings = {
          general: generalNotifications,
          sound: soundEnabled,
          screenLock: screenLockEnabled,
          reminders: remindersEnabled
        };

        await AsyncStorage.setItem('notification-settings', JSON.stringify(settings));
      } catch (error) {
        console.error('Error al guardar configuraciones de notificaciones:', error);
      }
    };

    saveSettings();
  }, [generalNotifications, soundEnabled, screenLockEnabled, remindersEnabled]);

  // Renderizar un elemento de configuración con switch
  const renderSwitchOption = (label, value, onValueChange) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingLabel}>{label}</Text>
      <Switch
        trackColor={{ false: '#3e3e3e', true: '#e0fe10' }}
        thumbColor={value ? '#ffffff' : '#f4f3f4'}
        ios_backgroundColor="#3e3e3e"
        onValueChange={onValueChange}
        value={value}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Ajustes De Notificaciones</Text>
        </TouchableOpacity>
      </View>

      {/* Notification Settings */}
      <View style={styles.settingsContainer}>
        {renderSwitchOption('Notificaciones generales', generalNotifications, setGeneralNotifications)}
        {renderSwitchOption('Sonido', soundEnabled, setSoundEnabled)}
        {renderSwitchOption('Bloquear pantalla', screenLockEnabled, setScreenLockEnabled)}
        {renderSwitchOption('Recordatorios', remindersEnabled, setRemindersEnabled)}
      </View>


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#3e3e3e',
  },
  settingLabel: {
    fontSize: 16,
    color: '#ffffff',
  },

});

export default NotificationSettingsScreen;
