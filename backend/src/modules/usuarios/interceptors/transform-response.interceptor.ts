import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from '../interfaces/api-response.interface';

/**
 * Interceptor that transforms all responses to follow a standard format
 * This ensures a consistent API across all endpoints
 */
@Injectable()
export class TransformResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  /**
   * Intercept the response and transform it to the standard format
   * @param context Execution context
   * @param next Next handler in chain
   * @returns Transformed response as Observable
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    // Process the response data and transform it to the standard format
    return next.handle().pipe(
      map(data => ({
        success: true,
        message: 'Operation successful',
        data
      }))
    );
  }
}
