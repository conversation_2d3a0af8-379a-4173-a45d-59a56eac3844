import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { WOD, EstadoWOD } from './entities/wod.entity';
import { CreateWodDto } from './dto/create-wod.dto';
import { UpdateWodDto } from './dto/update-wod.dto';
import { LigasService } from '../ligas/ligas.service';

@Injectable()
export class WodsService {
  constructor(
    @InjectRepository(WOD)
    private wodsRepository: Repository<WOD>,
    private ligasService: LigasService,
  ) {}

  async create(createWodDto: CreateWodDto): Promise<WOD> {
    // Verificar que la liga existe
    await this.ligasService.findOne(createWodDto.ligaId);

    // Verificar que la fecha límite sea posterior a la fecha de publicación
    if (createWodDto.fechaLimite <= createWodDto.fechaPublicacion) {
      throw new ConflictException('La fecha límite debe ser posterior a la fecha de publicación');
    }

    // Crear el nuevo WOD
    const newWod = this.wodsRepository.create(createWodDto);
    return this.wodsRepository.save(newWod);
  }

  async findAll(): Promise<WOD[]> {
    return this.wodsRepository.find({
      relations: ['liga'],
    });
  }

  async findOne(id: string): Promise<WOD> {
    const wod = await this.wodsRepository.findOne({
      where: { id },
      relations: ['liga'],
    });

    if (!wod) {
      throw new NotFoundException(`WOD con ID ${id} no encontrado`);
    }

    return wod;
  }

  async update(id: string, updateWodDto: UpdateWodDto): Promise<WOD> {
    const wod = await this.findOne(id);

    // Verificar que la fecha límite sea posterior a la fecha de publicación si se están actualizando
    if (updateWodDto.fechaPublicacion && updateWodDto.fechaLimite) {
      if (updateWodDto.fechaLimite <= updateWodDto.fechaPublicacion) {
        throw new ConflictException('La fecha límite debe ser posterior a la fecha de publicación');
      }
    } else if (updateWodDto.fechaPublicacion && wod.fechaLimite <= updateWodDto.fechaPublicacion) {
      throw new ConflictException('La fecha límite debe ser posterior a la fecha de publicación');
    } else if (updateWodDto.fechaLimite && updateWodDto.fechaLimite <= wod.fechaPublicacion) {
      throw new ConflictException('La fecha límite debe ser posterior a la fecha de publicación');
    }

    // Actualizar el WOD
    const updatedWod = this.wodsRepository.merge(wod, updateWodDto);
    return this.wodsRepository.save(updatedWod);
  }

  async remove(id: string): Promise<void> {
    const result = await this.wodsRepository.delete(id);
    
    if (result.affected === 0) {
      throw new NotFoundException(`WOD con ID ${id} no encontrado`);
    }
  }

  async findByLiga(ligaId: string): Promise<WOD[]> {
    return this.wodsRepository.find({
      where: { ligaId },
      relations: ['liga'],
    });
  }

  async findActivos(): Promise<WOD[]> {
    const today = new Date();
    
    return this.wodsRepository.find({
      where: {
        estado: EstadoWOD.PUBLICADO,
        fechaPublicacion: LessThanOrEqual(today),
        fechaLimite: MoreThanOrEqual(today),
      },
      relations: ['liga'],
    });
  }

  async publicarWod(id: string): Promise<WOD> {
    const wod = await this.findOne(id);
    
    if (wod.estado !== EstadoWOD.BORRADOR) {
      throw new ConflictException('Solo se pueden publicar WODs en estado de borrador');
    }
    
    wod.estado = EstadoWOD.PUBLICADO;
    return this.wodsRepository.save(wod);
  }

  async cerrarWod(id: string): Promise<WOD> {
    const wod = await this.findOne(id);
    
    if (wod.estado !== EstadoWOD.PUBLICADO) {
      throw new ConflictException('Solo se pueden cerrar WODs publicados');
    }
    
    wod.estado = EstadoWOD.CERRADO;
    return this.wodsRepository.save(wod);
  }
}
