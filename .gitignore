# compiled output
*/dist*
*/node_modules*
*/build*
setup.sh

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
.AppleDouble
.LSOverride
thumbs.db
Thumbs.db
ehthumbs.db
Desktop.ini

# Tests
/coverage
/.nyc_output
/test-results/
/playwright-report/
/playwright/.cache/

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# dotenv environment variable files
.env
.env.*
.env.development
.env.test
.env.production

# temp directory
.temp
.tmp
.cache/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# TypeScript
*.tsbuildinfo

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Backend - NestJS
/backend/node_modules/*
/backend/dist/*
/backend/uploads/*

# app dependencies
/app/node_modules/
/app/.pnp
/app/.pnp.js

# app testing
/app/coverage/

# app production
/app/build/
/app/dist/

# app misc
/app/**/.env*
/app/**/.DS_Store
/app/**/.env.local
/app/**/.env.development.local
/app/**/.env.test.local
/app/**/.env.production.local

# iOS specific
/app/ios/build/
/app/ios/Pods/
/app/ios/Podfile.lock
/app/ios/*.xcworkspace
/app/ios/*.xcodeproj/*
!/app/ios/*.xcodeproj/project.pbxproj
!/app/ios/*.xcodeproj/xcshareddata/
!/app/ios/*.xcworkspace/contents.xcworkspacedata
/app/ios/.xcode.env.local
/app/ios/DerivedData/

# Android specific
/app/android/build/
/app/android/.gradle
/app/android/app/build/
/app/android/local.properties
/app/android/.idea/
/app/android/*.iml
/app/android/gradle/
/app/android/gradlew
/app/android/gradlew.bat
/app/android/captures/
/app/android/app/release/

# Expo
/app/node_modules/
/app/.expo/
/app/web-build/
/app/dist/
/app/npm-debug.*
/app/*.jks
/app/*.p8
/app/*.p12
/app/*.key
/app/*.mobileprovision
/app/*.orig.*

# CocoaPods
/app/ios/Pods/

# Database
*.sqlite
*.sqlite3
*.db
