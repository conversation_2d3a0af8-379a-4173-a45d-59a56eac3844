import { IsNotEmpty, IsString, MinLength, Matches } from 'class-validator';

/**
 * DTO para restablecimiento de contraseña
 */
export class ResetPasswordDto {
  /**
   * Token de restablecimiento de contraseña
   * @example "a1b2c3d4e5f6..."
   */
  @IsString({ message: 'El token debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'El token es obligatorio' })
  token: string;

  /**
   * Nueva contraseña del usuario
   * Debe tener al menos 8 caracteres, incluir una mayúscula, 
   * una minúscula, un número y un carácter especial
   * @example "Password123!"
   */
  @IsString({ message: 'La contraseña debe ser una cadena de texto' })
  @IsNotEmpty({ message: 'La contraseña es obligatoria' })
  @MinLength(8, { message: 'La contraseña debe tener al menos 8 caracteres' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.,\-_])[A-Za-z\d@$!%*?&.,\-_]/,
    { message: 'La contraseña debe contener al menos una mayúscula, una minúscula, un número y un carácter especial (!@#$%&*.,-)' }
  )
  password: string;
}
