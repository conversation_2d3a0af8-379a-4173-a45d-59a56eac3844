import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Linking,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';

const HelpScreen = ({ navigation }) => {
  const { user } = useAuth();

  // Estado para controlar la pestaña activa (FAQ o Contact)
  const [activeTab, setActiveTab] = useState('faq');

  // Estados para el formulario de contacto
  const [name, setName] = useState(user?.displayName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  // Estado para las preguntas expandibles
  const [expandedFaq, setExpandedFaq] = useState(null);

  // Función para abrir enlaces externos
  const openLink = (url) => {
    Linking.canOpenURL(url).then(supported => {
      if (supported) {
        Linking.openURL(url);
      } else {
        console.log("No se puede abrir la URL: " + url);
      }
    });
  };

  // Función para renderizar un elemento de ayuda con icono
  const renderHelpItem = (icon, title, onPress) => (
    <TouchableOpacity style={styles.helpItem} onPress={onPress}>
      <View style={styles.helpIconContainer}>
        {icon}
      </View>
      <Text style={styles.helpItemText}>{title}</Text>
      <Ionicons name="chevron-down" size={24} color="#e0fe10" />
    </TouchableOpacity>
  );

  // Función para enviar el mensaje de contacto
  const handleSubmit = async () => {
    // Validar campos
    if (!name.trim() || !email.trim() || !subject.trim() || !message.trim()) {
      Alert.alert('Error', 'Por favor, completa todos los campos');
      return;
    }

    setLoading(true);

    try {
      // Aquí iría la lógica para enviar el mensaje al backend
      // Por ahora, simulamos una espera
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mostrar mensaje de éxito
      Alert.alert(
        'Mensaje enviado',
        'Gracias por contactarnos. Te responderemos lo antes posible.',
        [{ text: 'OK' }]
      );

      // Limpiar el formulario
      setSubject('');
      setMessage('');
    } catch (error) {
      console.error('Error al enviar mensaje:', error);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo más tarde.');
    } finally {
      setLoading(false);
    }
  };

  // Datos de preguntas frecuentes
  const faqData = [
    {
      id: 1,
      title: '¿Cómo puedo participar en una competición?',
      content: 'Para participar en una competición, debes registrarte en la sección "Competiciones" de la aplicación. Selecciona la competición que te interese y sigue las instrucciones para inscribirte.'
    },
    {
      id: 2,
      title: '¿Cómo se calculan los puntos en el ranking?',
      content: 'Los puntos en el ranking se calculan en base a tu posición en las competiciones, el nivel de dificultad de la competición y tu rendimiento en comparación con otros atletas.'
    },
    {
      id: 3,
      title: '¿Puedo cambiar mi categoría después de registrarme?',
      content: 'Sí, puedes cambiar tu categoría en la sección de "Perfil". Sin embargo, ten en cuenta que algunos cambios pueden afectar tu participación en competiciones ya registradas.'
    },
    {
      id: 4,
      title: '¿Cómo puedo subir mis resultados?',
      content: 'Para subir tus resultados, ve a la sección "Mis Competiciones", selecciona la competición activa y toca en "Subir Resultado". Puedes incluir fotos o videos como evidencia.'
    },
    {
      id: 5,
      title: '¿Qué hago si tengo problemas técnicos?',
      content: 'Si experimentas problemas técnicos, puedes contactarnos a través de la sección "Contacto" o enviarnos un mensaje directo a través de nuestras redes sociales.'
    }
  ];

  // Renderizar el contenido de la pestaña FAQ
  const renderFaqContent = () => (
    <View style={styles.tabContent}>
      {/* Política de privacidad */}
      <TouchableOpacity
        style={styles.expandableContainer}
        onPress={() => setExpandedFaq(expandedFaq === 'privacy' ? null : 'privacy')}
      >
        <View style={styles.expandableHeader}>
          <Text style={styles.expandableTitle}>Política de privacidad</Text>
          <Ionicons
            name={expandedFaq === 'privacy' ? "chevron-up" : "chevron-down"}
            size={24}
            color="#e0fe10"
          />
        </View>

        {expandedFaq === 'privacy' && (
          <View style={styles.expandableContent}>
            <Text style={styles.expandableText}>
              En The WOD League, nos tomamos muy en serio la privacidad de nuestros usuarios. Recopilamos información personal únicamente con el propósito de mejorar tu experiencia en la aplicación y para gestionar tu participación en competiciones. No compartimos tu información con terceros sin tu consentimiento explícito. Puedes solicitar la eliminación de tus datos en cualquier momento a través de la sección de contacto.
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Preguntas frecuentes */}
      {faqData.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={styles.expandableContainer}
          onPress={() => setExpandedFaq(expandedFaq === item.id ? null : item.id)}
        >
          <View style={styles.expandableHeader}>
            <Text style={styles.expandableTitle}>{item.title}</Text>
            <Ionicons
              name={expandedFaq === item.id ? "chevron-up" : "chevron-down"}
              size={24}
              color="#e0fe10"
            />
          </View>

          {expandedFaq === item.id && (
            <View style={styles.expandableContent}>
              <Text style={styles.expandableText}>{item.content}</Text>
            </View>
          )}
        </TouchableOpacity>
      ))}

      {/* Lista de opciones de ayuda */}
      <View style={styles.helpOptionsContainer}>
        <Text style={styles.helpOptionsTitle}>Otras formas de contacto</Text>

        {renderHelpItem(
          <FontAwesome name="headphones" size={24} color="#fff" />,
          'Customer service',
          () => navigation.navigate('CustomerService')
        )}

        {renderHelpItem(
          <FontAwesome name="globe" size={24} color="#fff" />,
          'Website',
          () => openLink('https://thewodleague.com')
        )}

        {renderHelpItem(
          <FontAwesome name="whatsapp" size={24} color="#fff" />,
          'Whatsapp',
          () => openLink('https://wa.me/yourphonenumber')
        )}

        {renderHelpItem(
          <FontAwesome name="facebook" size={24} color="#fff" />,
          'Facebook',
          () => openLink('https://facebook.com/thewodleague')
        )}

        {renderHelpItem(
          <FontAwesome name="instagram" size={24} color="#fff" />,
          'Instagram',
          () => openLink('https://instagram.com/thewodleague')
        )}
      </View>
    </View>
  );

  // Renderizar el contenido de la pestaña Contact
  const renderContactContent = () => (
    <View style={styles.tabContent}>
      {/* Formulario de contacto */}
      <View style={styles.formContainer}>
        <Text style={styles.formLabel}>Nombre</Text>
        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Tu nombre"
          placeholderTextColor="#999"
        />

        <Text style={styles.formLabel}>Email</Text>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder="<EMAIL>"
          placeholderTextColor="#999"
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <Text style={styles.formLabel}>Asunto</Text>
        <TextInput
          style={styles.input}
          value={subject}
          onChangeText={setSubject}
          placeholder="Asunto de tu mensaje"
          placeholderTextColor="#999"
        />

        <Text style={styles.formLabel}>Mensaje</Text>
        <TextInput
          style={[styles.input, styles.messageInput]}
          value={message}
          onChangeText={setMessage}
          placeholder="Escribe tu mensaje aquí..."
          placeholderTextColor="#999"
          multiline
          numberOfLines={6}
          textAlignVertical="top"
        />

        {/* Botón de enviar */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#e0fe10" />
            <Text style={styles.loadingText}>Enviando mensaje...</Text>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
          >
            <Text style={styles.submitButtonText}>Enviar mensaje</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Ayuda & FAQs</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <Text style={styles.title}>How Can We Help You?</Text>

          {/* Botones de navegación (pestañas) */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, activeTab === 'faq' ? styles.activeButton : styles.inactiveButton]}
              onPress={() => setActiveTab('faq')}
            >
              <Text style={activeTab === 'faq' ? styles.activeButtonText : styles.inactiveButtonText}>FAQ</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, activeTab === 'contact' ? styles.activeButton : styles.inactiveButton]}
              onPress={() => setActiveTab('contact')}
            >
              <Text style={activeTab === 'contact' ? styles.activeButtonText : styles.inactiveButtonText}>Contact Us</Text>
            </TouchableOpacity>
          </View>

          {/* Contenido de la pestaña activa */}
          {activeTab === 'faq' ? renderFaqContent() : renderContactContent()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 30,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    width: '48%',
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: '#e0fe10',
  },
  inactiveButton: {
    backgroundColor: '#ffffff',
  },
  activeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#232323',
  },
  inactiveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#232323',
  },
  tabContent: {
    marginTop: 10,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2e2e2e',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  helpIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#598392',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  helpItemText: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '500',
  },
  helpOptionsContainer: {
    marginTop: 30,
  },
  helpOptionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginBottom: 15,
  },
  expandableContainer: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#3e3e3e',
    paddingBottom: 15,
  },
  expandableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  expandableTitle: {
    fontSize: 16,
    color: '#e0fe10',
    flex: 1,
  },
  expandableContent: {
    paddingTop: 10,
    paddingBottom: 5,
  },
  expandableText: {
    fontSize: 14,
    color: '#ffffff',
    lineHeight: 20,
  },
  formContainer: {
    marginTop: 10,
  },
  formLabel: {
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#3e3e3e',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 20,
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#e0fe10',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#232323',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    padding: 20,
  },
  loadingText: {
    color: '#e0fe10',
    marginTop: 10,
    fontSize: 16,
  },
});

export default HelpScreen;
