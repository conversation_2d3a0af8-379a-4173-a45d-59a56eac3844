import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    ActivityIndicator,
    TouchableOpacity,
    Image,
    Alert,
    Platform,
    RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContextNest';
import { useUser } from '../context/UserContext';
import { NESTJS_URL } from '../config';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import Toast from 'react-native-toast-message';

/**
 * Función auxiliar para mostrar mensajes en la aplicación
 * @param {string} message - Mensaje a mostrar
 * @param {string} type - Tipo de mensaje (success, error, info)
 */
const showMessage = (message, type = 'error') => {
    Toast.show({
        type: type,
        text1: type === 'error' ? 'Error' : type === 'success' ? 'Éxito' : 'Información',
        text2: message,
        position: 'bottom',
        visibilityTime: 3000,
    });
};

/**
 * Profile screen component
 * Displays user profile information and statistics
 * Uses the new session persistence pattern with UserContext
 * @param {object} navigation - React Navigation object
 */
const ProfileScreen = ({ navigation }) => {
    // Global authentication and user data contexts
    const { user, logout } = useAuth();
    const { 
        userData, 
        userStats, 
        isLoading, 
        error: userError,
        refreshUserData,
        uploadProfilePhoto 
    } = useUser();

    // Local UI states
    const [localError, setLocalError] = useState(null);
    const [isUploadingImage, setIsUploadingImage] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [profileImage, setProfileImage] = useState(null);
    
    // Computed states based on userData and userStats with specific field names
    const profileData = {
        name: userData?.nombre || '',
        username: userData?.alias || '',
        email: userData?.email || '',
        category: userData?.nivel || '-',
        verified: Boolean(userData?.verified)
    };
    
    const stats = {
        matches: userStats?.partidasJugadas || userStats?.matches || 0,
        victories: userStats?.victorias || userStats?.victories || 0
    };

    /**
     * Request gallery permissions and update profile image from userData when component mounts
     */
    useEffect(() => {
        let isMounted = true;
        
        const initializeComponent = async () => {
            try {
                // Request permissions to access the gallery
                const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
                if (status !== 'granted' && isMounted) {
                    console.log('Gallery permissions denied');
                    showMessage('Necesitamos permisos para acceder a tu galería de imágenes', 'info');
                }
                
                // Check for profile image in userData
                if (userData && isMounted) {
                    updateLocalProfileImage();
                }
            } catch (error) {
                console.error('Error in component initialization:', error);
                if (isMounted) {
                    setLocalError('Error al inicializar el componente');
                }
            }
        };
        
        initializeComponent();
        
        return () => { isMounted = false; };
    }, []);
    
    /**
     * Update local profile image when userData changes
     */
    useEffect(() => {
        if (userData) {
            updateLocalProfileImage();
        }
    }, [userData]);

    /**
     * Update local profile image state based on userData from context
     */
    const updateLocalProfileImage = useCallback(() => {
        if (!userData) return;
        
        // Check for profile image in different possible fields
        const photoUrl = userData.photoUrl || userData.foto || userData.avatar;
        
        if (photoUrl) {
            // Format URL with cache-busting parameter
            const timestamp = new Date().getTime();
            const isAbsoluteUrl = photoUrl.startsWith('http');
            const formattedPath = !isAbsoluteUrl && !photoUrl.startsWith('/') ? 
                `/${photoUrl}` : photoUrl;
                
            const imageUrl = isAbsoluteUrl
                ? `${photoUrl}?t=${timestamp}`
                : `${NESTJS_URL}${formattedPath}?t=${timestamp}`;
                
            // Update the profile image state
            setProfileImage({ uri: imageUrl });
        } else {
            // No image available
            setProfileImage(null);
        }
    }, [userData]);
    
    /**
     * Handle pull-to-refresh action
     */
    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await refreshUserData(true); // true = show feedback
        } catch (error) {
            console.error('Error refreshing profile:', error);
        } finally {
            setRefreshing(false);
        }
    };
    
    /**
     * Handle image selection from gallery and upload using the UserContext
     */
    const handleProfileImageUpdate = async () => {
        if (isUploadingImage) return;
        
        try {
            setIsUploadingImage(true);
            setLocalError(null);
            
            // Launch image picker
            const result = await ImagePicker.launchImageLibraryAsync({
                allowsEditing: true,
                aspect: [1, 1],
                quality: 0.8,
                mediaTypes: ImagePicker.MediaTypeOptions.Images
            });
            
            // Check if user canceled
            if (result.canceled || !result.assets || result.assets.length === 0) {
                return;
            }
            
            // Process and upload image
            await processAndUploadImage(result.assets[0].uri);
            
        } catch (error) {
            console.error('Image selection error:', error);
            showMessage('No se pudo acceder a la galería', 'error');
        } finally {
            setIsUploadingImage(false);
        }
    };
    
    /**
     * Process and upload profile image
     */
    const processAndUploadImage = async (imageUri) => {
        try {
            // 1. Validate image
            const fileInfo = await FileSystem.getInfoAsync(imageUri);
            if (!validateImage(imageUri, fileInfo.size)) {
                return false;
            }
            
            // 2. Optimize image
            const optimizedImage = await ImageManipulator.manipulateAsync(
                imageUri,
                [{ resize: { width: 512 } }],
                { compress: 0.6, format: ImageManipulator.SaveFormat.JPEG }
            );
            
            // 3. Convert to base64
            const base64Image = await FileSystem.readAsStringAsync(optimizedImage.uri, {
                encoding: FileSystem.EncodingType.Base64
            });
            
            // 4. Prepare image data
            const fileType = 'jpeg';
            const mimeType = 'image/jpeg';
            const username = profileData.username || 'user';
            const filename = generateSafeFilename(username);
            const fullFileName = `${filename}.${fileType}`;
            const dataUri = `data:${mimeType};base64,${base64Image}`;
            
            // 5. Upload through context
            await uploadProfilePhoto(dataUri, fullFileName);
            
            return true;
        } catch (error) {
            console.error('Image processing error:', error);
            showMessage('Error al procesar la imagen', 'error');
            return false;
        }
    };
    
    /**
     * Validate image format and size
     */
    const validateImage = (uri, fileSize) => {
        // Check extension
        const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        const extension = uri.split('.').pop().toLowerCase();
        
        if (!validExtensions.includes(extension)) {
            showMessage('Formato de imagen no válido. Use JPG, PNG o WebP', 'error');
            return false;
        }
        
        // Check size (max 5MB)
        const MAX_SIZE = 5 * 1024 * 1024;
        if (fileSize && fileSize > MAX_SIZE) {
            showMessage('La imagen es demasiado grande (máx 5MB)', 'error');
            return false;
        }
        
        return true;
    };
    
    /**
     * Generate a safe filename with timestamp
     */
    const generateSafeFilename = (username) => {
        const sanitizedUsername = (username || 'user')
            .replace(/[^a-zA-Z0-9]/g, '-')
            .toLowerCase();
            
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        return `${sanitizedUsername}_${timestamp}`;
    };

    /**
     * Efecto para cargar datos iniciales
     * Usa el contexto para obtener y actualizar datos del usuario
     */
    useEffect(() => {
        // Si tenemos usuario autenticado pero no datos, intentar cargarlos
        if (user && !userData && !isLoading) {
            refreshUserData(false);
        }
        
        // Si cambia el usuario, actualiza los datos
        if (user && userData) {
            updateLocalProfileImage();
        }
    }, [user, userData, isLoading, refreshUserData]);
    
    /**
     * Mostrar mensajes de error cuando el contexto reporta errores
     */
    useEffect(() => {
        if (userError) {
            console.error('Error del contexto de usuario:', userError);
            setLocalError(userError);
        }
    }, [userError]);
    
    /**
     * Error handling helper
     */
    const getError = () => userError || localError;
    
    /**
     * Handle logout action
     */
    const handleLogout = async () => {
        try {
            // Show loading indicator
            await refreshUserData(false);
            await logout();
            
            // Navigate to auth screen
            navigation.reset({
                index: 0,
                routes: [{ name: 'Auth' }]
            });
        } catch (error) {
            console.error('Logout error:', error);
            showMessage('No se pudo cerrar sesión correctamente', 'error');
        }
    };
    
    /**
     * Handle menu navigation options
     */
    const handleMenuOption = (option) => {
        switch (option) {
            case 'editProfile':
                navigation.navigate('EditProfile');
                break;
            case 'results':
                navigation.navigate('Results');
                break;
            case 'settings':
                navigation.navigate('Settings');
                break;
            case 'help':
                navigation.navigate('Help');
                break;
            case 'logout':
                handleLogout();
                break;
            default:
                break;
        }
    };
    
    /**
     * Render a menu option with icon and label
     */
    const renderMenuOption = (iconName, label, optionKey, showChevron = true) => (
        <TouchableOpacity
            style={styles.menuItem}
            onPress={() => handleMenuOption(optionKey)}
            activeOpacity={0.7}
        >
            <View style={styles.menuIconContainer}>
                <Ionicons name={iconName} size={24} color="#fff" />
            </View>
            <Text style={styles.menuItemText}>{label}</Text>
            {showChevron && (
                <Ionicons
                    name="chevron-forward"
                    size={20}
                    color="#e0fe10"
                    style={styles.menuItemChevron}
                />
            )}
        </TouchableOpacity>
    );
    
    /**
     * Render loading, error or main profile content
     */
    const renderContent = () => {
        // Show loading state
        if (isLoading && !userData) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#e0fe10" />
                    <Text style={styles.loadingText}>Cargando perfil...</Text>
                </View>
            );
        }
        
        // Show error state
        const error = getError();
        if (error && !userData) {
            return (
                <View style={styles.loadingContainer}>
                    <Text style={styles.errorText}>{error}</Text>
                    <TouchableOpacity
                        style={styles.retryButton}
                        onPress={() => refreshUserData(true)}
                    >
                        <Text style={styles.retryButtonText}>Reintentar</Text>
                    </TouchableOpacity>
                </View>
            );
        }
        
        // No data available
        if (!userData) {
            return (
                <View style={styles.loadingContainer}>
                    <Text style={styles.errorText}>No se pudieron cargar los datos del perfil</Text>
                    <TouchableOpacity 
                        style={styles.retryButton}
                        onPress={() => refreshUserData(true)}
                    >
                        <Text style={styles.retryButtonText}>Reintentar</Text>
                    </TouchableOpacity>
                </View>
            );
        }
        
        // Main profile content
        return (
            <ScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={['#e0fe10']}
                        tintColor="#e0fe10"
                    />
                }
            >
                {/* Profile section */}
                <View style={styles.profileSection}>
                    {/* Avatar */}
                    <View style={styles.avatarContainer}>
                        {profileImage ? (
                            <Image
                                source={{
                                    uri: profileImage.uri,
                                    cache: 'reload'
                                }}
                                style={styles.avatarImage}
                                resizeMode="cover"
                                key={`profile-${new Date().getTime()}`}
                            />
                        ) : (
                            <Text style={styles.avatarText}>
                                {profileData.username.charAt(0).toUpperCase() || 'U'}
                            </Text>
                        )}
                        <TouchableOpacity
                            style={styles.editAvatarButton}
                            onPress={handleProfileImageUpdate}
                            disabled={isUploadingImage || isLoading}
                        >
                            {isUploadingImage ? (
                                <ActivityIndicator size="small" color="#000" />
                            ) : (
                                <Ionicons name="pencil" size={18} color="#000" />
                            )}
                        </TouchableOpacity>
                    </View>

                    {/* User information */}
                    <Text style={styles.username}>{profileData.name}</Text>
                    <Text style={styles.userEmail}>{profileData.email}</Text>
                    <Text style={styles.userCategory}>Categoría: {profileData.category}</Text>
                    {profileData.verified && (
                        <View style={styles.verifiedBadge}>
                            <Ionicons name="checkmark-circle" size={16} color="#e0fe10" />
                            <Text style={styles.verifiedText}>Verificado</Text>
                        </View>
                    )}
                </View>

                {/* Stats bar */}
                <View style={styles.statsBarContainer}>
                    <View style={styles.statsBar}>
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>{stats.matches}</Text>
                            <Text style={styles.statLabel}>Partidas</Text>
                        </View>
                        <View style={styles.statDivider} />
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>{stats.victories}</Text>
                            <Text style={styles.statLabel}>Victorias</Text>
                        </View>
                        <View style={styles.statDivider} />
                        <View style={styles.statItem}>
                            <Text style={styles.statValue}>
                                {stats.matches > 0
                                    ? ((stats.victories / stats.matches) * 100).toFixed(1)
                                    : '0.0'}%
                            </Text>
                            <Text style={styles.statLabel}>Ratio</Text>
                        </View>
                    </View>
                </View>

                {/* Menu options */}
                <View style={styles.menuOrFormContainer}>
                    <View style={styles.menuContainer}>
                        {renderMenuOption('create-outline', 'Editar Perfil', 'editProfile')}
                        {renderMenuOption('star-outline', 'Mis Resultados', 'results')}
                        {renderMenuOption('settings-outline', 'Ajustes', 'settings')}
                        {renderMenuOption('help-circle-outline', 'Ayuda', 'help')}
                        {renderMenuOption('log-out-outline', 'Cerrar Sesión', 'logout')}
                    </View>
                </View>
            </ScrollView>
        );
    };
    
    // Main component render
    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                    disabled={isLoading}
                >
                    <Ionicons name="chevron-back" size={24} color="#e0fe10" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Mi Perfil</Text>
                {isLoading && userData && (
                    <ActivityIndicator 
                        size="small" 
                        color="#e0fe10" 
                        style={styles.headerLoader}
                    />
                )}
            </View>
            
            {renderContent()}
        </View>
    );
};

// Obtener dimensiones de la pantalla para estilos responsivos

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#232323',
    },
    container: {
        flex: 1,
        backgroundColor: '#232323',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 60, // Aumentado para evitar colisión con la barra de estado
        paddingBottom: 15,
        paddingHorizontal: 15,
        backgroundColor: '#598392',
    },
    backButton: {
        marginRight: 10,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#ffffff',
        marginLeft: 10,
    },
    scrollView: {
        flex: 1,
        backgroundColor: '#598392', // Mismo color que profileSection
    },
    scrollViewContent: {
        backgroundColor: '#232323', // Fondo para el contenido tras la sección del perfil
        paddingBottom: 20,
    },
    profileSection: {
        backgroundColor: '#598392',
        paddingBottom: 30,
        alignItems: 'center',
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
    },
    avatarContainer: {
        width: 120,
        height: 120,
        borderRadius: 60,
        backgroundColor: '#ffffff',
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 20,
        borderWidth: 3,
    },
    avatarImage: {
        width: '100%',
        height: '100%',
        borderRadius: 60,
    },
    avatarText: {
        fontSize: 48,
        fontWeight: 'bold',
        color: '#598392',
    },
    editAvatarButton: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#e0fe10',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 2,
        borderColor: '#232323',
    },
    username: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#ffffff',
        marginBottom: 5,
    },
    userEmail: {
        fontSize: 16,
        color: '#ffffff',
        opacity: 0.8,
        marginBottom: 5,
    },
    userCategory: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#e0fe10',
        marginTop: 5,
        marginBottom: 10,
    },
    verifiedBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.2)',
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderRadius: 15,
        marginBottom: 15,
    },
    verifiedText: {
        color: '#e0fe10',
        marginLeft: 5,
        fontSize: 12,
        fontWeight: 'bold',
    },
    statsBarContainer: {
        paddingHorizontal: 15,
        marginTop: -30,
    },
    statsBar: {
        flexDirection: 'row',
        backgroundColor: '#2e2e2e',
        paddingVertical: 15,
        justifyContent: 'space-around',
        alignItems: 'center',
        borderRadius: 12,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    statItem: {
        flex: 1,
        alignItems: 'center',
    },
    statDivider: {
        width: 1,
        height: '60%',
        backgroundColor: '#fff',
    },
    statValue: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#ffffff',
        marginBottom: 3,
    },
    statLabel: {
        fontSize: 14,
        color: '#ffffff',
        opacity: 0.8,
    },
    menuContainer: {
        width: '100%',
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#2e2e2e',
        borderRadius: 10,
        marginBottom: 10,
        padding: 15,
    },
    menuIconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#598392',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 15,
    },
    menuItemText: {
        flex: 1,
        fontSize: 16,
        fontWeight: '500',
        color: '#ffffff',
    },
    menuItemChevron: {
        marginLeft: 10,
    },
    loadingText: {
        fontSize: 16,
        color: '#e0fe10',
        marginTop: 10,
    },
    errorText: {
        fontSize: 16,
        color: '#e0fe10',
        marginBottom: 10,
    },
    retryButton: {
        backgroundColor: '#598392',
        padding: 10,
        borderRadius: 5,
    },
    retryButtonText: {
        fontSize: 16,
        color: '#ffffff',
    },
    // Contenedor para el menú o el formulario de edición
    menuOrFormContainer: {
        marginTop: 20,
        paddingHorizontal: 15,
        backgroundColor: '#232323',
    },

});

ProfileScreen.navigationOptions = {
    header: null
};

export default ProfileScreen;