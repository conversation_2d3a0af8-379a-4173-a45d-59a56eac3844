import React from 'react';
import {
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  IconButton,
  Chip,
  Tooltip
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { MovimientoWod } from './tipos';

interface MovimientoTablaProps {
  movimientosWod: MovimientoWod[];
  onEliminar: (index: number) => void;
  onPreview?: (movimiento: MovimientoWod) => void;
}

const MovimientoTabla: React.FC<MovimientoTablaProps> = ({
  movimientosWod,
  onEliminar,
  onPreview
}) => {
  return (
    <TableContainer component={Paper} sx={{ mt: 3 }}>
      <Table sx={{ minWidth: 650 }} aria-label="Movimientos seleccionados">
        <TableHead>
          <TableRow>
            <TableCell>Nombre</TableCell>
            <TableCell>Categorías</TableCell>
            <TableCell>Géneros</TableCell>
            <TableCell align="right">Acciones</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {movimientosWod.map((movimiento, index) => {
            // Obtener las categorías incluidas
            const categorias = Object.keys(movimiento.categorias || {});
            
            // Obtener los géneros incluidos para cada categoría
            const generos: string[] = [];
            
            categorias.forEach(cat => {
              const categoria = movimiento.categorias && movimiento.categorias[cat as keyof typeof movimiento.categorias];
              
              if (categoria && 'masculino' in categoria && categoria.masculino) {
                generos.push('Masculino');
              }
              
              if (categoria && 'femenino' in categoria && categoria.femenino) {
                generos.push('Femenino');
              }
            });
            
            // Eliminar duplicados
            const generosUnicos = Array.from(new Set(generos));
            
            return (
              <TableRow key={index}>
                <TableCell>{movimiento.nombre}</TableCell>
                <TableCell>
                  {categorias.map(cat => (
                    <Chip 
                      key={cat} 
                      label={cat.toUpperCase()}
                      color={
                        cat === 'rx' ? 'primary' : 
                        cat === 'int' ? 'secondary' : 
                        'default'
                      }
                      size="small" 
                      sx={{ mr: 0.5, mb: 0.5 }} 
                    />
                  ))}
                </TableCell>
                <TableCell>
                  {generosUnicos.map(gen => (
                    <Chip 
                      key={gen} 
                      label={gen} 
                      variant="outlined" 
                      size="small" 
                      sx={{ mr: 0.5, mb: 0.5 }} 
                    />
                  ))}
                </TableCell>
                <TableCell align="right">
                  {onPreview && (
                    <Tooltip title="Ver detalles">
                      <IconButton 
                        onClick={() => onPreview(movimiento)}
                        color="info"
                        sx={{ mr: 1 }}
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip title="Eliminar">
                    <IconButton 
                      onClick={() => onEliminar(index)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            );
          })}
          {movimientosWod.length === 0 && (
            <TableRow>
              <TableCell colSpan={4} align="center">
                No hay movimientos seleccionados
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default MovimientoTabla;
