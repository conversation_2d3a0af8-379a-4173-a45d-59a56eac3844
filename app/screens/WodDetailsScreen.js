import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Share,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Icon from '@expo/vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { STRAPI_URL } from '../config';
import CreateBattleModal from '../components/CreateBattleModal';

const WodDetailsScreen = ({ route, navigation }) => {
  const { wod: initialWod } = route.params;
  const [wod, setWod] = useState(initialWod);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const getStrapiToken = async (email, password) => {
    try {
      const response = await axios.post(`${STRAPI_URL}/api/auth/local`, {
        identifier: email,
        password: password
      });
      return response.data.jwt;
    } catch (error) {
      console.error('Error getting Strapi token:', error.response?.data || error);
      throw error;
    }
  };

  const createBattle = async () => {
    try {
      setIsCreating(true);

      // Obtener todos los datos almacenados
      const keys = ['userEmail', 'userPassword', 'userName'];
      const results = await AsyncStorage.multiGet(keys);
      const storedData = Object.fromEntries(results);

      if (!storedData.userEmail || !storedData.userPassword) {
        throw new Error('No se encontraron las credenciales necesarias');
      }

      const strapiJwt = await getStrapiToken(storedData.userEmail, storedData.userPassword);
      const battleData = {
        data: {
          titulo: `Batalla vs ${storedData.userName}`,
          wodTitle: wod.title,
          wod: wod.id
        }
      };

      const response = await axios.post(`${STRAPI_URL}/api/battles`, battleData, {
        headers: {
          'Authorization': `Bearer ${strapiJwt}`,
          'Content-Type': 'application/json'
        }
      });

      const battleId = response.data.data.id;
      const battleLink = `thewodleague://battle/${battleId}`;
      
      // Primero intentamos compartir
      try {
        const shareResult = await Share.share({
          message: `¡Te desafío a una batalla en The Wod League! ${wod.title}\n\nÚnete aquí: ${battleLink}`,
          url: battleLink
        });

        // Esperamos a que se complete el compartir
        if (shareResult.action === Share.sharedAction) {
          console.log('Batalla compartida exitosamente');
        } else if (shareResult.action === Share.dismissedAction) {
          console.log('Compartir cancelado por el usuario');
        }
      } catch (error) {
        console.log('Error al compartir:', error);
      }

      // Cerramos el modal
      setModalVisible(false);

      // Pequeña pausa para permitir que el modal se cierre
      await new Promise(resolve => setTimeout(resolve, 100));

      // Finalmente navegamos al Home
      navigation.reset({
        index: 0,
        routes: [{ 
          name: 'Home',
          params: {
            newBattleId: battleId,
            showAnimation: true
          }
        }],
      });

    } catch (error) {
      console.error('Error creating battle:', error.response?.data || error.message);
      Alert.alert(
        'Error',
        error.response?.data?.error?.message || 'No se pudo crear la batalla. Inténtalo de nuevo.'
      );
    } finally {
      setIsCreating(false);
    }
  };

  const shareBattle = async (battleLink, wodTitle, battleId) => {
    try {
      const result = await Share.share({
        message: `¡Te desafío a una batalla en The Wod League! ${wodTitle}\n\nÚnete aquí: ${battleLink}`,
      });
    } catch (error) {
      Alert.alert('Error', 'No se pudo compartir la batalla');
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#eb8d28" />
      </View>
    );
  }

  if (!wod) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No se pudo cargar el WOD</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container}>
        <LinearGradient
          colors={['#4a90e2', '#357abd']}
          style={styles.header}
        >
          <Text style={styles.title}>{wod.title}</Text>
          <View style={styles.levelContainer}>
            <Icon name="fitness-center" size={20} color="#fff" />
            <Text style={styles.level}>Nivel: {wod.level}</Text>
          </View>
        </LinearGradient>

        <View style={styles.content}>
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon name="timer" size={20} color="#4a90e2" />
              <Text style={styles.infoText}>Duración: {wod.duration} minutos</Text>
            </View>
            <View style={styles.infoRow}>
              <Icon name="category" size={20} color="#4a90e2" />
              <Text style={styles.infoText}>Tipo: {wod.type}</Text>
            </View>
          </View>

          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>Descripción</Text>
            <Text style={styles.description}>{wod.description}</Text>
          </View>

          <TouchableOpacity
            style={styles.startButton}
            onPress={() => setModalVisible(true)}
          >
            <Text style={styles.startButtonText}>Iniciar Batalla</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <CreateBattleModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={createBattle}
        isCreating={isCreating}
        wod={wod}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  level: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 8,
  },
  content: {
    padding: 20,
  },
  infoSection: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  descriptionSection: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  startButton: {
    backgroundColor: '#eb8d28',
    borderRadius: 25,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default WodDetailsScreen;
