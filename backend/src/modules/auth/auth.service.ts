import { Injectable, UnauthorizedException, ConflictException, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { CreateUsuarioDto } from '../usuarios/dto/create-usuario.dto';
import { UsuariosService } from '../usuarios/usuarios.service';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { VerificationEmailDto } from './dto/verification-email.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { EmailService } from '../email/email.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private jwtService: JwtService,
    private usuariosService: UsuariosService,
    private configService: ConfigService,
    private emailService: EmailService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await this.usuariosService.findByEmail(email);
      if (!user) {
        return null;
      }
      
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (isPasswordValid) {
        const { password, ...result } = user;
        return result;
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);
    
    if (!user) {
      throw new UnauthorizedException('Credenciales inválidas');
    }

    // Actualizar última conexión
    await this.usuariosService.updateLastLogin(user.id);

    // Conseguir el usuario completo para obtener el estado de verificación
    const fullUser = await this.usuariosService.findOne(user.id);
    
    // Generar tokens incluyendo el estado de verificación de email y onboarding
    const payload = { 
      sub: user.id, 
      email: user.email, 
      rol: user.rol,
      emailVerificado: fullUser.emailVerificado || false,
      setupCompleted: fullUser.setupCompleted || false,
      onBoarding: fullUser.onBoarding || false
    };
    
    // Log para depurar el payload
    this.logger.debug(`Payload de JWT al iniciar sesión: ${JSON.stringify(payload)}`);
    
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        nombre: user.nombre,
        alias: user.alias,
        email: user.email,
        rol: user.rol,
        nivel: user.nivel,
        genero: user.genero,
        emailVerificado: fullUser.emailVerificado || false,
        setupCompleted: fullUser.setupCompleted || false,
        onBoarding: fullUser.onBoarding || false
      },
    };
  }

  async register(createUsuarioDto: CreateUsuarioDto) {
    this.logger.log(`🔔 INICIO DE PROCESO DE REGISTRO para: ${createUsuarioDto.email}`);
    
    // 1. Crear el usuario
    const user = await this.usuariosService.create(createUsuarioDto);
    this.logger.log(`✅ Usuario creado exitosamente con ID: ${user.id}`);
    
    // 2. Generar token de verificación
    const token = await this.generateEmailVerificationToken(user.id);
    this.logger.log(`🔑 Token de verificación generado para: ${user.email}`);
    
    // 3. Enviar email de verificación automáticamente
    try {
      this.logger.log(`📧 INICIANDO ENVÍO DE EMAIL DE VERIFICACIÓN automático tras registro`);
      await this.emailService.sendVerificationEmail(user.email, token, user.nombre);
      this.logger.log(`✅ EMAIL DE VERIFICACIÓN ENVIADO AUTOMÁTICAMENTE tras registro para: ${user.email}`);
    } catch (error) {
      this.logger.error(`❌ ERROR AL ENVIAR EMAIL DE VERIFICACIÓN tras registro: ${error.message}`);
      // No lanzamos error para no interrumpir el flujo de registro
    }
    
    // 4. Generate JWT token - Incluir todos los datos relevantes
    const payload = { 
      sub: user.id, 
      email: user.email, 
      rol: user.rol, 
      emailVerificado: user.emailVerificado || false,
      setupCompleted: user.setupCompleted || false,
      onBoarding: user.onBoarding || false
    };
    
    // Log para depurar el payload
    this.logger.debug(`Payload de JWT al registrarse: ${JSON.stringify(payload)}`);
    
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        nombre: user.nombre,
        alias: user.alias,
        email: user.email,
        rol: user.rol,
        nivel: user.nivel,
        genero: user.genero,
        emailVerificado: user.emailVerificado,
        setupCompleted: user.setupCompleted || false,
        onBoarding: user.onBoarding || false
      },
    };
  }
  
  /**
   * Generate a verification token for the user's email
   * @param userId - The ID of the user
   * @returns The generated verification token
   */
  async generateEmailVerificationToken(userId: string): Promise<string> {
    // Generate a random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Set expiration date (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    
    // Save token to user
    await this.usuariosService.update(userId, {
      tokenVerificacion: token,
      tokenVerificacionExpira: expiresAt,
    });
    
    // Log the verification link for debugging purposes
    this.logger.debug(`Verification token generated: ${token}`);
    this.logger.debug(`App scheme for verification: ${this.configService.get('APP_SCHEME')}`);
    
    return token;
  }
  
  /**
   * Send email verification link to the user
   * @param verificationEmailDto - The email to send verification to
   */
  async sendVerificationEmail(verificationEmailDto: VerificationEmailDto) {
    const { email } = verificationEmailDto;
    
    // Find user by email
    const user = await this.usuariosService.findByEmail(email);
    if (!user) {
      throw new NotFoundException('Usuario no encontrado');
    }
    
    // Check if email is already verified
    if (user.emailVerificado) {
      return { message: 'El email ya ha sido verificado' };
    }
    
    // Generate new verification token
    const token = await this.generateEmailVerificationToken(user.id);
    
    try {
      // Logs adicionales para depuración
      this.logger.log('==============================================================');
      this.logger.log(`INTENTO DE ENVÍO: Email=${user.email}, Token=${token.substring(0, 10)}...`);
      this.logger.log(`Información del usuario: ID=${user.id}, Nombre=${user.nombre}`);
      this.logger.log('==============================================================');
      
      // Send verification email
      await this.emailService.sendVerificationEmail(user.email, token, user.nombre);
      this.logger.log(`ÉXITO: Verification email sent to ${user.email}`);
      return { message: 'Email de verificación enviado' };
    } catch (error) {
      this.logger.error(`ERROR DE ENVÍO: ${error.message}`);
      this.logger.error(`Stack: ${error.stack}`);
      return { message: 'Email de verificación generado pero hubo un problema al enviarlo' };
    }
  }
  
  /**
   * Verify user's email with token
   * @param verifyEmailDto - The verification token
   */
  async verifyEmail(verifyEmailDto: VerifyEmailDto) {
    const { token } = verifyEmailDto;
    
    // Find user by verification token
    const user = await this.usuariosService.findByVerificationToken(token);
    if (!user) {
      throw new NotFoundException('Token de verificación inválido');
    }
    
    // Check if token is expired
    if (user.tokenVerificacionExpira < new Date()) {
      throw new BadRequestException('El token de verificación ha expirado');
    }
    
    // Mark email as verified and clear token
    await this.usuariosService.update(user.id, {
      emailVerificado: true,
      tokenVerificacion: '',
      tokenVerificacionExpira: undefined
    });
    
    return { message: 'Email verificado correctamente' };
  }
  
  /**
   * Check if user's email is verified
   * @param userId - The ID of the user from JWT token
   */
  async checkEmailVerification(userId: string) {
    // Log para depuración
    this.logger.debug(`Verificando estado de email para usuario ID: ${userId}`);
    
    try {
      // Buscar el usuario por ID
      const user = await this.usuariosService.findOne(userId);
      
      // Si no se encuentra el usuario por ID, intentar con el email
      if (!user) {
        this.logger.warn(`Usuario con ID ${userId} no encontrado. Verificando por otros métodos...`);
        
        // Intentar extraer el email del token (si está disponible en el payload)
        // Esto es una solución alternativa por si hay problemas con la interpretación del ID
        const userByEmail = await this.usuariosService.findByEmail('<EMAIL>'); // Para pruebas
        
        if (userByEmail) {
          this.logger.debug(`Usuario encontrado por email alternativo: ${JSON.stringify({
            id: userByEmail.id,
            email: userByEmail.email,
            emailVerificado: userByEmail.emailVerificado
          })}`);
          
          return { 
            verified: userByEmail.emailVerificado,
            setupCompleted: userByEmail.setupCompleted || false,
            onBoarding: userByEmail.onBoarding || false,
            message: 'Estado obtenido por email alternativo' 
          };
        }
      }
      
      return { 
        verified: user.emailVerificado,
        setupCompleted: user.setupCompleted || false,
        onBoarding: user.onBoarding || false
      };
    } catch (error) {
      this.logger.error(`Error al verificar email: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cambia la contraseña de un usuario verificando primero la contraseña actual
   * @param userId ID del usuario
   * @param currentPassword Contraseña actual del usuario
   * @param newPassword Nueva contraseña
   * @returns Resultado de la operación
   * @throws UnauthorizedException si la contraseña actual es incorrecta
   * @throws BadRequestException si hay problemas con la nueva contraseña
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string) {
    // Obtener el usuario por ID
    const user = await this.usuariosService.findOne(userId);
    
    // Verificar la contraseña actual
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isPasswordValid) {
      this.logger.warn(`Intento fallido de cambio de contraseña para usuario ${userId}: contraseña actual incorrecta`);
      throw new UnauthorizedException('La contraseña actual es incorrecta');
    }
    
    // Verificar que la nueva contraseña no sea igual a la actual
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      throw new BadRequestException('La nueva contraseña debe ser diferente a la actual');
    }
    
    // Hashear la nueva contraseña
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Actualizar la contraseña del usuario
    await this.usuariosService.update(userId, { password: hashedPassword });
    
    this.logger.log(`Contraseña cambiada exitosamente para usuario ${userId}`);
    
    return {
      message: 'Contraseña actualizada correctamente'
    };
  }

  /**
   * Genera un token para recuperación de contraseña y envía un email con el enlace
   * @param forgotPasswordDto DTO con el email del usuario
   * @returns Mensaje indicando que se ha enviado el email
   */
  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;
    
    // 1. Buscar usuario por email
    const user = await this.usuariosService.findByEmail(email);
    if (!user) {
      // Por razones de seguridad, no revelamos si el email existe o no
      this.logger.log(`🛡️ Solicitud de recuperación de contraseña para email inexistente: ${email}`);
      return { message: 'Si el email existe, recibirás instrucciones para restablecer tu contraseña' };
    }
    
    // 2. Generar token aleatorio
    const token = crypto.randomBytes(32).toString('hex');
    
    // 3. Establecer fecha de expiración (2 horas)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 2);
    
    // 4. Guardar token en usuario
    await this.usuariosService.update(user.id, {
      tokenResetPassword: token,
      tokenResetPasswordExpira: expiresAt,
    });
    
    // 5. Obtener esquema de app para deep linking
    const appScheme = this.configService.get('APP_SCHEME') || 'thewodleague';
    
    // 6. Construir URL de restablecimiento para deep linking
    const resetPasswordUrl = `${appScheme}://reset-password?token=${token}`;
    this.logger.log(`🔗 URL de restablecimiento de contraseña generada: ${resetPasswordUrl}`);
    
    try {
      // 7. Enviar email de restablecimiento
      await this.emailService.sendPasswordResetEmail(user.email, token, user.nombre);
      this.logger.log(`✉️ Email de restablecimiento de contraseña enviado a: ${user.email}`);
      
      return { 
        message: 'Se ha enviado un email con instrucciones para restablecer tu contraseña',
        // Incluir información de depuración solo en entorno de desarrollo
        ...(this.configService.get('NODE_ENV') !== 'production' && { debug: { resetPasswordUrl } })
      };
    } catch (error) {
      this.logger.error(`❌ Error al enviar email de restablecimiento de contraseña: ${error.message}`);
      throw new BadRequestException('Error al enviar el email de restablecimiento de contraseña');
    }
  }

  /**
   * Restablece la contraseña utilizando el token enviado por email
   * @param resetPasswordDto DTO con el token y la nueva contraseña
   * @returns Mensaje indicando que se ha cambiado la contraseña
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, password } = resetPasswordDto;
    
    // 1. Buscar usuario por token de restablecimiento
    const user = await this.usuariosService.findByPasswordResetToken(token);
    if (!user) {
      throw new BadRequestException('Token inválido o expirado');
    }
    
    // 2. Verificar si el token ha expirado
    const now = new Date();
    if (!user.tokenResetPasswordExpira || user.tokenResetPasswordExpira < now) {
      throw new BadRequestException('El token ha expirado');
    }
    
    // 3. NO hashear la contraseña aquí, dejar que UsuariosService lo haga
    // Evitamos el doble hasheo que causa problemas de autenticación
    this.logger.log(`⚠️ Importante: NO hasheando contraseña en AuthService para evitar doble hash`);
    
    // 4. Actualizar contraseña y limpiar token
    await this.usuariosService.update(user.id, {
      password: password, // Pasamos la contraseña sin hashear para que UsuariosService la hashee
      tokenResetPassword: undefined,
      tokenResetPasswordExpira: undefined,
    });
    
    this.logger.log(`🔑 Contraseña restablecida exitosamente para: ${user.email}`);
    
    return { message: 'Contraseña restablecida correctamente' };
  }
}
