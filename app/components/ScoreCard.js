import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';

const ScoreCard = ({ score, onPress, status = 'pending' }) => {
  const formatDate = (dateString) => {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
  };

  const renderScoreValue = () => {
    if (!score.value) return null;

    switch (score.scoring_method) {
      case 'Time':
        return (
          <Text style={styles.scoreValue}>
            Time: {score.value.time || 'Not completed'}
          </Text>
        );
      case 'Rounds + Reps':
        return (
          <Text style={styles.scoreValue}>
            {score.value.rounds} rounds + {score.value.reps} reps
          </Text>
        );
      case 'Total Reps':
        return (
          <Text style={styles.scoreValue}>
            {score.value.reps} reps
          </Text>
        );
      case 'Weight':
        return (
          <Text style={styles.scoreValue}>
            {score.value.weight}kg
          </Text>
        );
      case 'Distance':
        return (
          <Text style={styles.scoreValue}>
            {score.value.distance}m
          </Text>
        );
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'approved':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      case 'pending':
      default:
        return '#FFC107';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'approved':
        return 'check-circle';
      case 'rejected':
        return 'cancel';
      case 'pending':
      default:
        return 'pending';
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <Icon 
            name={getStatusIcon()} 
            size={20} 
            color={getStatusColor()} 
          />
          <Text style={[styles.status, { color: getStatusColor() }]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Text>
        </View>
        <Text style={styles.date}>
          {formatDate(score.submission_date)}
        </Text>
      </View>

      <View style={styles.content}>
        {renderScoreValue()}
        
        {score.notes && (
          <Text style={styles.notes} numberOfLines={2}>
            {score.notes}
          </Text>
        )}

        {score.evidence_url && (
          <View style={styles.evidenceContainer}>
            <Icon name="attachment" size={16} color="#666" />
            <Text style={styles.evidenceText}>Evidence attached</Text>
          </View>
        )}
      </View>

      {status === 'rejected' && score.rejection_reason && (
        <View style={styles.rejectionContainer}>
          <Text style={styles.rejectionTitle}>Reason for rejection:</Text>
          <Text style={styles.rejectionText}>{score.rejection_reason}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  status: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  date: {
    fontSize: 12,
    color: '#666',
  },
  content: {
    gap: 8,
  },
  scoreValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  notes: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  evidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  evidenceText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  rejectionContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#FEE2E2',
    borderRadius: 8,
  },
  rejectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#DC2626',
    marginBottom: 4,
  },
  rejectionText: {
    fontSize: 14,
    color: '#991B1B',
  },
});

export default ScoreCard;
