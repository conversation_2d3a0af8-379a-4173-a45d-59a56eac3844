import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Usuario } from '../../usuarios/entities/usuario.entity';
import { Liga } from '../../ligas/entities/liga.entity';

@Entity('clasificaciones')
export class Clasificacion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Usuario)
  @JoinColumn({ name: 'usuario_id' })
  usuario: Usuario;

  @Column({ name: 'usuario_id' })
  usuarioId: string;

  @ManyToOne(() => Liga)
  @JoinColumn({ name: 'liga_id' })
  liga: Liga;

  @Column({ name: 'liga_id' })
  ligaId: string;

  @Column({ name: 'categoria', length: 50 })
  categoria: string;

  @Column({ name: 'puntuacion_total', type: 'decimal', precision: 10, scale: 2, default: 0 })
  puntuacionTotal: number;

  @Column({ name: 'bonus_consistencia', type: 'decimal', precision: 10, scale: 2, default: 0 })
  bonusConsistencia: number;

  @Column({ name: 'wods_completados', default: 0 })
  wodsCompletados: number;

  @Column({ name: 'wods_totales', default: 0 })
  wodsTotales: number;

  @Column({ name: 'posicion', default: 0 })
  posicion: number;

  @Column({ name: 'posicion_anterior', default: 0 })
  posicionAnterior: number;

  @Column({ name: 'semana', default: 1 })
  semana: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'updated_at' })
  updatedAt: Date;
}
