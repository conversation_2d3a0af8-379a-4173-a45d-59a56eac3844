import { Controller, Get, Post, Body, Patch, Param, Delete, UseInterceptors, UploadedFile, UseGuards, Request } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard, Roles } from '../auth/guards/roles.guard';
import { OwnershipGuard } from '../auth/guards/ownership.guard';
import { UsuariosService } from './usuarios.service';
import { CreateUsuarioDto } from './dto/create-usuario.dto';
import { UpdateUsuarioDto } from './dto/update-usuario.dto';
import { UpdateRolDto } from './dto/update-rol.dto';
import { Usuario } from './entities/usuario.entity';
import { TransformResponseInterceptor } from './interceptors/transform-response.interceptor';

/**
 * Controlador para la administración de usuarios
 * Proporciona endpoints para la gestión CRUD de usuarios
 */
@Controller('usuarios')
@UseInterceptors(TransformResponseInterceptor)
export class UsuariosController {
  constructor(private readonly usuariosService: UsuariosService) {}

  /**
   * Crea un nuevo usuario
   * @param createUsuarioDto Datos para crear el usuario
   * @returns Usuario creado
   */
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('Admin')
  async create(@Body() createUsuarioDto: CreateUsuarioDto): Promise<Usuario> {
    return this.usuariosService.create(createUsuarioDto);
  }

  /**
   * Obtiene todos los usuarios
   * @returns Lista de usuarios
   */
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('Admin')
  async findAll(): Promise<Usuario[]> {
    return this.usuariosService.findAll();
  }

  /**
   * Obtiene un usuario por su ID
   * @param id ID del usuario
   * @returns Usuario encontrado
   */
  @Get(':id')
  @UseGuards(JwtAuthGuard, OwnershipGuard)
  async findOne(@Param('id') id: string): Promise<Usuario> {
    return this.usuariosService.findOne(id);
  }

  /**
   * Actualiza un usuario por su ID
   * @param id ID del usuario
   * @param updateUsuarioDto Datos para actualizar el usuario
   * @returns Usuario actualizado
   */
  @Patch(':id')
  @UseGuards(JwtAuthGuard, OwnershipGuard)
  async update(
    @Param('id') id: string,
    @Body() updateUsuarioDto: UpdateUsuarioDto
  ): Promise<Usuario> {
    return this.usuariosService.update(id, updateUsuarioDto);
  }

  /**
   * Elimina un usuario por su ID
   * @param id ID del usuario
   * @returns Void
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('Admin')
  async remove(@Param('id') id: string): Promise<void> {
    return this.usuariosService.remove(id);
  }

  /**
   * Actualiza solo el rol de un usuario (endpoint protegido)
   * @param id ID del usuario
   * @param updateRolDto DTO con el nuevo rol
   * @returns Usuario con rol actualizado
   */
  @Patch(':id/rol')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('Admin') // Solo administradores pueden cambiar roles
  async updateRol(
    @Param('id') id: string,
    @Body() updateRolDto: UpdateRolDto
  ): Promise<Usuario> {
    return this.usuariosService.updateRol(id, updateRolDto.rol);
  }
}
