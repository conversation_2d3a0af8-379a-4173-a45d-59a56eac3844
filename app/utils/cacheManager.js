/**
 * Cache Manager
 * Sistema de caché para almacenar datos localmente y reducir llamadas a la API
 */
import AsyncStorage from '@react-native-async-storage/async-storage';

// Prefijo para todas las claves de caché
const CACHE_PREFIX = 'cache_';

// TTL predeterminados por tipo de recurso (en milisegundos)
const DEFAULT_TTL = {
  // Datos que apenas cambian
  STATIC: 24 * 60 * 60 * 1000,  // 24 horas
  // Datos que cambian con poca frecuencia
  LOW: 60 * 60 * 1000,          // 1 hora
  // Datos que cambian con moderada frecuencia
  MEDIUM: 10 * 60 * 1000,       // 10 minutos
  // Datos que cambian frecuentemente
  HIGH: 2 * 60 * 1000,          // 2 minutos
  // Datos en tiempo real (casi sin caché)
  REALTIME: 30 * 1000           // 30 segundos
};

// TTL específicos por tipo de recursos
const RESOURCE_TTL = {
  'user_profile': DEFAULT_TTL.MEDIUM,
  'leagues_list': DEFAULT_TTL.LOW,
  'active_wods': DEFAULT_TTL.MEDIUM,
  'leaderboard_general': DEFAULT_TTL.HIGH,
  'leaderboard_wod': DEFAULT_TTL.HIGH,
  'pvp_challenges': DEFAULT_TTL.REALTIME,
  'user_badges': DEFAULT_TTL.LOW,
  'box_info': DEFAULT_TTL.STATIC,
};

const cacheManager = {
  /**
   * Guarda datos en caché con tiempo de expiración
   * @param {string} key - Clave única para identificar los datos
   * @param {any} data - Datos a almacenar en caché
   * @param {number} ttl - Tiempo de vida en milisegundos (opcional)
   * @returns {Promise<void>}
   */
  async set(key, data, ttl) {
    try {
      // Determinar TTL a usar
      const timeToLive = ttl || RESOURCE_TTL[key] || DEFAULT_TTL.MEDIUM;
      
      // Crear objeto de caché con datos y timestamp de expiración
      const cacheItem = {
        data,
        expiry: Date.now() + timeToLive,
      };
      
      // Guardar en AsyncStorage
      await AsyncStorage.setItem(`${CACHE_PREFIX}${key}`, JSON.stringify(cacheItem));

    } catch (error) {
      console.error('Error guardando en caché:', error);
    }
  },
  
  /**
   * Obtiene datos de la caché verificando su validez
   * @param {string} key - Clave para recuperar datos
   * @returns {Promise<any|null>} - Datos almacenados o null si no existen o han expirado
   */
  async get(key) {
    try {
      // Obtener datos de AsyncStorage
      const cachedJson = await AsyncStorage.getItem(`${CACHE_PREFIX}${key}`);
      
      // Si no hay datos, retornar null
      if (!cachedJson) {
        return null;
      }
      
      // Parsear datos de caché
      const cacheItem = JSON.parse(cachedJson);
      
      // Verificar si ha expirado
      if (Date.now() > cacheItem.expiry) {
        // Si expiró, eliminar y retornar null
        if (__DEV__) {
          console.log(`🕑 Caché expirada para "${key}"`);
        }
        await AsyncStorage.removeItem(`${CACHE_PREFIX}${key}`);
        return null;
      }
      
      // Calcular tiempo restante de validez
      if (__DEV__) {
        const remainingTime = Math.round((cacheItem.expiry - Date.now()) / 1000);
        console.log(`✅ Usando caché para "${key}", válida por ${remainingTime}s más`);
      }
      
      // Retornar datos válidos
      return cacheItem.data;
    } catch (error) {
      console.error('Error recuperando de caché:', error);
      return null;
    }
  },
  
  /**
   * Invalida (elimina) una entrada específica de la caché
   * @param {string} key - Clave a invalidar
   * @returns {Promise<void>}
   */
  async invalidate(key) {
    try {
      await AsyncStorage.removeItem(`${CACHE_PREFIX}${key}`);
      if (__DEV__) {
        console.log(`🗑️ Caché invalidada para "${key}"`);
      }
    } catch (error) {
      console.error('Error invalidando caché:', error);
    }
  },
  
  /**
   * Elimina todas las entradas de caché o las de un grupo específico
   * @param {string} [groupPrefix] - Prefijo de grupo opcional
   * @returns {Promise<void>}
   */
  async clear(groupPrefix = '') {
    try {
      // Obtener todas las claves de AsyncStorage
      const allKeys = await AsyncStorage.getAllKeys();
      
      // Filtrar solo las claves de caché
      const cacheKeys = allKeys.filter(k => 
        k.startsWith(CACHE_PREFIX) && 
        (groupPrefix ? k.startsWith(`${CACHE_PREFIX}${groupPrefix}`) : true)
      );
      
      if (cacheKeys.length > 0) {
        // Eliminar todas las claves filtradas
        await AsyncStorage.multiRemove(cacheKeys);
        if (__DEV__) {
          console.log(`🧹 Se eliminaron ${cacheKeys.length} entradas de caché${groupPrefix ? ` para el grupo "${groupPrefix}"` : ''}`);
        }
      }
    } catch (error) {
      console.error('Error limpiando caché:', error);
    }
  },
  
  /**
   * Actualiza parte de la información en caché sin cambiar el tiempo de expiración
   * @param {string} key - Clave de los datos a actualizar
   * @param {Object} updatedData - Nuevos datos (parciales)
   * @returns {Promise<boolean>} - true si se actualizó, false si no existe o hubo error
   */
  async update(key, updatedData) {
    try {
      // Obtener entrada actual
      const cachedJson = await AsyncStorage.getItem(`${CACHE_PREFIX}${key}`);
      if (!cachedJson) {
        return false;
      }
      
      // Parsear y actualizar
      const cacheItem = JSON.parse(cachedJson);
      
      // Si los datos son un objeto, hacer merge, sino reemplazar
      if (typeof cacheItem.data === 'object' && cacheItem.data !== null && !Array.isArray(cacheItem.data)) {
        cacheItem.data = { ...cacheItem.data, ...updatedData };
      } else {
        cacheItem.data = updatedData;
      }
      
      // Guardar actualización sin cambiar tiempo de expiración
      await AsyncStorage.setItem(`${CACHE_PREFIX}${key}`, JSON.stringify(cacheItem));
      
      if (__DEV__) {
        console.log(`🔄 Caché actualizada para "${key}"`);
      }
      
      return true;
    } catch (error) {
      console.error('Error actualizando caché:', error);
      return false;
    }
  },
  
  /**
   * Refresca el tiempo de expiración de una entrada de caché
   * @param {string} key - Clave a refrescar
   * @param {number} [ttl] - Nuevo tiempo de vida (opcional)
   * @returns {Promise<boolean>} - true si se refrescó, false si no existe o hubo error
   */
  async refresh(key, ttl) {
    try {
      // Obtener entrada actual
      const cachedJson = await AsyncStorage.getItem(`${CACHE_PREFIX}${key}`);
      if (!cachedJson) {
        return false;
      }
      
      // Parsear y actualizar tiempo de expiración
      const cacheItem = JSON.parse(cachedJson);
      const timeToLive = ttl || RESOURCE_TTL[key] || DEFAULT_TTL.MEDIUM;
      
      cacheItem.expiry = Date.now() + timeToLive;
      
      // Guardar con nueva expiración
      await AsyncStorage.setItem(`${CACHE_PREFIX}${key}`, JSON.stringify(cacheItem));
      
      if (__DEV__) {
        console.log(`🔄 Tiempo de expiración actualizado para "${key}", expira en ${timeToLive/1000}s`);
      }
      
      return true;
    } catch (error) {
      console.error('Error refrescando caché:', error);
      return false;
    }
  }
};

export default cacheManager;
