import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import * as crypto from 'crypto';

// Servicios
import { AuthService } from '../../modules/auth/auth.service';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';

// Entidades
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';

// DTOs
import { VerificationEmailDto } from '../../modules/auth/dto/verification-email.dto';
import { VerifyEmailDto } from '../../modules/auth/dto/verify-email.dto';

// Helpers
import { DatabaseTestHelper, UsuariosTest<PERSON>el<PERSON>, AuthTest<PERSON>elper, usuariosFixtures } from '../helper';
import jwtConfig from '../../config/jwt.config';

/**
 * Suite de pruebas para verificación de email
 * Sigue el patrón AAA (Arrange-Act-Assert) y usa base de datos real
 * Cumple con las buenas prácticas de testing de The WOD League
 */
describe('Verificación de Email', () => {
  let usuariosService: UsuariosService;
  let authService: AuthService;
  let jwtService: JwtService;
  let testUserId: string;
  const logger = new Logger('VerificacionEmailTest');
  
  // Generador de datos de prueba para evitar conflictos de email
  const generateTestUser = (suffix: string) => ({
    ...usuariosFixtures.defaultUser,
    email: `verify.${suffix}.${Date.now()}@example.com`,
    nombre: `Verificar Email Test ${suffix}`,
    alias: `verifyemail${suffix}`
  });
  
  beforeAll(async () => {
    // Arrange: Verificar conexión a la base de datos
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      logger.error('No se pudo conectar a la base de datos para pruebas');
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Crear módulos de testing usando helpers existentes
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);
    
    // Configurar el módulo de autenticación
    const authModule = await AuthTestHelper.createAuthTestingModule(usuariosService);
    authService = authModule.get<AuthService>(AuthService);
    jwtService = authModule.get<JwtService>(JwtService);
  });
  
  afterEach(async () => {
    // Siguiendo el principio de "Tests Aislados" de las buenas prácticas
    // Limpiamos los datos después de cada test
    if (testUserId) {
      try {
        await usuariosService['usuariosRepository'].delete({ id: testUserId });
        logger.log(`Usuario de prueba ${testUserId} eliminado con éxito`);
        testUserId = ''; // Limpiar el ID para el siguiente test
      } catch (error) {
        logger.warn(`Error al limpiar datos de prueba: ${error.message}`);
      }
    }
  });
  
  // TESTS PARA enviar email de verificación
  describe('Envío de email de verificación', () => {
    it('debe generar un token de verificación para un usuario existente', async () => {
      // Arrange: Crear un usuario para este test y luego enviar el email
      const testUser = generateTestUser('send1');
      const createdUser = await usuariosService.create({
        nombre: testUser.nombre,
        alias: testUser.alias,
        email: testUser.email,
        password: testUser.password,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO
      });
      testUserId = createdUser.id; // Guardar para limpieza posterior
      
      const verificationEmailDto: VerificationEmailDto = {
        email: testUser.email
      };
      
      // Act: Llamar al servicio de autenticación
      const result = await authService.sendVerificationEmail(verificationEmailDto);
      
      // Assert: Verificar que se generó un token y se actualizó el usuario
      expect(result).toHaveProperty('message');
      expect(result.message).toContain('Email de verificación enviado');
      
      // Verificar que el usuario fue actualizado con el token
      const updatedUser = await usuariosService.findByEmail(testUser.email);
      expect(updatedUser).not.toBeNull();
      if (updatedUser) {
        expect(updatedUser.tokenVerificacion).toBeDefined();
        expect(updatedUser.tokenVerificacion.length).toBeGreaterThan(0);
        expect(updatedUser.tokenVerificacionExpira).toBeDefined();
        
        // La fecha de expiración debe ser futura
        const now = new Date();
        expect(updatedUser.tokenVerificacionExpira.getTime()).toBeGreaterThan(now.getTime());
      }
    });
    
    it('debe rechazar la solicitud si el email no existe', async () => {
      // Arrange: Preparar DTO con email inexistente
      const verificationEmailDto: VerificationEmailDto = {
        email: '<EMAIL>'
      };
      
      // Act & Assert: Verificar que lanza una excepción
      await expect(authService.sendVerificationEmail(verificationEmailDto))
        .rejects
        .toThrow(NotFoundException);
    });
  });
  
  // TESTS PARA verificar email con token
  describe('Verificación de email con token', () => {
    it('debe verificar un email con un token válido', async () => {
      // Arrange: Crear un usuario para este test
      const testUser = generateTestUser('verify1');
      const createdUser = await usuariosService.create({
        nombre: testUser.nombre,
        alias: testUser.alias,
        email: testUser.email,
        password: testUser.password,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO
      });
      testUserId = createdUser.id; // Guardar para limpieza posterior
      
      // Generar y guardar un token de verificación
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Token válido por 24 horas
      
      await usuariosService.update(testUserId, {
        tokenVerificacion: token,
        tokenVerificacionExpira: expiresAt,
        emailVerificado: false // Asegurarse de que inicia como no verificado
      });
      
      const verifyEmailDto: VerifyEmailDto = {
        token: token
      };
      
      // Act: Verificar el email con el token
      const result = await authService.verifyEmail(verifyEmailDto);
      
      // Assert: Verificar que el email ha sido marcado como verificado
      expect(result).toHaveProperty('message');
      expect(result.message).toContain('Email verificado correctamente');
      
      // Verificar que el usuario ha sido actualizado en la base de datos
      const updatedUser = await usuariosService.findByEmail(testUser.email);
      expect(updatedUser).not.toBeNull();
      if (updatedUser) {
        expect(updatedUser.emailVerificado).toBe(true);
        expect(updatedUser.tokenVerificacion).toBe(''); // Token se limpia después de verificar
        // La fecha de expiración puede ser null o una fecha, dependiendo de la implementación
        // No se verifica este valor para evitar problemas de compatibilidad
      }
    });
    
    it('debe rechazar un token inválido', async () => {
      // Arrange: Preparar DTO con token inválido
      const verifyEmailDto: VerifyEmailDto = {
        token: 'token-invalido-123456789'
      };
      
      // Act & Assert: Verificar que lanza una excepción
      await expect(authService.verifyEmail(verifyEmailDto))
        .rejects
        .toThrow(NotFoundException);
    });
    
    it('debe rechazar un token expirado', async () => {
      // Arrange: Crear un usuario para este test
      const testUser = generateTestUser('expired1');
      const createdUser = await usuariosService.create({
        nombre: testUser.nombre,
        alias: testUser.alias,
        email: testUser.email,
        password: testUser.password,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO
      });
      testUserId = createdUser.id; // Guardar para limpieza posterior
      
      // Generar y guardar un token expirado
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() - 1); // Token ya expirado (1 hora en el pasado)
      
      await usuariosService.update(testUserId, {
        tokenVerificacion: token,
        tokenVerificacionExpira: expiresAt,
        emailVerificado: false
      });
      
      const verifyEmailDto: VerifyEmailDto = {
        token: token
      };
      
      // Act & Assert: Verificar que lanza una excepción por token expirado
      await expect(authService.verifyEmail(verifyEmailDto))
        .rejects
        .toThrow();
    });
  });
  
  // TESTS PARA consultar estado de verificación
  describe('Consulta de estado de verificación', () => {
    it('debe devolver el estado correcto de verificación', async () => {
      // Arrange: Crear un usuario verificado para este test
      const testUser = generateTestUser('status1');
      const createdUser = await usuariosService.create({
        nombre: testUser.nombre,
        alias: testUser.alias,
        email: testUser.email,
        password: testUser.password,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO
      });
      testUserId = createdUser.id; // Guardar para limpieza posterior
      
      // Actualizar usuario para tener un estado conocido
      await usuariosService.update(testUserId, {
        emailVerificado: true
      });
      
      // Act: Llamar al servicio para consultar estado
      const result = await authService.checkEmailVerification(testUserId);
      
      // Assert: Comprobar resultado
      expect(result).toHaveProperty('verified');
      expect(result.verified).toBe(true);
    });
    
    it('debe devolver falso si el email no está verificado', async () => {
      // Arrange: Crear un usuario no verificado para este test
      const testUser = generateTestUser('status2');
      const createdUser = await usuariosService.create({
        nombre: testUser.nombre,
        alias: testUser.alias,
        email: testUser.email,
        password: testUser.password,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.MASCULINO
      });
      testUserId = createdUser.id; // Guardar para limpieza posterior
      
      // Asegurar que no esté verificado
      await usuariosService.update(testUserId, {
        emailVerificado: false
      });
      
      // Act: Llamar al servicio para consultar estado
      const result = await authService.checkEmailVerification(testUserId);
      
      // Assert: Comprobar resultado
      expect(result).toHaveProperty('verified');
      expect(result.verified).toBe(false);
    });
    
    it('debe rechazar la consulta para un id de usuario inválido', async () => {
      // Act & Assert: Verificar que lanza excepción con ID inválido
      await expect(authService.checkEmailVerification('id-invalido-123'))
        .rejects
        .toThrow();
    });
  });
});
