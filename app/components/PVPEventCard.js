import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { format } from 'date-fns';

const PVPEventCard = ({ event, onPress, type = 'default' }) => {
  const formatDate = (dateString) => {
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
  };

  const getTimeLeft = (expirationDate) => {
    const now = new Date();
    const expiration = new Date(expirationDate);
    const diffTime = Math.abs(expiration - now);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const renderScoreInfo = () => {
    if (!event.score) return null;

    switch (event.scoring_method) {
      case 'Time':
        return (
          <Text style={styles.scoreText}>
            Time: {event.score.time || 'Not completed'}
          </Text>
        );
      case 'Rounds + Reps':
        return (
          <Text style={styles.scoreText}>
            {event.score.rounds} rounds + {event.score.reps} reps
          </Text>
        );
      case 'Total Reps':
        return (
          <Text style={styles.scoreText}>
            {event.score.reps} reps
          </Text>
        );
      case 'Weight':
        return (
          <Text style={styles.scoreText}>
            {event.score.weight}kg
          </Text>
        );
      case 'Distance':
        return (
          <Text style={styles.scoreText}>
            {event.score.distance}m
          </Text>
        );
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.card, type === 'my' ? styles.myCard : styles.defaultCard]}
      onPress={onPress}
    >
      <View style={styles.header}>
        <Text style={styles.title}>{event.title}</Text>
        <View style={styles.timeContainer}>
          <Icon name="access-time" size={16} color="#666" />
          <Text style={styles.timeText}>
            {getTimeLeft(event.expiration_date)} days left
          </Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.locationContainer}>
          <Icon name="location-on" size={16} color="#666" />
          <Text style={styles.locationText}>{event.location}</Text>
        </View>

        <View style={styles.wodContainer}>
          <Text style={styles.wodTitle}>{event.wod_name}</Text>
          <Text style={styles.wodDescription} numberOfLines={2}>
            {event.wod_description}
          </Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detail}>
            <Icon name="timer" size={16} color="#666" />
            <Text style={styles.detailText}>{event.time_cap} min cap</Text>
          </View>
          <View style={styles.detail}>
            <Icon name="leaderboard" size={16} color="#666" />
            <Text style={styles.detailText}>{event.scoring_method}</Text>
          </View>
        </View>

        {type === 'my' && renderScoreInfo()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  defaultCard: {
    backgroundColor: '#fff',
  },
  myCard: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#eb8d28',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
  content: {
    gap: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  wodContainer: {
    gap: 4,
  },
  wodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  wodDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  detail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  scoreText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#eb8d28',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default PVPEventCard;
