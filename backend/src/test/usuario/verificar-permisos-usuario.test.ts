import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario, RolUsuario, EstadoUsuario } from '../../modules/usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { JwtModule, JwtService } from '@nestjs/jwt';
import jwtConfig from '../../config/jwt.config';
import { UnauthorizedException } from '@nestjs/common';
import { DatabaseTestHelper, UsuariosTestHelper, AuthTestHelper, usuariosFixtures } from '../helper';

/**
 * Test de verificación de permisos de usuario
 * 
 * Este test utiliza una base de datos real (configurada para pruebas)
 * para verificar las operaciones relacionadas con los permisos de usuarios.
 */
describe('Verificar Permisos Usuario Tests', () => {
  let usuariosService: UsuariosService;
  let jwtService: JwtService;
  let testUsers: {
    admin: Usuario | null,
    boxOwner: Usuario | null,
    regularUser: Usuario | null,
    inactiveUser: Usuario | null
  } = {
    admin: null,
    boxOwner: null,
    regularUser: null,
    inactiveUser: null
  };

  beforeAll(async () => {
    // Verificar conexión a la base de datos antes de iniciar las pruebas
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      throw new Error('Error de conexión a la base de datos');
    }
    
    // Crear primero el módulo base con servicios de usuarios usando el helper
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);
    
    // Configurar el JWT para pruebas de verificación de permisos
    const authModule = await Test.createTestingModule({
      imports: [
        JwtModule.registerAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => ({
            secret: configService.get('jwt.secret'),
            signOptions: { expiresIn: configService.get('jwt.expiresIn') },
          }),
        }),
        ConfigModule.forRoot({
          isGlobal: true,
          load: [jwtConfig],
          envFilePath: '.env.development',
        }),
      ],
    }).compile();
    
    jwtService = authModule.get<JwtService>(JwtService);

    // Limpiar usuarios de prueba anteriores
    await limpiarUsuariosTest(usuariosService);

    // Crear usuarios de prueba con diferentes roles
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    // Datos de usuarios
    const usersData = [
      {
        nombre: 'Admin Test',
        alias: 'admintest',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
        rol: RolUsuario.ADMIN,
        estado: EstadoUsuario.ACTIVO
      },
      {
        nombre: 'Box Owner Test',
        alias: 'boxownertest',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.RX,
        genero: GeneroUsuario.MASCULINO,
        rol: RolUsuario.BOX_OWNER,
        estado: EstadoUsuario.ACTIVO
      },
      {
        nombre: 'Regular User Test',
        alias: 'regulartest',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.INTERMEDIO,
        genero: GeneroUsuario.FEMENINO,
        rol: RolUsuario.USUARIO,
        estado: EstadoUsuario.ACTIVO
      },
      {
        nombre: 'Inactive User Test',
        alias: 'inactivetest',
        email: '<EMAIL>',
        password: hashedPassword,
        nivel: NivelUsuario.SCALED,
        genero: GeneroUsuario.MASCULINO,
        rol: RolUsuario.USUARIO,
        estado: EstadoUsuario.INACTIVO
      }
    ];

    // Crear usuarios en la base de datos
    for (const userData of usersData) {
      const createdUser = await usuariosService['usuariosRepository'].save(
        usuariosService['usuariosRepository'].create(userData)
      );
      
      // Guardar referencias para los tests
      if (userData.rol === RolUsuario.ADMIN) {
        testUsers.admin = createdUser;
      } else if (userData.rol === RolUsuario.BOX_OWNER) {
        testUsers.boxOwner = createdUser;
      } else if (userData.rol === RolUsuario.USUARIO && userData.estado === EstadoUsuario.ACTIVO) {
        testUsers.regularUser = createdUser;
      } else if (userData.estado === EstadoUsuario.INACTIVO) {
        testUsers.inactiveUser = createdUser;
      }
    }
  });

  afterAll(async () => {
    // Limpiar: eliminar los usuarios de prueba
    await limpiarUsuariosTest(usuariosService);
  });

  // Función auxiliar para limpiar usuarios de prueba
  async function limpiarUsuariosTest(service) {
    const testEmails = [
      '<EMAIL>', 
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    for (const email of testEmails) {
      try {
        const user = await service['usuariosRepository'].findOne({
          where: { email }
        });
        if (user) {
          await service['usuariosRepository'].delete(user.id);
        }
      } catch (error) {
        // Ignoramos errores, el usuario posiblemente no existe
      }
    }
  }

  // Función para crear tokens JWT para los diferentes usuarios
  function createToken(user) {
    const payload = { sub: user.id, email: user.email, rol: user.rol };
    return jwtService.sign(payload);
  }

  // Test para verificar que un usuario admin tiene acceso a funciones administrativas
  it('debe permitir acceso administrativo a usuario con rol ADMIN', async () => {
    // Crear token para usuario administrador
    const adminToken = createToken(testUsers.admin);
    
    // Decodificar el token para obtener el payload
    const decoded = jwtService.verify(adminToken);
    
    // Verificar que el payload contiene la información correcta
    expect(decoded).toBeDefined();
    expect(testUsers.admin).not.toBeNull();
    if (testUsers.admin) {
      expect(decoded.sub).toBe(testUsers.admin.id);
      expect(decoded.rol).toBe(RolUsuario.ADMIN);
    }
    
    // Verificar que el usuario existe en la base de datos
    const adminUser = await usuariosService.findOne(decoded.sub);
    expect(adminUser).toBeDefined();
    expect(adminUser.rol).toBe(RolUsuario.ADMIN);
  });

  // Test para verificar que un usuario de box tiene los permisos adecuados
  it('debe permitir acceso de propietario de box a usuario con rol BOX_OWNER', async () => {
    // Crear token para usuario box owner
    const boxOwnerToken = createToken(testUsers.boxOwner);
    
    // Decodificar el token para obtener el payload
    const decoded = jwtService.verify(boxOwnerToken);
    
    // Verificar que el payload contiene la información correcta
    expect(decoded).toBeDefined();
    expect(testUsers.boxOwner).not.toBeNull();
    if (testUsers.boxOwner) {
      expect(decoded.sub).toBe(testUsers.boxOwner.id);
      expect(decoded.rol).toBe(RolUsuario.BOX_OWNER);
    }
    
    // Verificar que el usuario existe en la base de datos
    const boxOwnerUser = await usuariosService.findOne(decoded.sub);
    expect(boxOwnerUser).toBeDefined();
    expect(boxOwnerUser.rol).toBe(RolUsuario.BOX_OWNER);
  });

  // Test para verificar que un usuario regular tiene acceso limitado
  it('debe asignar permisos limitados a usuario con rol USUARIO', async () => {
    // Crear token para usuario regular
    const regularUserToken = createToken(testUsers.regularUser);
    
    // Decodificar el token para obtener el payload
    const decoded = jwtService.verify(regularUserToken);
    
    // Verificar que el payload contiene la información correcta
    expect(decoded).toBeDefined();
    expect(testUsers.regularUser).not.toBeNull();
    if (testUsers.regularUser) {
      expect(decoded.sub).toBe(testUsers.regularUser.id);
      expect(decoded.rol).toBe(RolUsuario.USUARIO);
    }
    
    // Verificar que el usuario existe en la base de datos
    const regularUser = await usuariosService.findOne(decoded.sub);
    expect(regularUser).toBeDefined();
    expect(regularUser.rol).toBe(RolUsuario.USUARIO);
  });

  // Test para verificar que un usuario inactivo no tiene acceso
  it('debe rechazar el acceso a usuario con estado INACTIVO', async () => {
    // Simular verificación de estado de usuario
    function verificarEstadoActivo(usuario) {
      if (usuario.estado !== EstadoUsuario.ACTIVO) {
        throw new UnauthorizedException('Usuario inactivo o suspendido');
      }
      return true;
    }
    
    // Verificar usuario inactivo
    expect(() => verificarEstadoActivo(testUsers.inactiveUser)).toThrow(UnauthorizedException);
    
    // Verificar que usuario activo pasa la verificación
    expect(verificarEstadoActivo(testUsers.regularUser)).toBe(true);
  });
});
