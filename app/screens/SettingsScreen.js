import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// No necesitamos useAuth por ahora

const SettingsScreen = ({ navigation }) => {

  // Opciones de ajustes
  const settingsOptions = [
    {
      id: 'notifications',
      icon: 'notifications',
      title: 'Ajustes de notificacion',
      screen: 'NotificationSettings'
    },
    {
      id: 'password',
      icon: 'key',
      title: 'Cambiar de contraseña',
      screen: 'PasswordSettings'
    },
    {
      id: 'deleteAccount',
      icon: 'person',
      title: 'Eliminar cuenta',
      screen: 'DeleteAccount'
    }
  ];

  // Renderizar una opción de ajustes
  const renderSettingOption = (option) => (
    <TouchableOpacity
      key={option.id}
      style={styles.settingItem}
      onPress={() => navigation.navigate(option.screen)}
      activeOpacity={0.7}
    >
      <View style={styles.settingIconContainer}>
        <Ionicons name={option.icon} size={24} color="#232323" />
      </View>
      <Text style={styles.settingText}>{option.title}</Text>
      <Ionicons name="chevron-forward" size={24} color="#e0fe10" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Ajustes</Text>
        </TouchableOpacity>
      </View>

      {/* Settings Options */}
      <View style={styles.settingsContainer}>
        {settingsOptions.map(option => renderSettingOption(option))}
      </View>

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    paddingVertical: 15,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0fe10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  settingText: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
  },
});

export default SettingsScreen;
