/**
 * Central API Client with interceptors
 * Handles all communication with the NestJS backend
 * Provides automatic token management, error handling, and retries
 */
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NESTJS_URL } from '../config';

/**
 * Create axios instance with default configuration
 */
const apiClient = axios.create({
  baseURL: NESTJS_URL,
  timeout: 15000, // 15 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

/**
 * Request interceptor
 * Automatically adds authorization token to all requests
 */
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // Get token from storage
      const token = await AsyncStorage.getItem('auth-token');
      
      // If token exists, add it to request headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    } catch (error) {
      console.error('Error in request interceptor:', error);
      return config;
    }
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

/**
 * Response interceptor
 * Handles common error scenarios and token refresh
 */
apiClient.interceptors.response.use(
  // Success handler
  (response) => response,
  
  // Error handler
  async (error) => {
    const originalRequest = error.config;
    
    // Prevent infinite loops
    if (originalRequest._retry) {
      return Promise.reject(error);
    }
    
    // Handle 401 Unauthorized or 403 Forbidden (likely token expired)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // Mark request as retried to prevent loops
      originalRequest._retry = true;
      
      try {
        // Get refresh token
        const refreshToken = await AsyncStorage.getItem('refresh-token');
        
        if (!refreshToken) {
          // No refresh token, reject
          console.warn('No refresh token available for 401/403 error');
          return Promise.reject(error);
        }
        
        // Try to refresh the token
        console.log('Attempting to refresh token...');
        
        // Make direct axios call to avoid interceptors loop
        const refreshResponse = await axios.post(
          `${NESTJS_URL}/api/auth/refresh`,
          { refreshToken },
          { headers: { 'Content-Type': 'application/json' } }
        );
        
        if (refreshResponse.data && refreshResponse.data.access_token) {
          // Store new tokens
          const newAccessToken = refreshResponse.data.access_token;
          await AsyncStorage.setItem('auth-token', newAccessToken);
          
          if (refreshResponse.data.refresh_token) {
            await AsyncStorage.setItem('refresh-token', refreshResponse.data.refresh_token);
          }
          
          // Update authorization header and retry original request
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          return axios(originalRequest);
        } else {
          console.warn('Token refresh failed - invalid response');
          // Clear tokens and force re-login
          await AsyncStorage.removeItem('auth-token');
          await AsyncStorage.removeItem('refresh-token');
          return Promise.reject(error);
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        // Clear tokens and force re-login
        await AsyncStorage.removeItem('auth-token');
        await AsyncStorage.removeItem('refresh-token');
        return Promise.reject(error);
      }
    }
    
    // Network errors handling
    if (error.message && (
      error.message.includes('Network Error') || 
      error.message.includes('timeout')
    )) {
      console.warn('Network error or timeout:', error.message);
      // Can implement retry logic here for network errors
    }
    
    return Promise.reject(error);
  }
);

// Export HTTP methods with improved error handling
export default {
  /**
   * GET request
   * @param {string} url - Endpoint URL (without base URL)
   * @param {Object} config - Axios config object
   * @returns {Promise<Object>} - Response data
   */
  async get(url, config = {}) {
    try {
      const response = await apiClient.get(url, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  },
  
  /**
   * POST request
   * @param {string} url - Endpoint URL (without base URL)
   * @param {Object} data - Request payload
   * @param {Object} config - Axios config object
   * @returns {Promise<Object>} - Response data
   */
  async post(url, data = {}, config = {}) {
    try {
      const response = await apiClient.post(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  },
  
  /**
   * PUT request
   * @param {string} url - Endpoint URL (without base URL)
   * @param {Object} data - Request payload
   * @param {Object} config - Axios config object
   * @returns {Promise<Object>} - Response data
   */
  async put(url, data = {}, config = {}) {
    try {
      const response = await apiClient.put(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  },
  
  /**
   * PATCH request
   * @param {string} url - Endpoint URL (without base URL)
   * @param {Object} data - Request payload
   * @param {Object} config - Axios config object
   * @returns {Promise<Object>} - Response data
   */
  async patch(url, data = {}, config = {}) {
    try {
      const response = await apiClient.patch(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  },
  
  /**
   * DELETE request
   * @param {string} url - Endpoint URL (without base URL)
   * @param {Object} config - Axios config object
   * @returns {Promise<Object>} - Response data
   */
  async delete(url, config = {}) {
    try {
      const response = await apiClient.delete(url, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  },
  
  /**
   * Centralized error handler
   * @param {Error} error - Axios error object
   * @private
   */
  handleError(error) {
    // Extract useful information from error
    const status = error.response?.status;
    const data = error.response?.data;
    const url = error.config?.url;
    
    // Log error details for debugging
    console.error(`API Error (${status}) on ${url}:`, data || error.message);
    
    // Here we can add app-specific error handling like showing toasts
  }
};
