{"expo": {"name": "TheWodLeague", "slug": "TheWodLeague", "version": "1.0.0", "scheme": "thewodleague", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.nscbe.thewodleague", "buildNumber": "1", "associatedDomains": ["applinks:thewodleague.com"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nscbe.thewodleague"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router"], "extra": {"eas": {"projectId": "thewodleague"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/thewodleague"}}}