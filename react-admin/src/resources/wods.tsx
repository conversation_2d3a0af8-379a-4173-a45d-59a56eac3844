import {
  List,
  Datagrid,
  TextField,
  <PERSON><PERSON><PERSON>,
  EditButton,
  Edit,
  SimpleForm,
  TextInput,
  Create,
  ReferenceField,
  SelectInput,
  NumberInput,
  ReferenceInput,
  AutocompleteInput,
  required,
  DateInput,
  BooleanInput,
  FormTab,
  TabbedForm
} from 'react-admin';
import { Box } from '@mui/material';
import { useState } from 'react';
import { MovimientosInput } from '../components/MovimientosInput';
import { SixWeekDateInput } from '../components/SixWeekDateInput';

// Lista de WODs
export const WodList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="titulo" />
      <TextField source="tipo" />
      <DateField source="fechaPublicacion" />
      <DateField source="fechaLimite" />
      <TextField source="semana" />
      <ReferenceField source="ligaId" reference="leagues">
        <TextField source="nombre" />
      </ReferenceField>
    </Datagrid>
  </List>
);

// Componente de edición de WODs
export const WodEdit = () => {
  return (
    <Edit transform={(data) => ({
      ...data,
      // Si movimientos ya es un objeto, dejarlo así, si es string, parsearlo
      movimientos: typeof data.movimientos === 'string'
        ? JSON.parse(data.movimientos)
        : data.movimientos
    })}>
      <TabbedForm>
        <FormTab label="Información Básica">
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextInput disabled source="id" />
            <TextInput source="titulo" validate={[required()]} fullWidth />
            <TextInput source="descripcion" multiline rows={3} fullWidth validate={[required()]} />

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 200px' }}>
                <SelectInput source="tipo" choices={[
                  { id: 'AMRAP', name: 'AMRAP' },
                  { id: 'FORTIME', name: 'For Time' },
                  { id: 'EMOM', name: 'EMOM' },
                  { id: 'MAXWEIGHT', name: 'Peso Máximo' },
                  { id: 'MAXREPS', name: 'Repeticiones Máximas' },
                ]} validate={[required()]} fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <DateInput source="fechaPublicacion" validate={[required()]} fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <SixWeekDateInput
                  source="fechaLimite"
                  validate={[required()]}
                  helperText="Fecha límite de entrega del WOD"
                  startDateField="fechaPublicacion"
                  fullWidth
                />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 150px' }}>
                <NumberInput source="semana" disabled validate={[required()]} min={1} helperText="Calculado automáticamente" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 150px' }}>
                <SelectInput source="estado" choices={[
                  { id: 'Borrador', name: 'Borrador' },
                  { id: 'Publicado', name: 'Publicado' },
                  { id: 'Cerrado', name: 'Cerrado' },
                ]} defaultValue="Borrador" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <ReferenceInput source="ligaId" reference="leagues" label="Liga">
                  <AutocompleteInput optionText="nombre" validate={[required()]} fullWidth />
                </ReferenceInput>
              </Box>
              <Box sx={{ flex: '1 1 150px' }}>
                <BooleanInput source="esBonus" label="¿Es WOD bonus?" defaultValue={false} />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 300px' }}>
                <TextInput source="imagen" label="URL de la imagen" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 300px' }}>
                <TextInput source="resultadoLimite" label="Resultado límite" fullWidth />
              </Box>
            </Box>
          </Box>
        </FormTab>

        <FormTab label="Movimientos">
          <MovimientosInput
            source="movimientos"
            label="Movimientos del WOD"
          />
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

// Componente de creación de WODs
export const WodCreate = () => {
  return (
    <Create transform={(data) => ({
      ...data,
      // Si movimientos ya es un objeto, dejarlo así, si es string, parsearlo
      movimientos: typeof data.movimientos === 'string'
        ? JSON.parse(data.movimientos)
        : data.movimientos
    })}>
      <TabbedForm>
        <FormTab label="Información Básica">
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextInput source="titulo" validate={[required()]} fullWidth />
            <TextInput source="descripcion" multiline rows={3} fullWidth validate={[required()]} />

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 200px' }}>
                <SelectInput source="tipo" choices={[
                  { id: 'AMRAP', name: 'AMRAP' },
                  { id: 'FORTIME', name: 'For Time' },
                  { id: 'EMOM', name: 'EMOM' },
                  { id: 'MAXWEIGHT', name: 'Peso Máximo' },
                  { id: 'MAXREPS', name: 'Repeticiones Máximas' },
                ]} validate={[required()]} fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <DateInput source="fechaPublicacion" validate={[required()]} fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <SixWeekDateInput
                  source="fechaLimite"
                  validate={[required()]}
                  helperText="Fecha límite de entrega del WOD"
                  startDateField="fechaPublicacion"
                  fullWidth
                />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 150px' }}>
                <NumberInput source="semana" disabled validate={[required()]} min={1} defaultValue={1} helperText="Calculado automáticamente" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 150px' }}>
                <SelectInput source="estado" choices={[
                  { id: 'Borrador', name: 'Borrador' },
                  { id: 'Publicado', name: 'Publicado' },
                  { id: 'Cerrado', name: 'Cerrado' },
                ]} defaultValue="Borrador" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 200px' }}>
                <ReferenceInput source="ligaId" reference="leagues" label="Liga">
                  <AutocompleteInput optionText="nombre" validate={[required()]} fullWidth />
                </ReferenceInput>
              </Box>
              <Box sx={{ flex: '1 1 150px' }}>
                <BooleanInput source="esBonus" label="¿Es WOD bonus?" defaultValue={false} />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: '1 1 300px' }}>
                <TextInput source="imagen" label="URL de la imagen" fullWidth />
              </Box>
              <Box sx={{ flex: '1 1 300px' }}>
                <TextInput source="resultadoLimite" label="Resultado límite" fullWidth />
              </Box>
            </Box>
          </Box>
        </FormTab>

        <FormTab label="Movimientos">
          <MovimientosInput
            source="movimientos"
            label="Movimientos del WOD"
          />
        </FormTab>
      </TabbedForm>
    </Create>
  );
};
