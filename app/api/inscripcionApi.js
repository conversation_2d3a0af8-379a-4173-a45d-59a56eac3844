import { STRAPI_URL } from '../config';
import { handleResponse } from './utils';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Crear una nueva inscripción para un evento
 * @param {string} eventoId - ID del evento
 * @param {string} codigoGrupo - Código del grupo
 * @param {string} nivel - Nivel seleccionado (debe ser uno de los disponibles en el evento)
 * @returns {Promise<Object>} Inscripción creada
 */
const crearInscripcion = async (eventoId, codigoGrupo, nivel) => {
    try {
        // Obtener token de autenticación directamente de AsyncStorage
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
            throw new Error('Debes estar autenticado para inscribirte a un evento');
        }

        // Primero verificamos que el nivel esté disponible en el evento
        const eventoResponse = await fetch(`${STRAPI_URL}/api/eventos/${eventoId}?populate=*`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const eventoData = await handleResponse(eventoResponse);
        const nivelesDisponibles = eventoData.data.attributes.nivelesDisponibles;

        if (!nivelesDisponibles.includes(nivel)) {
            throw new Error(`Nivel no válido. Los niveles disponibles son: ${nivelesDisponibles.join(', ')}`);
        }

        const response = await fetch(`${STRAPI_URL}/api/inscripciones?populate=*`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                data: {
                    evento: eventoId,
                    codigoGrupo,
                    nivel
                },
            }),
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error al crear inscripción:', error);
        throw error;
    }
};

/**
 * Cancelar una inscripción
 * @param {string} inscripcionId - ID de la inscripción
 * @returns {Promise<Object>} Inscripción actualizada
 */
const cancelarInscripcion = async (inscripcionId) => {
    try {
        // Obtener token de autenticación directamente de AsyncStorage
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
            throw new Error('Debes estar autenticado para cancelar una inscripción');
        }

        const response = await fetch(`${STRAPI_URL}/api/inscripciones/${inscripcionId}?populate=*`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                data: {
                    estado: 'cancelada'
                }
            }),
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error al cancelar inscripción:', error);
        throw error;
    }
};

/**
 * Obtener todas las inscripciones del usuario actual
 * @returns {Promise<Array>} Array de inscripciones
 */
const obtenerMisInscripciones = async () => {
    try {
        // Obtener token de autenticación directamente de AsyncStorage
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
            throw new Error('Debes estar autenticado para ver tus inscripciones');
        }

        const response = await fetch(`${STRAPI_URL}/api/inscripciones/me?populate=*`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error al obtener inscripciones del usuario:', error);
        throw error;
    }
};

/**
 * Obtener todas las inscripciones de un evento
 * @param {string} eventoId - ID del evento
 * @returns {Promise<Array>} Array de inscripciones
 */
const obtenerInscripcionesEvento = async (eventoId) => {
    try {
        // Obtener token de autenticación directamente de AsyncStorage
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
            throw new Error('Debes estar autenticado para ver las inscripciones');
        }

        const response = await fetch(
            `${STRAPI_URL}/api/inscripciones?filters[evento][id][$eq]=${eventoId}&populate=*`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error al obtener inscripciones del evento:', error);
        throw error;
    }
};

/**
 * Confirmar una inscripción
 * @param {string} inscripcionId - ID de la inscripción
 * @returns {Promise<Object>} Inscripción actualizada
 */
const confirmarInscripcion = async (inscripcionId) => {
    try {
        // Obtener token de autenticación directamente de AsyncStorage
        const token = await AsyncStorage.getItem('auth-token');
        if (!token) {
            throw new Error('Debes estar autenticado para confirmar una inscripción');
        }

        const response = await fetch(`${STRAPI_URL}/api/inscripciones/${inscripcionId}?populate=*`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                data: {
                    estado: 'confirmada'
                }
            }),
        });
        const data = await handleResponse(response);
        return data.data;
    } catch (error) {
        console.error('Error al confirmar inscripción:', error);
        throw error;
    }
};

export default {
    crearInscripcion,
    cancelarInscripcion,
    obtenerMisInscripciones,
    obtenerInscripcionesEvento,
    confirmarInscripcion,
};
