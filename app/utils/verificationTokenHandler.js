import { Alert, Linking } from 'react-native';
import authApiNest from '../api/authApiNest';

/**
 * Handler para procesar tokens de verificación recibidos por URL
 * @param {object} navigation - Objeto de navegación para redireccionar al usuario
 * @returns {Function} Función para eliminar el listener cuando se desmonte el componente
 */
export const setupVerificationTokenHandler = (navigation) => {
  console.log('Configurando listener para enlaces de verificación...');
  
  // Función para manejar URLs entrantes
  const handleUrl = async ({ url }) => {
    if (!url) return;
    
    try {
      console.log('URL recibida:', url);
      
      // Verificar si la URL contiene un token de verificación o restablecimiento
      // Formato esperado puede ser cualquiera de estos:
      // - thewodleague://verify-email?token=abc123 (formato del correo electrónico)
      // - thewodleague://verify?token=abc123
      // - thewodleague://reset-password?token=abc123 (formato para recuperación de contraseña)
      // - exp://*************:8081/--/verify-email?token=abc123
      // - exp://*************:8081/--/verify?token=abc123
      // - exp://*************:8081/--/reset-password?token=abc123
      // - app://verify?token=abc123 (formato antiguo)
      console.log('🔍 Analizando URL para buscar token:', url);
      
      // Analizar la URL para extraer la ruta y parámetros
      console.log('🧩 Descomponiendo URL para análisis detallado');
      
      // 1. Identificar el tipo de token por la URL
      const isVerifyEmail = url.includes('verify-email') || url.includes('verify');
      const isResetPassword = url.includes('reset-password');
      
      console.log(`📧 ¿Es URL de verificación de email? ${isVerifyEmail ? 'SÍ' : 'NO'}`);
      console.log(`🔑 ¿Es URL de restablecimiento de contraseña? ${isResetPassword ? 'SÍ' : 'NO'}`);
      
      // 2. Extraer el token independientemente de la ruta
      const match = url.match(/[?&]token=([^&]+)/);
      
      if (match && match[1]) {
        const token = match[1];
        console.log('Token detectado:', token);
        
        if (isVerifyEmail) {
          // Procesar token de verificación de email
          await authApiNest.verifyEmail(token);
          console.log('Email verificado exitosamente, navegando a la pantalla principal');
          
          // Mostrar mensaje y redirigir a pantalla principal
          Alert.alert(
            '¡Email Verificado!',
            'Tu email ha sido verificado correctamente. ¡Bienvenido a The WOD League! 💪',
            [
              { 
                text: 'Continuar', 
                onPress: () => {
                  // Navegar al flujo principal de la app con reset para evitar volver atrás
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'Main' }]
                  });
                }
              }
            ],
            { cancelable: false } // Forzar al usuario a pulsar el botón
          );
          
          // Programar navegación automática después de 3 segundos incluso si no pulsa el botón
          setTimeout(() => {
            navigation.reset({
              index: 0,
              routes: [{ name: 'Main' }]
            });
          }, 3000);
        } 
        else if (isResetPassword) {
          // Para tokens de restablecimiento de contraseña, navegar a la pantalla de restablecimiento
          console.log('Token de restablecimiento de contraseña detectado, navegando a pantalla de restauración');
          
          // Navegar a la pantalla de restablecimiento de contraseña con el token
          navigation.navigate('ResetPassword', { token });
          return; // Salir temprano para evitar el código de verificación de email
        }
        else {
          // Token desconocido o formato no compatible
          console.warn('Formato de URL no reconocido:', url);
          Alert.alert('Error', 'El enlace que intentas usar no es válido.');  
        }
        
        // La forma correcta es navegar a la pantalla principal
        // La verificación ya está guardada en la BD, por lo que al ir a Main,
        // se detectará automáticamente en la próxima comprobación
        
        // Este bloque ahora está en los condicionales arriba
      }
    } catch (error) {
      console.error('Error al procesar token:', error);
      
      // Determinar el tipo de error para mostrar un mensaje apropiado
      let errorMessage = 'No se pudo procesar el token. Por favor, intenta de nuevo.';
      
      if (error.response) {
        const status = error.response.status;
        
        if (status === 400) {
          errorMessage = 'El enlace de verificación no es válido o ha expirado.';
        } else if (status === 404) {
          errorMessage = 'No encontramos un usuario asociado con este enlace de verificación.';
        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        }
      }
      
      Alert.alert('Error de Verificación', errorMessage);
    }
  };
  
  // Añadir el listener para URL entrantes
  // Configurar el listener
  const subscription = Linking.addEventListener('url', handleUrl);
  
  // Devolver una función para eliminar el listener
  return () => {
    console.log('Eliminando listener de verificación...');
    subscription.remove();
  };
};

/**
 * Función para verificar si la app se abrió con un enlace de verificación
 */
export const checkInitialURL = async (navigation) => {
  try {
    // Obtener URL inicial si la app se abrió con un enlace
    const initialURL = await Linking.getInitialURL();
    
    if (initialURL) {
      console.log('App abierta desde URL:', initialURL);
      
      // Identificar el tipo de token por la URL
      const isVerifyEmail = initialURL.includes('verify-email') || initialURL.includes('verify');
      const isResetPassword = initialURL.includes('reset-password');
      
      console.log(`📧 ¿Es URL inicial de verificación de email? ${isVerifyEmail ? 'SÍ' : 'NO'}`);
      console.log(`🔑 ¿Es URL inicial de restablecimiento de contraseña? ${isResetPassword ? 'SÍ' : 'NO'}`);
      
      // Verificar si contiene token
      const match = initialURL.match(/[?&]token=([^&]+)/);
      
      if (match && match[1]) {
        const token = match[1];
        console.log('Token inicial detectado:', token);
        
        try {
          if (isVerifyEmail) {
            // Intentar verificar el token de email
            await authApiNest.verifyEmail(token);
            
            // Verificación exitosa
            Alert.alert(
              '¡Email Verificado!',
              'Tu email ha sido verificado correctamente.',
              [
                { 
                  text: 'OK', 
                  onPress: () => {
                    // Ir a la pantalla principal
                    navigation.reset({
                      index: 0,
                      routes: [{ name: 'Main' }],
                    });
                  }
                }
              ]
            );
          } 
          else if (isResetPassword) {
            // Para tokens de restablecimiento de contraseña
            console.log('Token inicial de restablecimiento de contraseña detectado');    
            // Navegar a la pantalla de restablecimiento de contraseña con el token
            navigation.navigate('ResetPassword', { token });
          }
        } catch (verifyError) {
          console.error('Error verificando token inicial:', verifyError);
          
          // Determinar mensaje de error
          let errorMessage = 'No se pudo verificar tu email. Por favor, intenta de nuevo.';
          
          if (verifyError.response) {
            const status = verifyError.response.status;
            
            if (status === 400) {
              errorMessage = 'El enlace de verificación no es válido o ha expirado.';
            } else if (status === 404) {
              errorMessage = 'No encontramos un usuario asociado con este enlace de verificación.';
            } else if (verifyError.response.data?.message) {
              errorMessage = verifyError.response.data.message;
            }
          }
          
          Alert.alert('Error de Verificación', errorMessage);
        }
      }
    }
  } catch (error) {
    console.error('Error al verificar URL inicial:', error);
  }
};

