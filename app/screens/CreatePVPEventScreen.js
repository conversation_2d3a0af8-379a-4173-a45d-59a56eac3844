import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import Toast from 'react-native-toast-message';
import { useAuth } from '../context/AuthContextNest';
import pvpEventApi from '../api/pvpEventApi';

const SCORING_METHODS = [
  'Time',
  'Rounds + Reps',
  'Total Reps',
  'Weight',
  'Distance',
];

const CreatePVPEventScreen = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    wod_name: '',
    wod_description: '',
    time_cap: '',
    scoring_method: SCORING_METHODS[0],
    expiration_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
  });

  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleInputChange('expiration_date', selectedDate);
    }
  };

  const validateForm = () => {
    const requiredFields = [
      'title',
      'location',
      'wod_name',
      'wod_description',
      'time_cap',
    ];
    
    for (const field of requiredFields) {
      if (!formData[field]) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: `Please fill in the ${field.replace('_', ' ')}`,
        });
        return false;
      }
    }

    const timeCap = parseInt(formData.time_cap);
    if (isNaN(timeCap) || timeCap <= 0) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Time cap must be a positive number',
      });
      return false;
    }

    if (formData.expiration_date <= new Date()) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Expiration date must be in the future',
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const eventData = {
        ...formData,
        creator_id: user.uid,
        creation_date: new Date(),
      };

      await pvpEventApi.createPVPEvent(eventData);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Event created successfully!',
      });
      navigation.goBack();
    } catch (error) {
      console.error('Error creating event:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to create event',
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#eb8d28" />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <Text style={styles.sectionTitle}>Event Details</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Title *</Text>
          <TextInput
            style={styles.input}
            value={formData.title}
            onChangeText={(text) => handleInputChange('title', text)}
            placeholder="Enter event title"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.description}
            onChangeText={(text) => handleInputChange('description', text)}
            placeholder="Enter event description"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Location *</Text>
          <TextInput
            style={styles.input}
            value={formData.location}
            onChangeText={(text) => handleInputChange('location', text)}
            placeholder="Enter event location"
          />
        </View>

        <Text style={styles.sectionTitle}>Workout Details</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>WOD Name *</Text>
          <TextInput
            style={styles.input}
            value={formData.wod_name}
            onChangeText={(text) => handleInputChange('wod_name', text)}
            placeholder="Enter WOD name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>WOD Description *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={formData.wod_description}
            onChangeText={(text) => handleInputChange('wod_description', text)}
            placeholder="Enter WOD description"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Time Cap (minutes) *</Text>
          <TextInput
            style={styles.input}
            value={formData.time_cap}
            onChangeText={(text) => handleInputChange('time_cap', text)}
            placeholder="Enter time cap in minutes"
            keyboardType="numeric"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Scoring Method *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.scoring_method}
              onValueChange={(value) => handleInputChange('scoring_method', value)}
              style={styles.picker}
            >
              {SCORING_METHODS.map((method) => (
                <Picker.Item key={method} label={method} value={method} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Expiration Date *</Text>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateButtonText}>
              {formData.expiration_date.toLocaleDateString()}
            </Text>
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={formData.expiration_date}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
        </View>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
        >
          <Text style={styles.submitButtonText}>Create Event</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  form: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
  },
  dateButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
  },
  dateButtonText: {
    fontSize: 16,
    color: '#333',
  },
  submitButton: {
    backgroundColor: '#eb8d28',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreatePVPEventScreen;
