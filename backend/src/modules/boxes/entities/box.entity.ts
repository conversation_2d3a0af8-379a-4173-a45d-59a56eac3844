import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Usuario } from '../../usuarios/entities/usuario.entity';

export enum EstadoBox {
  ACTIVO = 'Activo',
  INACTIVO = 'Inactivo',
}

@Entity('boxes')
export class Box {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 100 })
  nombre: string;

  @Column()
  ubicacion: string;

  @ManyToOne(() => Usuario)
  @JoinColumn({ name: 'owner_id' })
  owner: Usuario;

  @Column({ name: 'owner_id' })
  ownerId: string;

  @CreateDateColumn({ name: 'fecha_registro' })
  fechaRegistro: Date;

  @Column({ nullable: true, type: 'text' })
  descripcion: string;

  @Column({ nullable: true })
  logo: string;

  @Column({
    type: 'enum',
    enum: EstadoBox,
    default: EstadoBox.ACTIVO,
  })
  estado: EstadoBox;

  @OneToMany(() => Usuario, usuario => usuario.box)
  miembros: Usuario[];

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
