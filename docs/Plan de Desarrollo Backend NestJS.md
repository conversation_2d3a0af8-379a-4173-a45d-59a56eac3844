# Plan de Desarrollo del Backend para The WOD League

## Resumen Ejecutivo

Este documento detalla el plan de desarrollo para el backend de **The WOD League**, una aplicación para competiciones remotas de CrossFit. El backend se implementará utilizando **NestJS** con **TypeScript**, **PostgreSQL** como base de datos principal, y **Redis** para clasificaciones en tiempo real y caché. La arquitectura seguirá las especificaciones del informe de infraestructura, utilizando WebSockets para comunicaciones en tiempo real, AWS S3 para almacenamiento de archivos, y un enfoque modular para facilitar el mantenimiento y la escalabilidad.

> **NOTA IMPORTANTE**: Este documento ha sido reorganizado para reflejar las prioridades actuales de desarrollo. La implementación se dividirá en dos fases:
> - **Fase 1 (Actual)**: Enfoque en los módulos de Autenticación, Usuarios, Boxes, Ligas, WODs, Resultados, Clasificaciones, Inscripciones y PVP.
> - **Fase 2 (Futuras Releases)**: Implementación de Battle Box, Eventos Especiales, Badges y otras características avanzadas.

## Arquitectura del Backend

Según el informe de infraestructura, utilizaremos:
- **NestJS con Express** como framework backend
- **PostgreSQL (AWS RDS)** como base de datos relacional
- **Redis (AWS ElastiCache)** para caché y clasificaciones en tiempo real
- **WebSockets con Socket.io** para comunicaciones en tiempo real
- **AWS S3** para almacenamiento de archivos
- **Passport.js con JWT y OAuth** para autenticación
- **OneSignal** para notificaciones push
- **Docker + AWS Elastic Beanstalk** para el despliegue del MVP

## Estructura del Proyecto

```
backend/
├── src/
│   ├── main.ts                      # Punto de entrada de la aplicación
│   ├── app.module.ts                # Módulo principal
│   ├── config/                      # Configuraciones de la aplicación
│   │   ├── app.config.ts            # Configuración general
│   │   ├── database.config.ts       # Configuración de PostgreSQL
│   │   ├── redis.config.ts          # Configuración de Redis
│   │   ├── s3.config.ts             # Configuración de AWS S3
│   │   └── jwt.config.ts            # Configuración de JWT
│   ├── common/                      # Código compartido
│   │   ├── decorators/              # Decoradores personalizados
│   │   ├── filters/                 # Filtros de excepción
│   │   ├── guards/                  # Guards de autenticación
│   │   ├── interceptors/            # Interceptores
│   │   ├── pipes/                   # Pipes de validación
│   │   └── utils/                   # Utilidades
│   ├── modules/                     # Módulos de la aplicación
│   │   ├── auth/                    # Módulo de autenticación
│   │   ├── usuarios/                # Módulo de usuarios
│   │   ├── boxes/                   # Módulo de boxes
│   │   ├── ligas/                   # Módulo de ligas
│   │   ├── wods/                    # Módulo de WODs
│   │   ├── resultados/              # Módulo de resultados
│   │   ├── clasificaciones/         # Módulo de clasificaciones
│   │   ├── inscripciones/           # Módulo de inscripciones
│   │   ├── pvp/                     # Módulo de PVP
│   │   ├── battle-box/              # Módulo de Battle Box (Fase 2)
│   │   ├── eventos-especiales/      # Módulo de eventos especiales (Fase 2)
│   │   ├── badges/                  # Módulo de badges (Fase 2)
│   │   └── notificaciones/          # Módulo de notificaciones
│   ├── websockets/                  # Configuración de WebSockets
│   │   ├── clasificaciones.gateway.ts # Gateway para clasificaciones en tiempo real
│   │   ├── notificaciones.gateway.ts  # Gateway para notificaciones
│   │   └── pvp.gateway.ts           # Gateway para desafíos PVP
│   └── jobs/                        # Tareas programadas
│       ├── clasificacion.job.ts     # Actualización de clasificaciones
│       ├── notificaciones.job.ts    # Envío de notificaciones
│       └── outliers.job.ts          # Detección de outliers
├── test/                            # Tests
│   ├── e2e/                         # Tests end-to-end
│   └── unit/                        # Tests unitarios
├── nest-cli.json                    # Configuración de NestJS
├── package.json                     # Dependencias
├── tsconfig.json                    # Configuración de TypeScript
├── Dockerfile                       # Configuración de Docker
└── docker-compose.yml               # Configuración de Docker Compose
```

## Detalle de Implementación por Módulos

### 1. Configuración Inicial y Base de Datos

1. **Inicialización del proyecto NestJS**
   - Crear proyecto con `nest new backend`
   - Configurar estructura de carpetas
   - Configurar ESLint y Prettier

2. **Configuración de PostgreSQL (AWS RDS)**
   - Implementar conexión a PostgreSQL usando TypeORM
   - Configurar entidades y relaciones
   - Implementar migraciones para gestión de esquema
   - Configurar repositorios para acceso a datos

3. **Configuración de Redis (AWS ElastiCache)**
   - Implementar conexión a Redis
   - Configurar caché para clasificaciones en tiempo real
   - Configurar almacenamiento de rankings

4. **Configuración de AWS S3**
   - Implementar servicio para subida y descarga de archivos
   - Configurar políticas de acceso
   - Implementar generación de URLs firmadas

5. **Configuración de Docker**
   - Crear Dockerfile para desarrollo y producción
   - Configurar docker-compose.yml para servicios locales
   - Preparar configuración para AWS Elastic Beanstalk

### 2. Módulo de Autenticación y Usuarios

1. **Autenticación con Passport.js**
   - ✅ Implementar registro de usuarios
   - ✅ Implementar inicio de sesión con JWT
   - Implementar OAuth para redes sociales
   - ✅ Implementar recuperación de contraseña
   - ✅ Implementar verificación de email
   - Implementar guards para rutas protegidas

2. **Gestión de Usuarios**
   - ✅ Implementar CRUD de usuarios
   - ✅ Implementar gestión de perfiles
   - ✅ Implementar cambio de contraseña
   - ✅ Implementar afiliación a box

3. **Roles y Permisos**
   - ✅ Implementar sistema de roles (Usuario, BoxOwner, Admin)
   - ✅ Implementar guards para verificación de roles
   - ✅ Implementar decoradores para control de acceso

## IMPLEMENTACIÓN ACTUAL (FASE 1)

### 3. Módulo de Boxes

1. **Gestión de Boxes**
   - ✅ Implementar CRUD de boxes
   - ✅ Implementar asignación de propietarios
   - ✅ Implementar gestión de miembros
   - ✅ Implementar validación de boxes

2. **Membresía de Box**
   - ✅ Implementar solicitudes de membresía
   - ✅ Implementar aceptación/rechazo de miembros
   - ✅ Implementar roles dentro del box

### 4. Módulo de Ligas

1. **Gestión de Ligas**
   - ✅ Implementar CRUD de ligas
   - ✅ Implementar estados de liga (Preparación, Activa, Finalizada)
   - ✅ Implementar configuración de categorías

2. **Inscripciones**
   - ✅ Implementar sistema de inscripción a ligas
   - ✅ Implementar validación de categorías
   - Implementar integración con Stripe para pagos

### 5. Módulo de WODs

1. **Gestión de WODs**
   - ✅ Implementar CRUD de WODs
   - ✅ Implementar tipos de WOD (AMRAP, FORTIME, EMOM, etc.)
   - ✅ Implementar WODs bonus
   - ✅ Implementar sistema de publicación programada

2. **Validación de WODs**
   - ✅ Implementar validación de fechas
   - ✅ Implementar validación de tipos
   - Implementar límites de resultados esperados

### 6. Módulo de Resultados

1. **Gestión de Resultados**
   - ✅ Implementar registro de resultados
   - ✅ Implementar actualización de resultados
   - ✅ Implementar validación de resultados
   - ✅ Implementar sistema de flags para resultados sospechosos

2. **Validación de Resultados**
   - ✅ Implementar validación por admin
   - ✅ Implementar validación por propietarios de box
   - ✅ Implementar sistema de flags comunitarios esperados

### 7. Módulo de Clasificaciones con Redis

1. **Cálculo de Clasificaciones**
   - ✅ Implementar cálculo de puntuaciones por tipo de WOD
   - ✅ Implementar clasificación general
   - ✅ Implementar clasificaciones por categoría de desempates

2. **Clasificaciones en Tiempo Real con Redis y WebSockets**
   - ✅ Implementar caché con Redis
   - ✅ Implementar WebSockets para actualizaciones en tiempo real
   - ✅ Implementar clasificaciones por categoría

3. **Historial de Clasificaciones**
   - ✅ Implementar historial de posiciones
   - ✅ Implementar clasificaciones semanales
   - ✅ Implementar estadísticas de rendimiento

### 8. Módulo PVP (Player vs Player)

1. **Desafíos Directos**
   - ✅ Implementar creación de desafíos
   - ✅ Implementar aceptación/rechazo de desafíos
   - ✅ Implementar registro de resultados
   - ✅ Implementar sistema de puntos PVP

2. **Desafíos Comunidad**
   - ✅ Implementar creación de desafíos comunidad
   - ✅ Implementar participación en desafíos
   - ✅ Implementar cálculo de premios
   - ✅ Implementar gestión de empates

3. **Notificaciones en Tiempo Real**
   - ✅ Implementar WebSockets para notificaciones de desafíos
   - ✅ Implementar notificaciones push con OneSignal

## IMPLEMENTACIÓN FUTURA (FASE 2)

### 9. Módulo Battle Box

1. **Gestión de Battle Box**
   - Implementar CRUD de Battle Box
   - Implementar inscripción de boxes
   - Implementar gestión de equipos

2. **Gestión de Equipos**
   - Implementar añadir/remover atletas
   - Implementar convocatoria para WODs
   - Implementar validación de composición de equipos

3. **Resultados y Clasificación**
   - Implementar registro de resultados de equipo
   - Implementar cálculo de ranking
   - Implementar sistema de flags y revisión
   - Implementar sistema de repetición de WODs

### 10. Módulo de Eventos Especiales y Badges

1. **Eventos Especiales**
   - Implementar CRUD de eventos
   - Implementar inscripción en eventos
   - Implementar registro de resultados
   - Implementar cálculo de ranking

2. **Badges y Logros**
   - Implementar CRUD de badges
   - Implementar asignación de badges
   - Implementar verificación de condiciones
   - Implementar visualización de badges en perfil

### 11. Módulo de Notificaciones

1. **Sistema de Notificaciones**
   - ✅ Implementar notificaciones en tiempo real con WebSockets
   - ✅ Implementar notificaciones push con OneSignal
   - Implementar notificaciones por email
   - Implementar preferencias de notificación

2. **Integración con Redes Sociales**
   - Implementar generación de imágenes para compartir
   - Implementar integración con API de Instagram

### 12. Tareas Programadas y Procesamiento Asíncrono

1. **Tareas Programadas**
   - Implementar actualización periódica de clasificaciones
   - Implementar envío programado de notificaciones
   - Implementar limpieza de datos temporales

2. **Procesamiento Asíncrono**
   - Implementar colas para cálculo de clasificaciones
   - Implementar procesamiento en segundo plano para tareas pesadas

### 13. Testing

1. **Tests Unitarios**
   - Implementar tests para cada módulo
   - Implementar mocks para dependencias externas

2. **Tests de Integración**
   - Implementar tests para flujos completos
   - Implementar tests para interacción entre módulos

3. **Tests End-to-End**
   - Implementar tests para API completa
   - Implementar tests para flujos de usuario

## Estado Actual de Implementación

### Resumen de Progreso

- ✅ **Configuración del Proyecto**: Completada
- ✅ **Módulos Base**: Autenticación, Usuarios, Boxes implementados
- ✅ **Sistema de Ligas**: Implementación completa
- ✅ **Sistema de WODs**: Implementación completa
- ✅ **Resultados y Clasificaciones**: Implementación completa
- ✅ **Inscripciones**: Implementación parcial (pendiente integración con pasarela de pago)
- ⏳ **PVP**: Módulo creado pero sin implementación
- ❌ **Módulos Futuros**: Battle Box, Eventos Especiales, Badges pendientes

## Plan de Implementación Detallado

### Fase 1: MVP (2-3 Meses) - Configuración y Estructura Base

1. **Semana 1-2: Configuración del Proyecto**
   - Inicializar proyecto NestJS
   - Configurar PostgreSQL, Redis y AWS S3
   - Configurar estructura de carpetas y módulos base
   - Configurar Docker y preparar para Elastic Beanstalk

2. **Semana 3-4: Implementación de Autenticación y Usuarios**
   - Implementar registro e inicio de sesión con Passport.js
   - Implementar JWT y guards
   - Implementar recuperación de contraseña
   - Implementar verificación de email
   - Implementar CRUD de usuarios y boxes

3. **Semana 5-6: Implementación de Ligas y WODs**
   - Implementar CRUD de ligas
   - Implementar CRUD de WODs
   - Implementar inscripciones a ligas
   - Implementar integración con Stripe para pagos

4. **Semana 7-8: Implementación de Resultados y Clasificaciones**
   - Implementar registro de resultados
   - Implementar validación de resultados
   - Implementar cálculo de puntuación
   - Implementar clasificaciones en tiempo real con Redis y WebSockets

5. **Semana 9-10: Testing y Despliegue MVP**
   - Implementar tests unitarios y de integración
   - Configurar CI/CD con GitHub Actions
   - Desplegar en AWS Elastic Beanstalk
   - Realizar pruebas de carga y optimización

### Fase 1 (Actual): MVP y PVP (2-4 Meses)

1. **Mes 3-4: Implementación de PVP**
   - Implementar desafíos directos
   - Implementar desafíos comunidad
   - Implementar sistema de puntos PVP
   - Implementar notificaciones en tiempo real

### Fase 2 (Futura): Escalado (6-12 Meses) - Funcionalidades Avanzadas

1. **Mes 5-6: Implementación de Battle Box**
   - Implementar gestión de Battle Box
   - Implementar gestión de equipos
   - Implementar resultados y clasificación de equipos
   - Implementar sistema de flags y revisión

3. **Mes 7-8: Implementación de Eventos Especiales y Badges**
   - Implementar eventos especiales
   - Implementar sistema de badges
   - Implementar asignación de badges
   - Implementar integración con redes sociales

4. **Mes 9-10: Optimización y Escalabilidad**
   - Implementar índices en PostgreSQL para consultas frecuentes
   - Optimizar caché en Redis
   - Configurar auto-scaling en Kubernetes
   - Implementar monitoreo con Prometheus y Grafana

5. **Mes 11-12: Testing Avanzado y Documentación**
   - Implementar tests end-to-end
   - Implementar tests de carga
   - Documentar API con Swagger
   - Preparar documentación técnica y de usuario

## Implementación Técnica Detallada

### Modelos de Datos (PostgreSQL con TypeORM)

Basado en el diagrama de clases proporcionado, implementaremos las siguientes entidades en PostgreSQL:

1. **Usuario**
```typescript
@Entity()
export class Usuario {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  nombre: string;

  @Column()
  alias: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string; // Hashed

  @Column({ nullable: true })
  foto?: string;

  @Column({
    type: 'enum',
    enum: ['RX', 'Intermedio', 'Scaled'],
  })
  nivel: string;

  @Column({
    type: 'enum',
    enum: ['Masculino', 'Femenino'],
  })
  genero: string;

  @ManyToOne(() => Box, { nullable: true })
  box: Box;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  fecha_registro: Date;

  @Column({ type: 'timestamp', nullable: true })
  ultima_conexion?: Date;

  @Column({ default: 20 })
  puntos_pvp: number;

  @Column({
    type: 'enum',
    enum: ['Usuario', 'BoxOwner', 'Admin'],
    default: 'Usuario',
  })
  rol: string;

  @Column({
    type: 'enum',
    enum: ['Activo', 'Inactivo', 'Suspendido'],
    default: 'Activo',
  })
  estado: string;

  @OneToMany(() => Resultado, resultado => resultado.usuario)
  resultados: Resultado[];

  @OneToMany(() => Inscripcion, inscripcion => inscripcion.usuario)
  inscripciones: Inscripcion[];
}
```

### Implementación de Redis para Clasificaciones en Tiempo Real

Utilizaremos Redis para almacenar y actualizar las clasificaciones en tiempo real:

```typescript
// Ejemplo de implementación de servicio de clasificación con Redis
@Injectable()
export class ClasificacionService {
  constructor(
    private readonly redisService: RedisService,
    private readonly resultadoService: ResultadoService,
  ) {}

  async actualizarClasificacion(ligaId: string, categoriaId: string): Promise<void> {
    // Obtener resultados y calcular puntuaciones
    const resultados = await this.resultadoService.obtenerResultadosPorLiga(ligaId);
    const clasificacion = this.calcularPuntuaciones(resultados, categoriaId);

    // Guardar en Redis
    const cacheKey = `clasificacion:${ligaId}:${categoriaId}`;
    await this.redisService.set(cacheKey, JSON.stringify(clasificacion));

    // Establecer TTL para la caché
    await this.redisService.expire(cacheKey, 3600); // 1 hora

    // Notificar a clientes conectados vía WebSockets
    this.clasificacionGateway.emitirActualizacionClasificacion(ligaId, categoriaId);
  }

  async obtenerClasificacion(ligaId: string, categoriaId: string): Promise<any[]> {
    const cacheKey = `clasificacion:${ligaId}:${categoriaId}`;
    const cachedData = await this.redisService.get(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Si no hay datos en caché, calcular y guardar
    await this.actualizarClasificacion(ligaId, categoriaId);
    return JSON.parse(await this.redisService.get(cacheKey));
  }

  private calcularPuntuaciones(resultados: Resultado[], categoriaId: string): any[] {
    // Implementación del cálculo de puntuaciones según tipo de WOD
    // ...
  }
}
```

### Implementación de WebSockets para Actualizaciones en Tiempo Real

```typescript
@WebSocketGateway({ namespace: 'clasificaciones' })
export class ClasificacionGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  afterInit(server: Server) {
    console.log('Clasificacion WebSocket Gateway initialized');
  }

  handleConnection(client: Socket, ...args: any[]) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('suscribirClasificacion')
  handleSuscripcion(client: Socket, payload: { ligaId: string, categoriaId: string }): void {
    const room = `clasificacion:${payload.ligaId}:${payload.categoriaId}`;
    client.join(room);
    console.log(`Client ${client.id} joined room ${room}`);
  }

  emitirActualizacionClasificacion(ligaId: string, categoriaId: string): void {
    const room = `clasificacion:${ligaId}:${categoriaId}`;
    this.server.to(room).emit('actualizacionClasificacion', {
      ligaId,
      categoriaId,
      timestamp: new Date().toISOString(),
    });
  }
}
```

### Implementación del Cálculo de Puntuaciones

```typescript
// Servicio para cálculo de puntuaciones
@Injectable()
export class PuntuacionService {
  calcularPuntuacion(resultado: Resultado, mejorResultado: Resultado, tipoWOD: string): number {
    let puntuacion = 0;

    switch (tipoWOD) {
      case 'FORTIME':
        // Menor tiempo es mejor
        puntuacion = 100 * (mejorResultado.valor / resultado.valor);
        break;
      case 'AMRAP':
      case 'MAXREPS':
        // Mayor número de repeticiones es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      case 'MAXWEIGHT':
        // Mayor peso es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      case 'EMOM':
        // Mayor número de minutos completados es mejor
        puntuacion = 100 * (resultado.valor / mejorResultado.valor);
        break;
      default:
        throw new Error(`Tipo de WOD no soportado: ${tipoWOD}`);
    }

    // Aplicar bonus de consistencia
    puntuacion += 10; // +10 puntos por WOD completado

    return Math.round(puntuacion * 100) / 100; // Redondear a 2 decimales
  }

  calcularBonusConsistenciaFinal(wodsTotales: number, wodsCompletados: number): number {
    const porcentajeCompletado = (wodsCompletados / wodsTotales) * 100;

    if (porcentajeCompletado === 100) {
      return 100;
    } else if (porcentajeCompletado >= 90) {
      return 75;
    } else if (porcentajeCompletado >= 80) {
      return 50;
    } else {
      return 0;
    }
  }
}
```

## Requisitos de Infraestructura

### Desarrollo Local
- Docker y Docker Compose para entorno de desarrollo
- PostgreSQL local para desarrollo
- Redis local para desarrollo
- Node.js v16+ y npm/yarn

### Producción (MVP - AWS Elastic Beanstalk)
- AWS RDS para PostgreSQL
- AWS ElastiCache para Redis
- AWS S3 para almacenamiento de archivos
- AWS Elastic Beanstalk para despliegue
- AWS CloudWatch para monitoreo

### Producción (Escalado - AWS EKS)
- AWS EKS (Kubernetes) para orquestación de contenedores
- AWS RDS para PostgreSQL con réplicas de lectura
- AWS ElastiCache para Redis en modo cluster
- AWS S3 con CloudFront para CDN
- AWS CloudWatch y Prometheus/Grafana para monitoreo

## Estimación de Recursos

### Equipo de Desarrollo
- 1-2 desarrolladores backend con experiencia en NestJS y TypeScript
- 1 DevOps para configuración de infraestructura
- 1-2 desarrolladores frontend (fuera del alcance de este documento)

### Tiempo de Desarrollo
- MVP: 2-3 meses
- Versión completa: 6-12 meses adicionales

### Costes Estimados (según informe de infraestructura)
- MVP (1000 usuarios): ~$64.00/mes
- Escalado (10,000 usuarios): ~$317.00/mes

## Conclusión

Este plan de desarrollo proporciona una hoja de ruta detallada para implementar el backend de The WOD League utilizando NestJS, PostgreSQL y Redis. La arquitectura propuesta está diseñada para ser modular, escalable y optimizada para los requisitos específicos de la aplicación, siguiendo las recomendaciones del informe de infraestructura. El enfoque en fases permite un desarrollo ágil, comenzando con un MVP funcional y expandiéndose gradualmente para incluir todas las funcionalidades avanzadas.
