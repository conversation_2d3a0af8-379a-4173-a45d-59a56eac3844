## 📋 TODO: <PERSON><PERSON>s <PERSON>

- Revisar la guía oficial de NestJS para autenticación JWT: https://docs.nestjs.com/security/authentication
- Verificar que todos los endpoints de autenticación estén implementados en el backend de NestJS
- Implementar tests automatizados para los flujos de autenticación
- Considerar el uso de refresh tokens para mejorar la seguridad y experiencia de usuario
- Documentar los nuevos flujos de autenticación para el equipo de desarrollo
- Añadir configuración de reenvío de email para permitir personalizar el contenido del correo
- Crear una página web de verificación para cuando el usuario haga clic en el enlace desde un dispositivo diferente
