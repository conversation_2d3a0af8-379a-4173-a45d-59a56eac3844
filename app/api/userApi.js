import { STRAPI_URL, NESTJS_URL } from '../config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

/**
 * Obtener un token de autenticación válido para comunicación con Strapi
 * @returns {Promise<string>} Token de autenticación de Strapi
 */
const getAuthToken = async () => {
  try {
    // ÚNICA OPCIÓN: Obtener token desde strapi-auth
    // El token de Firebase (jwt) solo se usa para el login inicial
    const strapiAuth = await AsyncStorage.getItem('strapi-auth');
    if (!strapiAuth) {
      console.warn('No se encontró token de autenticación de Strapi');
      throw new Error('No hay token de Strapi disponible. Es necesario iniciar sesión nuevamente');
    }
    
    let token = null;
    try {
      const parsed = JSON.parse(strapiAuth);
      token = parsed.jwt;
    } catch (parseError) {
      console.warn('Error al parsear strapi-auth:', parseError);
      throw new Error('Token de Strapi corrupto. Es necesario iniciar sesión nuevamente');
    }
    
    // Verificar que el token no está vacío
    if (!token || token.trim() === '') {
      console.warn('Token de Strapi vacío');
      throw new Error('Token de Strapi inválido. Es necesario iniciar sesión nuevamente');
    }
 
    return token;
  } catch (error) {
    console.error('Error obteniendo token de Strapi:', error);
    throw new Error('Error de autenticación: ' + (error.message || 'Error desconocido'));
  }
};

/**
 * Procesar la respuesta de Strapi y manejar errores
 * @param {Response} response - Respuesta de fetch
 * @returns {Promise<Object>} Datos procesados
 */
const handleResponse = async (response) => {
  if (!response.ok) {
    // Intentar obtener información detallada del error
    let errorMessage = 'Error en la petición';
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || 'Error desconocido';
      console.error('Error de API:', errorData);
    } catch (e) {
      console.error('Error al procesar respuesta de error:', e);
    }
    
    if (response.status === 401) {
      errorMessage = 'Error de autenticación: Sesión expirada';
    } else if (response.status === 404) {
      errorMessage = 'Usuario no encontrado';
    }
    
    throw new Error(errorMessage);
  }
  
  return await response.json();
};

/**
 * Obtener el perfil de un usuario
 * @param {string} userId - ID del usuario en Firebase
 * @returns {Promise<Object>} Datos del perfil
 */
const getUserProfile = async (userId) => {
  try {
    const token = await getAuthToken();
    
    // Asegurar que el formato del header es exactamente el mismo que en LoginScreen
    const authHeader = `Bearer ${token}`;
    
    const response = await fetch(`${STRAPI_URL}/api/users/me?populate=*`, {
      headers: {
        'Authorization': authHeader
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error en la respuesta de getUserProfile:', errorData);
      throw new Error(errorData.error?.message || 'Error en la petición');
    }

    const userData = await response.json();
    
    // Conservamos los datos exactamente como vienen de Strapi (SSOT)
    // Solo añadimos el valor predeterminado si realmente falta el campo
    if (!userData.category) {
      console.warn('⚠️ Campo category no encontrado en datos de Strapi. Usando valor predeterminado "Sin Asignar"');
      userData.category = 'Sin Asignar'; // Consistente con el valor predeterminado en el esquema
    }
    
    return { data: userData }; 
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

/**
 * Obtener las estadísticas de un usuario
 * @param {string} userId - ID del usuario en Firebase
 * @returns {Promise<Object>} Estadísticas del usuario
 */
const getUserStats = async (userId) => {
  try {
    const token = await getAuthToken();
    
    // Asegurar que el formato del header es exactamente el mismo que en LoginScreen
    const authHeader = `Bearer ${token}`;
    
    const response = await fetch(`${STRAPI_URL}/api/users/me`, {
      headers: {
        'Authorization': authHeader
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error en la respuesta de getUserStats:', errorData);
      throw new Error(errorData.error?.message || 'Error en la petición');
    }

    const data = await response.json();
    return {
      data: {
        partidasJugadas: data.partidasJugadas ?? 0,
        victorias: data.victorias ?? 0,
        nivelPVP: data.nivelPVP ?? 1,
        progresoXP: data.progresoXP ?? 0,
        mejorPosicion: data.mejorPosicion ?? 0,
        puntosTotales: data.puntosTotales ?? 0,
        username: data.username,
        level: data.level || '-'
      }
    }; // Asegurar estructura consistente
  } catch (error) {
    console.error('Error fetching user stats:', error);
    throw error;
  }
};

/**
 * Actualizar las estadísticas de un usuario
 * @param {string} userId - ID del usuario en Firebase
 * @param {Object} stats - Nuevas estadísticas
 * @returns {Promise<Object>} Estadísticas actualizadas
 */
const updateUserStats = async (userId, stats) => {
  try {
    const token = await getAuthToken();
    
    const updateResponse = await fetch(`${STRAPI_URL}/api/users/me`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(stats)
    });

    const updatedData = await handleResponse(updateResponse);
    return { data: updatedData };
  } catch (error) {
    console.error('Error updating user stats:', error);
    throw error;
  }
};

/**
 * Actualizar el perfil de un usuario
 * @param {string} userId - ID del usuario en Firebase
 * @param {Object} profileData - Nuevos datos del perfil
 * @returns {Promise<Object>} Perfil actualizado
 */
const updateUserProfile = async (userId, profileData) => {
  try {    
    // Obtener token actualizado para cada petición
    const token = await getAuthToken();
    
    if (!token) {
      console.error('No se pudo obtener token de autenticación');
      throw new Error('No hay token de autenticación disponible');
    }
    
    // Verificar qué datos estamos enviando - añadir depuración temporal
    console.log('Datos enviados a Strapi:', JSON.stringify(profileData));
    
    // Asegurar que los campos tienen el formato esperado por Strapi
    const sanitizedData = {};
    Object.keys(profileData).forEach(key => {
      // Evitar enviar campos nulos, indefinidos o con formato incorrecto
      if (profileData[key] !== null && profileData[key] !== undefined) {
        sanitizedData[key] = profileData[key];
      }
    });
    
    console.log('Datos sanitizados:', JSON.stringify(sanitizedData));
    
    // CORRECCIÓN: Usar el endpoint con ID explícito (/api/users/:id) en lugar de /me
    const strapiUserId = await getStrapiUserId();
    
    if (!strapiUserId) {
      throw new Error('No se pudo obtener el ID de usuario de Strapi');
    }
    
    console.log(`Usando endpoint con ID: ${STRAPI_URL}/api/users/${strapiUserId}`);
    
    const updateResponse = await fetch(`${STRAPI_URL}/api/users/${strapiUserId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(sanitizedData)
    });
    
    console.log('Status de respuesta:', updateResponse.status);
    
    // Para otros errores, mantener el comportamiento original
    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      console.error('Respuesta de error completa:', errorText);
      try {
        const errorJson = JSON.parse(errorText);
        console.error('Detalles del error:', errorJson);
        throw new Error(errorJson.error?.message || 'Error al actualizar perfil');
      } catch (parseError) {
        throw new Error('Error al actualizar perfil: ' + errorText);
      }
    }
      
    // Procesar respuesta
    const updatedData = await handleResponse(updateResponse);
    
    return { data: updatedData };
  } catch (error) {
    console.error('Error updating user profile:', error);
    
    // Mejorar mensaje de error para el usuario
    const userMessage = error.message.includes('No hay token') || 
                         error.message.includes('Error de autenticación') ?
                         'Sesión expirada. Por favor, vuelve a iniciar sesión.' :
                         error.message;
    
    throw new Error(userMessage);
  }
};

/**
 * Obtener el ID de usuario de Strapi del token actual
 * @returns {Promise<string|null>} ID de usuario en Strapi o null si no se encuentra
 */
const getStrapiUserId = async () => {
  try {
    // Intentar obtener el ID desde el endpoint /me
    const response = await fetch(`${STRAPI_URL}/api/users/me`, {
      headers: {
        'Authorization': `Bearer ${await getAuthToken()}`
      }
    });

    if (!response.ok) {
      console.error('Error al obtener ID de usuario:', await response.text());
      return null;
    }

    const userData = await response.json();
    return userData.id?.toString();
  } catch (error) {
    console.error('Error en getStrapiUserId:', error);
    return null;
  }
};

/**
 * Obtiene un token JWT de NestJS del almacenamiento local
 * @returns {Promise<string>} Token JWT
 */
const getNestJsToken = async () => {
  const token = await AsyncStorage.getItem('auth-token');
  if (!token) {
    throw new Error('No hay token JWT de NestJS');
  }
  return token;
};

/**
 * Obtiene el ID del usuario actual de NestJS desde el token JWT
 * @returns {Promise<string>} ID del usuario
 */
const getNestJsUserId = async () => {
  try {
    const token = await getNestJsToken();
    const tokenData = JSON.parse(atob(token.split('.')[1]));
    return tokenData.sub;
  } catch (error) {
    console.error('Error obteniendo ID de usuario de NestJS:', error);
    throw error;
  }
};

/**
 * Obtiene el perfil del usuario de NestJS
 * @returns {Promise<Object>} Datos del perfil
 */
const getNestJsUserProfile = async () => {
  try {
    const token = await getNestJsToken();
    
    const response = await axios.get(`${NESTJS_URL}/api/usuarios/perfil`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return { data: response.data };
  } catch (error) {
    console.error('Error obteniendo perfil de NestJS:', error.response?.data || error);
    throw error;
  }
};

/**
 * Actualiza el perfil del usuario en NestJS
 * @param {Object} profileData - Datos a actualizar
 * @returns {Promise<Object>} Datos actualizados
 */
const updateNestJsUserProfile = async (profileData) => {
  try {
    const token = await getNestJsToken();
    const userId = await getNestJsUserId();
    
    const response = await axios.patch(
      `${NESTJS_URL}/api/usuarios/${userId}`, 
      profileData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return { data: response.data };
  } catch (error) {
    console.error('Error actualizando perfil en NestJS:', error.response?.data || error);
    throw error;
  }
};

/**
 * Restablece el estado de onboarding del usuario en NestJS
 * @returns {Promise<Object>} Datos actualizados
 */
const resetOnboarding = async () => {
  try {
    // Actualizar el estado de onboarding en el backend
    return await updateNestJsUserProfile({
      onBoarding: false
    });
  } catch (error) {
    console.error('Error al restablecer onboarding en NestJS:', error.response?.data || error);
    throw error;
  }
};

export default {
  // Funciones de Strapi (legacy)
  getUserStats,
  updateUserStats,
  getUserProfile,
  updateUserProfile,
  getStrapiUserId,
  
  // Funciones de NestJS (nuevas)
  getNestJsUserProfile,
  updateNestJsUserProfile,
  getNestJsUserId,
  resetOnboarding
};
