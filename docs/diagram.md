@startuml
'===========================
' Modelo de Datos Principal
'===========================

class Usuario {
  + id: int
  + nombre: string
  + alias: string
  + edad: int "nullable durante registro"
  + email: string
  + password (hash): string
  + foto: string "path/URL"
  + nivel: enum {RX, Intermedio, Scaled} "nullable durante registro"
  + genero: enum {Masculino, Femenino} "nullable durante registro"
  + box_id: int "opcional"
  + fecha_registro: date
  + ultima_conexion: date
  + puntos_pvp: int = 20
  + rol: enum {Usuario, BoxOwner, Admin}
  + estado: enum {Activo, Inactivo, Suspendido}
  + emailVerificado: boolean = false
  + tokenVerificacion: string "token para verificación de email"
  + tokenVerificacionExpira: date "fecha expiración del token"
  + setupCompleted: boolean = false "completó configuración inicial"
  + onBoarding: boolean = false "completó proceso de onboarding"
}

class Box {
  + id: int
  + nombre: string
  + ubicacion: string
  + owner_id: int
  + fecha_registro: date
  + descripcion: string
  + logo: string "path/URL"
  + estado: enum {Activo, Inactivo}
}

class Liga {
  + id: int
  + nombre: string
  + descripcion: string
  + fecha_inicio: date
  + fecha_fin: date
  + duracion_semanas: int = 10
  + estado: enum {Preparacion, Activa, Finalizada}
  + precio_inscripcion: float
  + tiene_descuento_early: boolean
  + precio_early: float
  + bonusConsistencia: boolean = true
  + categoria_rx: boolean = true
  + categoria_intermedio: boolean = true
  + categoria_scaled: boolean = true
  + genero_masculino: boolean = true
  + genero_femenino: boolean = true
}

class WOD {
  + id: int
  + liga_id: int
  + tipo_evento_id: int
  + titulo: string
  + descripcion: string
  + tipo: enum {AMRAP, FORTIME, EMOM, MAXWEIGHT, MAXREPS}
  + fecha_publicacion: date
  + fecha_limite: date
  + es_bonus: boolean
  + semana: int
  + estado: enum {Borrador, Publicado, Cerrado}
  + imagen: string "path/URL"
  + movimientos: json
  + resultado_limite: string "Valor límite máximo esperado para validación"
}

class Resultado {
  + id: int
  + usuario_id: int
  + wod_id: int
  + valor: float
  + fecha_envio: date
  + validado: boolean
  + validado_por: int
  + comentario: string
  + flags: int
  + flags_recibidos: int
  + modificaciones: int
  + ultima_modificacion: timestamp
  + puntuacion_raw: float
  + tiempo_cap: boolean
}

class Inscripcion {
  + id: int
  + usuario_id: int
  + liga_id: int
  + categoria_id: int
  + fecha_inscripcion: date
  + precio_pagado: float
  + estado_pago: string
  + metodo_pago: string
  + referencia_pago: string
  + puntos_totales: int
  + wods_completados: int
  + posicion_actual: int
}

class Categoria {
  + id: int
  + nombre: string
  + genero: enum {Masculino, Femenino}
  + nivel: enum {RX, Intermedio, Scaled}
  + descripcion: string
}

'===========================
' Sistema de Clasificación
'===========================

class Clasificacion {
  + id: int
  + liga: relation "Liga" "manyToOne" "Modelo Strapi, sin sufijo _id"
  + categoria_id: int "Campo entero, mantiene sufijo _id"
  + genero: enum {Masculino, Femenino}
  + nivel: enum {RX, Intermedio, Scaled}
  + fecha_creacion: date
  + ultima_actualizacion: timestamp
  + estado: enum {Activa, Finalizada}
  + cache_key: string
  + ultima_calculada: timestamp
  + total_participantes: int
}

class RankingUsuario {
  + id: int
  + clasificacion_id: int
  + usuario_id: int
  + posicion_actual: int
  + posicion_anterior: int
  + puntos_totales: float
  + puntos_por_rendimiento: float
  + puntos_por_consistencia: float
  + wods_completados: int
  + ultima_actualizacion: timestamp
  + streak_actual: int
  + cambio_posicion: int
  + promedio_rendimiento: float
}

class RankingSemanal {
  + id: int
  + ranking_usuario_id: int
  + semana: int
  + posicion: int
  + puntos_semana: float
  + wods_completados_semana: int
  + fecha_calculo: timestamp
}

class OutlierDetection {
  + id: int
  + categoria_id: int
  + tipo_wod: enum {AMRAP, FORTIME, EMOM, MAXWEIGHT, MAXREPS}
  + limite_inferior: float
  + limite_superior: float
  + desviacion_estandar: float
  + ultima_actualizacion: date
}

class ClasificacionCache {
  + id: int
  + clasificacion_id: int
  + data: json
  + fecha_generacion: timestamp
  + es_valida: boolean
  + tipo: enum {Bruta, Oficial, Historica}
}

'===========================
' Modelo de Datos para PVP
'===========================

class DesafioDirecto {
  + id: int
  + retador_id: int
  + retado_id: int
  + wod_id: int
  + puntos_apuesta: int
  + fecha_creacion: date
  + fecha_limite: date
  + estado: enum {Pendiente, Aceptado, Rechazado, Completado, Expirado}
  + ganador_id: int
  + resultado_retador_id: int
  + resultado_retado_id: int
  + puntos_ganados_retador: int
  + puntos_ganados_retado: int
  + fecha_resolucion: date
  + empate: boolean
  + notificado: boolean
  + recordatorio_enviado: boolean
}

class DesafioComunidad {
  + id: int
  + creador_id: int
  + wod_id: int
  + nivel: enum {RX, Intermedio, Scaled}
  + min_participantes: int = 5
  + max_participantes: int = 20
  + punto_entrada: int = 5
  + plazo_dias: int = 7
  + distribucion_premio: string "JSON con la distribución de premios"
  + fecha_creacion: date
  + fecha_limite: date
  + estado: enum {Pendiente, Activo, Completo, Cancelado}
  + participantes_actuales: int
}

class ParticipanteDesafioComunidad {
  + id: int
  + desafio_id: int
  + usuario_id: int
  + resultado_id: int
  + puntos_apostados: int
  + puntos_ganados: int
  + posicion: int
  + fecha_inscripcion: date
}

'===========================
' Modelo de Datos para Battle Box
'===========================

class BattleBox {
  + id: int
  + nombre: string
  + descripcion: string
  + fecha_inicio: date
  + fecha_fin: date
  + duracion_semanas: int
  + estado: enum {Preparacion, Activa, Finalizada}
  + precio_inscripcion: float
  + min_atletas_por_equipo: int
  + max_atletas_por_equipo: int
  + min_mujeres_por_equipo: int
}

class BoxEquipo {
  + id: int
  + box_id: int
  + battlebox_id: int
  + nombre_equipo: string "opcional"
  + flags_disponibles: int
  + puntos_totales: int
  + puntos_penalizacion: int = 0
  + repeticiones_realizadas: int = 0
  + posicion_actual: int
  + fecha_inscripcion: date
  + estado_pago: string
  + metodo_pago: string
  + referencia_pago: string
  + ultimo_wod_completado: int
  + participacion_porcentaje: float
  + miembros_confirmados: int
}

class AtletaBoxEquipo {
  + id: int
  + equipo_id: int
  + usuario_id: int
  + es_activo: boolean
  + wods_participados: int = 0
  + es_convocado_actual: boolean
  + fecha_adicion: date
}

class ResultadoEquipo {
  + id: int
  + equipo_id: int
  + wod_id: int
  + valor: float
  + puntos_obtenidos: float
  + posicion_ranking: int
  + repetido: boolean = false
  + fecha_envio: date
  + validado: boolean
  + flags: int
  + repeticiones_disponibles: int
  + comentario: string
}

class FlagResultado {
  + id: int
  + resultado_id: int
  + tipo_resultado: enum {Individual, Equipo}
  + flaggedor_id: int
  + tipo_flaggedor: enum {Usuario, Box}
  + justificacion: string
  + fecha_flag: date
  + estado: enum {Pendiente, Aprobado, Rechazado, Resuelto}
  + resolucion: text
}

class ComiteRevisionBattleBox {
  + id: int
  + battlebox_id: int
  + box_revisor_id: int
  + resultado_revisado_id: int
  + flag_revisado_id: int
  + decision: enum {Aprobado, Rechazado, Pendiente}
  + justificacion: string
  + fecha_revision: date
  + tiempo_respuesta: int
  + votos_necesarios: int
  + box_implicados: json
}

'===========================
' Modelo de Datos para Eventos Especiales y Notificaciones
'===========================

class EventoEspecial {
  + id: int
  + nombre: string
  + tematica: string
  + descripcion: string
  + fecha_inicio: date
  + fecha_fin: date
  + duracion_dias: int = 3
  + wod_id: int
  + imagen: string "path/URL"
  + estado: enum {Preparacion, Activo, Finalizado}
  + requiere_pago: boolean
  + precio: float
  + con_badges: boolean = true
  + max_participantes: int
}

class Badge {
  + id: int
  + nombre: string
  + descripcion: string
  + imagen: string "path/URL"
  + tipo: enum {Participacion, Rendimiento, Consistencia, Especial}
  + evento_id: int
  + condicion: string
  + rareza: enum {Comun, Raro, Epico, Legendario}
  + fecha_disponible_desde: date
  + fecha_disponible_hasta: date
  + es_coleccionable: boolean
  + codigo: string
}

class UsuarioBadge {
  + id: int
  + usuario_id: int
  + badge_id: int
  + fecha_obtencion: date
  + detalle: string
}


'===========================
' Relaciones entre Clases
'===========================

' Modelo Principal
Usuario "1" -- "0..1" Box : pertenece a
Box -- "1" Usuario : owner <<owner_id>>
Liga "1" -- "0..*" WOD : contiene
Usuario "1" -- "0..*" Resultado : registra
WOD "1" -- "0..*" Resultado : genera
Usuario "1" -- "0..*" Inscripcion : se inscribe
Liga "1" -- "0..*" Inscripcion : tiene
Categoria "1" -- "0..*" Inscripcion : clasifica

' Sistema de Clasificación
Liga "1" -- "0..*" Clasificacion : genera
Categoria "1" -- "0..*" Clasificacion : define_parametros
Clasificacion "1" -- "0..*" RankingUsuario : contiene
Usuario "1" -- "0..*" RankingUsuario : tiene_posicion
RankingUsuario "1" -- "0..*" RankingSemanal : historial
Clasificacion "1" -- "0..*" ClasificacionCache : cachea
Categoria "1" -- "0..*" OutlierDetection : valida

' Módulo PVP
DesafioDirecto "1" -- "1" Usuario : retador <<retador_id>>
DesafioDirecto "1" -- "1" Usuario : retado <<retado_id>>
DesafioDirecto "1" -- "0..1" Resultado : resultado_retador <<resultado_retador_id>>
DesafioDirecto "1" -- "0..1" Resultado : resultado_retado <<resultado_retado_id>>
WOD "1" -- "0..*" DesafioDirecto : utiliza
DesafioComunidad "1" -- "1" Usuario : creador <<creador_id>>
WOD "1" -- "0..*" DesafioComunidad : utiliza
ParticipanteDesafioComunidad "1" -- "1" DesafioComunidad : participa en
ParticipanteDesafioComunidad "1" -- "0..1" Resultado : registra <<resultado_id>>
Usuario "1" -- "0..*" ParticipanteDesafioComunidad : participa

' Módulo Battle Box
BattleBox "1" -- "0..*" BoxEquipo : contiene
Box "1" -- "0..*" BoxEquipo : registra
BoxEquipo "1" -- "0..*" AtletaBoxEquipo : compone
Usuario "1" -- "0..*" AtletaBoxEquipo : forma parte
BoxEquipo "1" -- "0..*" ResultadoEquipo : genera
WOD "1" -- "0..*" ResultadoEquipo : utiliza
ResultadoEquipo "1" -- "0..*" FlagResultado : puede tener
FlagResultado "1" -- "0..*" ComiteRevisionBattleBox : es_revisado_por
Box "1" -- "0..*" ComiteRevisionBattleBox : revisa

' Módulo Eventos Especiales
EventoEspecial "1" -- "0..*" Badge : otorga
Badge "1" -- "0..*" UsuarioBadge : asigna
Usuario "1" -- "0..*" UsuarioBadge : posee

@enduml
