import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * Guard to check if a user is the owner of the resource or has admin privileges
 * This ensures users can only modify their own data unless they have admin role
 */
@Injectable()
export class OwnershipGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  /**
   * Determine if the current user can access the requested resource
   * @param context Execution context containing the request
   * @returns Boolean indicating if the user is authorized
   */
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const params = request.params;
    
    // If user is admin, allow access
    if (user && user.rol === 'Admin') {
      return true;
    }
    
    // If user is trying to access their own resource, allow access
    if (user && params.id && user.sub === params.id) {
      return true;
    }
    
    // Otherwise, deny access
    throw new ForbiddenException('You do not have permission to access this resource');
  }
}
