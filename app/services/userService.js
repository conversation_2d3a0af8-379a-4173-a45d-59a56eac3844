/**
 * User Service
 * Manages all operations related to user data with caching and optimized API calls
 */
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../api/apiClient';
import cacheManager from '../utils/cacheManager';
import { NESTJS_URL } from '../config';

const CACHE_KEYS = {
  PROFILE: 'user_profile',
  STATS: 'user_stats',
  BADGES: 'user_badges',
  RESULTS: 'user_results',
  BOX: 'user_box',
};

const userService = {
  /**
   * Get user profile data with cache support
   * @param {boolean} forceRefresh - Whether to force a fresh API call
   * @returns {Promise<Object>} - User profile data
   */
  async getProfile(forceRefresh = false) {
    try {
      // If not forcing refresh, try to get from cache first
      if (!forceRefresh) {
        const cachedProfile = await cacheManager.get(CACHE_KEYS.PROFILE);
        if (cachedProfile) {
          return cachedProfile;
        }
      }

      // Get fresh data from API
      console.log('Obteniendo datos de usuario de NestJS...');

      try {
        // Try the correct endpoint first
        const profile = await apiClient.get('/api/perfil');

        // Save to cache (10 minutes TTL)
        await cacheManager.set(CACHE_KEYS.PROFILE, profile);

        return profile;
      } catch (error) {
        console.error('Error obteniendo perfil de NestJS:', error.response?.data || error.message);

        // If we have a 403 error but have cached data, return the cached data
        if (error.response?.status === 403) {
          const cachedProfile = await cacheManager.get(CACHE_KEYS.PROFILE);
          if (cachedProfile) {
            console.log('Usando datos en caché debido a error 403');
            return cachedProfile;
          }

          // If no cached data, create a default profile
          const defaultProfile = {
            id: 'unknown',
            username: 'usuario',
            fullName: 'Usuario',
            email: '<EMAIL>',
            category: 'Sin Asignar',
            verified: false
          };

          // Save default profile to cache
          await cacheManager.set(CACHE_KEYS.PROFILE, defaultProfile, 10 * 60 * 1000);

          return defaultProfile;
        }

        throw error;
      }
    } catch (error) {
      console.error('Error obteniendo perfil de NestJS:', error.response?.data || error.message);

      // Last resort - check if we have any cached data
      const cachedProfile = await cacheManager.get(CACHE_KEYS.PROFILE);
      if (cachedProfile) {
        console.log('Usando datos en caché como último recurso');
        return cachedProfile;
      }

      throw error;
    }
  },

  /**
   * Update user profile
   * @param {Object} profileData - New profile data
   * @returns {Promise<Object>} - Updated profile
   */
  async updateProfile(profileData) {
    try {
      // Update on server using the correct endpoint
      const updatedProfile = await apiClient.patch('/api/perfil', profileData);

      // Update cache with new data
      await cacheManager.set(CACHE_KEYS.PROFILE, updatedProfile);

      return updatedProfile;
    } catch (error) {
      console.error('Error actualizando perfil:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Upload profile photo
   * @param {string} imageBase64 - Base64 encoded image
   * @param {string} fileName - Optional filename
   * @returns {Promise<Object>} - Response with photo URL
   */
  async uploadProfilePhoto(imageBase64, fileName = 'profile.jpg') {
    try {
      console.log('📸 Iniciando subida de foto de perfil a NestJS');

      // Determine mime type from base64 string or default to jpeg
      let mimeType = 'image/jpeg';
      let base64Data = imageBase64;

      if (imageBase64.startsWith('data:')) {
        mimeType = imageBase64.split(';')[0].split(':')[1];
        base64Data = imageBase64.split(',')[1];
      }

      // Create FormData object for multipart/form-data upload
      const formData = new FormData();

      // Create a file object for React Native
      const fileObj = {
        uri: Platform.OS === 'ios'
          ? `data:${mimeType};base64,${base64Data}`
          : imageBase64,
        name: fileName,
        type: mimeType
      };

      // Append to FormData - the key must match the field name expected by Multer
      formData.append('file', fileObj);

      console.log('Enviando imagen al servidor como FormData');
      console.log('URL del servidor:', `${NESTJS_URL}/api/perfil/photo`);

      // Get the auth token
      const authToken = await AsyncStorage.getItem('auth-token');
      if (!authToken) {
        throw new Error('No se encontró token de autenticación');
      }

      // Use fetch API directly for better control over multipart/form-data
      const response = await fetch(`${NESTJS_URL}/api/perfil/photo`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        body: formData
      });

      // Handle response
      const responseText = await response.text();
      console.log('Respuesta del servidor (texto):', responseText);

      let responseData;

      try {
        responseData = JSON.parse(responseText);
        console.log('Respuesta del servidor (JSON):', responseData);
      } catch (e) {
        console.warn('No se pudo parsear la respuesta como JSON:', e);
        responseData = { message: responseText };
      }

      if (!response.ok) {
        console.error('Error en respuesta del servidor:', responseData);
        throw new Error(JSON.stringify(responseData));
      }

      // Verificar que la respuesta contenga la URL de la foto
      if (!responseData.foto && !responseData.photoUrl) {
        console.warn('⚠️ La respuesta del servidor no contiene URL de foto');
        console.log('Respuesta completa:', responseData);

        // Intentar extraer la URL de la foto de la respuesta
        if (responseData.message && responseData.message.includes('/uploads/profiles/')) {
          const urlMatch = responseData.message.match(/\/uploads\/profiles\/[^"'\s]+/);
          if (urlMatch) {
            responseData.foto = urlMatch[0];
            console.log('URL de foto extraída del mensaje:', responseData.foto);
          }
        }
      }

      // Update cached profile with new photo URL if available
      if (responseData) {
        // Asegurarse de que tenemos una URL de foto
        const photoUrl = responseData.foto || responseData.photoUrl;

        if (photoUrl) {
          console.log('✅ URL de foto recibida:', photoUrl);

          // Actualizar caché
          const cachedProfile = await cacheManager.get(CACHE_KEYS.PROFILE);
          if (cachedProfile) {
            // Update both possible field names for maximum compatibility
            cachedProfile.foto = photoUrl;
            cachedProfile.photoUrl = photoUrl;
            await cacheManager.set(CACHE_KEYS.PROFILE, cachedProfile);
            console.log('Caché de perfil actualizada con nueva foto');
          }

          // Verificar si la URL es accesible
          try {
            const testResponse = await fetch(`${NESTJS_URL}${photoUrl}`, { method: 'HEAD' });
            console.log('Test de accesibilidad de imagen:', testResponse.status, testResponse.ok);
          } catch (testError) {
            console.warn('Error al verificar accesibilidad de imagen:', testError);
          }

          // Force refresh profile data
          await this.getProfile(true);
        } else {
          console.warn('⚠️ No se encontró URL de foto en la respuesta del servidor');
        }
      }

      return responseData;
    } catch (error) {
      console.error('Error subiendo foto de perfil:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Get user statistics
   * @param {boolean} forceRefresh - Whether to force a fresh API call
   * @returns {Promise<Object>} - User statistics data
   */
  async getUserStats(forceRefresh = false) {
    try {
      // Try cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedStats = await cacheManager.get(CACHE_KEYS.STATS);
        if (cachedStats) {
          return cachedStats;
        }
      }

      try {
        // Get user ID first
        const profile = await this.getProfile(false);
        const userId = profile.id;

        if (!userId || userId === 'unknown') {
          // Return default stats if we don't have a valid user ID
          const defaultStats = {
            partidasJugadas: 0,
            victorias: 0,
            derrotas: 0,
            puntos: 0,
            nivel: 1
          };

          // Save default stats to cache
          await cacheManager.set(CACHE_KEYS.STATS, defaultStats, 5 * 60 * 1000);

          return defaultStats;
        }

        // Get user results from the correct endpoint
        const results = await apiClient.get(`/api/resultados/usuario/${userId}`);

        // Calculate stats from results
        const stats = this.calculateStatsFromResults(results);

        // Save to cache (5 minutes TTL)
        await cacheManager.set(CACHE_KEYS.STATS, stats, 5 * 60 * 1000);

        return stats;
      } catch (error) {
        console.error('Error obteniendo estadísticas:', error.response?.data || error.message);

        // Check if we have cached stats
        if (error.response?.status === 403) {
          const cachedStats = await cacheManager.get(CACHE_KEYS.STATS);
          if (cachedStats) {
            console.log('Usando estadísticas en caché debido a error 403');
            return cachedStats;
          }
        }

        // If no stats are available yet, return default values
        const defaultStats = {
          partidasJugadas: 0,
          victorias: 0,
          derrotas: 0,
          puntos: 0,
          nivel: 1
        };

        // Save default stats to cache
        await cacheManager.set(CACHE_KEYS.STATS, defaultStats, 5 * 60 * 1000);

        return defaultStats;
      }
    } catch (error) {
      console.error('Error obteniendo estadísticas:', error.response?.data || error.message);

      // Last resort - check if we have any cached stats
      const cachedStats = await cacheManager.get(CACHE_KEYS.STATS);
      if (cachedStats) {
        console.log('Usando estadísticas en caché como último recurso');
        return cachedStats;
      }

      // Return default stats if all else fails
      const defaultStats = {
        partidasJugadas: 0,
        victorias: 0,
        derrotas: 0,
        puntos: 0,
        nivel: 1
      };

      return defaultStats;
    }
  },

  /**
   * Calculate statistics from user results
   * @param {Array} results - User workout results
   * @returns {Object} - Calculated statistics
   */
  calculateStatsFromResults(results) {
    if (!results || !Array.isArray(results) || results.length === 0) {
      return {
        partidasJugadas: 0,
        victorias: 0,
        derrotas: 0,
        puntos: 0,
        nivel: 1
      };
    }

    // Count total workouts
    const partidasJugadas = results.length;

    // Count validated results as victories
    const victorias = results.filter(result => result.validado).length;

    // Calculate total points
    const puntos = results.reduce((total, result) => total + (result.puntuacionRaw || 0), 0);

    return {
      partidasJugadas,
      victorias,
      derrotas: partidasJugadas - victorias,
      puntos: Math.round(puntos * 100) / 100,
      nivel: 1
    };
  },

  /**
   * Get user badges
   * @param {boolean} forceRefresh - Whether to force a fresh API call
   * @returns {Promise<Array>} - User badges
   */
  async getUserBadges(forceRefresh = false) {
    try {
      // Try cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedBadges = await cacheManager.get(CACHE_KEYS.BADGES);
        if (cachedBadges) {
          return cachedBadges;
        }
      }

      // Get fresh badges from API
      const badges = await apiClient.get('/api/badges/my-badges');

      // Save to cache (1 hour TTL)
      await cacheManager.set(CACHE_KEYS.BADGES, badges, 60 * 60 * 1000);

      return badges;
    } catch (error) {
      console.error('Error obteniendo insignias:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Join a box
   * @param {string} boxId - ID of the box to join
   * @returns {Promise<Object>} - Updated user profile
   */
  async joinBox(boxId) {
    try {
      const result = await apiClient.post(`/api/boxes/${boxId}/join`);

      // Invalidate profile cache as it's now outdated
      await cacheManager.invalidate(CACHE_KEYS.PROFILE);

      return result;
    } catch (error) {
      console.error('Error al unirse al box:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Leave current box
   * @returns {Promise<Object>} - Updated user profile
   */
  async leaveBox() {
    try {
      const result = await apiClient.delete(`/api/boxes/leave`);

      // Invalidate profile cache as it's now outdated
      await cacheManager.invalidate(CACHE_KEYS.PROFILE);

      return result;
    } catch (error) {
      console.error('Error al abandonar el box:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Reset onboarding status
   * @returns {Promise<Object>} - Updated user profile
   */
  async resetOnboarding() {
    try {
      const result = await apiClient.post('/api/perfil/reset-onboarding');

      // Update cache with new onboarding status
      await cacheManager.invalidate(CACHE_KEYS.PROFILE);

      return result;
    } catch (error) {
      console.error('Error al resetear onboarding:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Clear all user related caches
   * @returns {Promise<void>}
   */
  async clearCache() {
    try {
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.PROFILE),
        cacheManager.invalidate(CACHE_KEYS.STATS),
        cacheManager.invalidate(CACHE_KEYS.BADGES),
        cacheManager.invalidate(CACHE_KEYS.RESULTS)
      ]);
      console.log('Caché de usuario limpiada');
    } catch (error) {
      console.error('Error al limpiar caché de usuario:', error);
    }
  }
};

export default userService;
