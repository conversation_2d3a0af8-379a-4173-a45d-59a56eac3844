// Definir la URL de Strapi según el entorno
const STRAPI_URL = 'http://localhost:1337'; // Para iOS simulator
// const STRAPI_URL = 'http://********:1337'; // Para Android emulator

// Helper function to handle API responses
const handleResponse = async (response) => {
  const text = await response.text();
  let data;
  try {
    data = JSON.parse(text);
  } catch (e) {
    return text;
  }
  if (!response.ok) {
    throw new Error(data.error?.message || 'API request failed');
  }
  return data;
};

/**
 * Fetch all PVP events
 * @returns {Promise<Array>} Array of PVP events
 */
const fetchPVPEvents = async () => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/pvp-events?populate=*`);
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error fetching PVP events:', error);
    throw error;
  }
};

/**
 * Fetch PVP events for a specific user
 * @param {string} userId - Firebase user ID
 * @returns {Promise<Array>} Array of user's PVP events
 */
const fetchUserPVPEvents = async (userId) => {
  try {
    const response = await fetch(
      `${STRAPI_URL}/api/pvp-events?filters[participants][userId][$eq]=${userId}&populate=*`
    );
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error fetching user PVP events:', error);
    throw error;
  }
};

/**
 * Create a new PVP event
 * @param {Object} eventData - Event data including title, description, etc.
 * @returns {Promise<Object>} Created PVP event
 */
const createPVPEvent = async (eventData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/pvp-events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: eventData }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error creating PVP event:', error);
    throw error;
  }
};

/**
 * Join a PVP event as a participant
 * @param {string} eventId - PVP event ID
 * @param {Object} participantData - Participant data including user info
 * @returns {Promise<Object>} Updated PVP event
 */
const joinPVPEvent = async (eventId, participantData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/participants`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: {
          ...participantData,
          pvp_event: eventId,
        },
      }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error joining PVP event:', error);
    throw error;
  }
};

/**
 * Update a participant's score in a PVP event
 * @param {string} participantId - Participant ID
 * @param {Object} scoreData - Score data including rounds, reps, time, etc.
 * @returns {Promise<Object>} Updated participant data
 */
const updatePVPEventScore = async (participantId, scoreData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/participants/${participantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: scoreData,
      }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error updating PVP event score:', error);
    throw error;
  }
};

/**
 * Fetch participants for a specific PVP event
 * @param {string} eventId - PVP event ID
 * @returns {Promise<Array>} Array of participants
 */
const fetchParticipants = async (eventId) => {
  try {
    const response = await fetch(
      `${STRAPI_URL}/api/participants?filters[pvp_event][id][$eq]=${eventId}&populate=*`
    );
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error fetching participants:', error);
    throw error;
  }
};

/**
 * Delete a PVP event
 * @param {string} eventId - PVP event ID
 * @returns {Promise<void>}
 */
const deletePVPEvent = async (eventId) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/pvp-events/${eventId}`, {
      method: 'DELETE',
    });
    await handleResponse(response);
  } catch (error) {
    console.error('Error deleting PVP event:', error);
    throw error;
  }
};

/**
 * Update a PVP event
 * @param {string} eventId - PVP event ID
 * @param {Object} eventData - Updated event data
 * @returns {Promise<Object>} Updated PVP event
 */
const updatePVPEvent = async (eventId, eventData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/pvp-events/${eventId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: eventData,
      }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error updating PVP event:', error);
    throw error;
  }
};

export default {
  fetchPVPEvents,
  fetchUserPVPEvents,
  createPVPEvent,
  joinPVPEvent,
  updatePVPEventScore,
  fetchParticipants,
  deletePVPEvent,
  updatePVPEvent,
};
