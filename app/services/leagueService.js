/**
 * League Service
 * Gestiona todas las operaciones relacionadas con ligas y clasificaciones
 * Implementa caché optimizada para distintos tipos de datos
 */
import apiClient from '../api/apiClient';
import cacheManager from '../utils/cacheManager';

// Claves de caché para distintos recursos
const CACHE_KEYS = {
  ACTIVE_LEAGUES: 'leagues_active',
  LEAGUE_DETAIL: (id) => `league_${id}`,
  LEADERBOARD: (id, category = '', gender = '') => 
    `leaderboard_${id}${category ? '_' + category : ''}${gender ? '_' + gender : ''}`,
  WEEKLY_LEADERBOARD: (id, week) => `leaderboard_${id}_week_${week}`,
  USER_POSITION: (leagueId) => `leaderboard_${leagueId}_myposition`,
  USER_LEAGUES: 'leagues_registered',
  ACTIVE_WODS: (leagueId) => `league_${leagueId}_active_wods`,
  WODS_HISTORY: (leagueId) => `league_${leagueId}_wods_history`,
};

const leagueService = {
  /**
   * Obtiene las ligas activas/disponibles
   * @param {boolean} forceRefresh - Forzar actualización de datos
   * @returns {Promise<Array>} - Lista de ligas
   */
  async getActiveLeagues(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeagues = await cacheManager.get(CACHE_KEYS.ACTIVE_LEAGUES);
        if (cachedLeagues) {
          return cachedLeagues;
        }
      }
      
      // Obtener datos frescos de la API
      console.log('Obteniendo ligas activas desde API...');
      const leagues = await apiClient.get('/api/leagues');
      
      // Guardar en caché (60 minutos)
      await cacheManager.set(CACHE_KEYS.ACTIVE_LEAGUES, leagues, 60 * 60 * 1000);
      
      return leagues;
    } catch (error) {
      console.error('Error obteniendo ligas activas:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene detalles de una liga específica
   * @param {string} leagueId - ID de la liga
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos de la liga
   */
  async getLeagueDetails(leagueId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.LEAGUE_DETAIL(leagueId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeague = await cacheManager.get(cacheKey);
        if (cachedLeague) {
          return cachedLeague;
        }
      }
      
      // Obtener datos frescos
      const league = await apiClient.get(`/api/leagues/${leagueId}`);
      
      // Guardar en caché (30 minutos)
      await cacheManager.set(cacheKey, league, 30 * 60 * 1000);
      
      return league;
    } catch (error) {
      console.error(`Error obteniendo detalles de liga ${leagueId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene la clasificación general de una liga
   * @param {string} leagueId - ID de la liga
   * @param {string} category - Categoría (RX, Intermedio, Scaled)
   * @param {string} gender - Género (Masculino, Femenino)
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos de clasificación
   */
  async getLeaderboard(leagueId, category = '', gender = '', forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.LEADERBOARD(leagueId, category, gender);
      
      // Parámetros de consulta
      const params = {};
      if (category) params.category = category;
      if (gender) params.gender = gender;
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeaderboard = await cacheManager.get(cacheKey);
        if (cachedLeaderboard) {
          return cachedLeaderboard;
        }
      }
      
      // Construir URL con query params si existen
      const queryString = new URLSearchParams(params).toString();
      const url = `/api/leagues/${leagueId}/leaderboard${queryString ? '?' + queryString : ''}`;
      
      // Obtener datos frescos
      const leaderboard = await apiClient.get(url);
      
      // En competiciones activas, caché de corta duración (2 minutos)
      // En competiciones finalizadas, caché de larga duración (60 minutos)
      const ttl = leaderboard.leagueStatus === 'Finalizada' ? 60 * 60 * 1000 : 2 * 60 * 1000;
      
      // Guardar en caché
      await cacheManager.set(cacheKey, leaderboard, ttl);
      
      return leaderboard;
    } catch (error) {
      console.error(`Error obteniendo clasificación para liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene la clasificación semanal 
   * @param {string} leagueId - ID de la liga
   * @param {number} weekNumber - Número de semana
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos de clasificación semanal
   */
  async getWeeklyLeaderboard(leagueId, weekNumber, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.WEEKLY_LEADERBOARD(leagueId, weekNumber);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedLeaderboard = await cacheManager.get(cacheKey);
        if (cachedLeaderboard) {
          return cachedLeaderboard;
        }
      }
      
      // Obtener datos frescos
      const leaderboard = await apiClient.get(
        `/api/leagues/${leagueId}/leaderboard/week/${weekNumber}`
      );
      
      // Para semanas pasadas, caché de larga duración (60 minutos)
      // Para semana actual, caché de corta duración (2 minutos)
      const currentWeek = leaderboard.currentWeek || 0;
      const ttl = weekNumber < currentWeek ? 60 * 60 * 1000 : 2 * 60 * 1000;
      
      // Guardar en caché
      await cacheManager.set(cacheKey, leaderboard, ttl);
      
      return leaderboard;
    } catch (error) {
      console.error(`Error obteniendo clasificación semanal para liga ${leagueId}, semana ${weekNumber}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene mi posición actual en una liga
   * @param {string} leagueId - ID de la liga
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos de mi posición
   */
  async getMyPosition(leagueId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.USER_POSITION(leagueId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedPosition = await cacheManager.get(cacheKey);
        if (cachedPosition) {
          return cachedPosition;
        }
      }
      
      // Obtener datos frescos
      const position = await apiClient.get(`/api/leagues/${leagueId}/leaderboard/me`);
      
      // Guardar en caché (2 minutos)
      await cacheManager.set(cacheKey, position, 2 * 60 * 1000);
      
      return position;
    } catch (error) {
      console.error(`Error obteniendo mi posición en liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene las ligas en las que estoy inscrito
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de inscripciones
   */
  async getMyRegistrations(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedRegistrations = await cacheManager.get(CACHE_KEYS.USER_LEAGUES);
        if (cachedRegistrations) {
          return cachedRegistrations;
        }
      }
      
      // Obtener datos frescos
      const registrations = await apiClient.get('/api/leagues/my-registrations');
      
      // Guardar en caché (10 minutos)
      await cacheManager.set(CACHE_KEYS.USER_LEAGUES, registrations, 10 * 60 * 1000);
      
      return registrations;
    } catch (error) {
      console.error('Error obteniendo mis inscripciones:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Inscribirse en una liga
   * @param {string} leagueId - ID de la liga
   * @param {string} categoryId - ID de la categoría
   * @returns {Promise<Object>} - Datos de inscripción
   */
  async registerForLeague(leagueId, categoryId) {
    try {
      const registration = await apiClient.post(`/api/leagues/${leagueId}/register`, {
        categoryId
      });
      
      // Invalidar caché de inscripciones
      await cacheManager.invalidate(CACHE_KEYS.USER_LEAGUES);
      
      return registration;
    } catch (error) {
      console.error(`Error inscribiéndose en liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Cancela inscripción en una liga
   * @param {string} leagueId - ID de la liga
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async cancelRegistration(leagueId) {
    try {
      const result = await apiClient.delete(`/api/leagues/${leagueId}/cancel`);
      
      // Invalidar cachés relacionadas
      await cacheManager.invalidate(CACHE_KEYS.USER_LEAGUES);
      
      return result;
    } catch (error) {
      console.error(`Error cancelando inscripción en liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene el WOD activo de una liga
   * @param {string} leagueId - ID de la liga
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos del WOD activo
   */
  async getActiveWod(leagueId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.ACTIVE_WODS(leagueId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedWod = await cacheManager.get(cacheKey);
        if (cachedWod) {
          return cachedWod;
        }
      }
      
      // Obtener datos frescos
      const wod = await apiClient.get(`/api/leagues/${leagueId}/wods/active`);
      
      // Guardar en caché (15 minutos)
      await cacheManager.set(cacheKey, wod, 15 * 60 * 1000);
      
      return wod;
    } catch (error) {
      console.error(`Error obteniendo WOD activo para liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene el historial de WODs de una liga
   * @param {string} leagueId - ID de la liga
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de WODs
   */
  async getWodsHistory(leagueId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.WODS_HISTORY(leagueId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedWods = await cacheManager.get(cacheKey);
        if (cachedWods) {
          return cachedWods;
        }
      }
      
      // Obtener datos frescos
      const wods = await apiClient.get(`/api/leagues/${leagueId}/wods`);
      
      // Guardar en caché (30 minutos)
      await cacheManager.set(cacheKey, wods, 30 * 60 * 1000);
      
      return wods;
    } catch (error) {
      console.error(`Error obteniendo historial de WODs para liga ${leagueId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Invalida todas las cachés relacionadas con una liga específica
   * @param {string} leagueId - ID de la liga
   * @returns {Promise<void>}
   */
  async invalidateLeagueCache(leagueId) {
    try {
      // Crear un array de promesas para invalidar cachés
      const invalidationPromises = [
        cacheManager.invalidate(CACHE_KEYS.LEAGUE_DETAIL(leagueId)),
        cacheManager.invalidate(CACHE_KEYS.ACTIVE_WODS(leagueId)),
        cacheManager.invalidate(CACHE_KEYS.WODS_HISTORY(leagueId)),
        // Invalidar leaderboards generales y por categoría/género
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId)),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'RX', 'Masculino')),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'RX', 'Femenino')),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'Intermedio', 'Masculino')),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'Intermedio', 'Femenino')),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'Scaled', 'Masculino')),
        cacheManager.invalidate(CACHE_KEYS.LEADERBOARD(leagueId, 'Scaled', 'Femenino')),
        cacheManager.invalidate(CACHE_KEYS.USER_POSITION(leagueId))
      ];
      
      // Ejecutar todas las invalidaciones en paralelo
      await Promise.all(invalidationPromises);
      
      console.log(`Cachés de liga ${leagueId} invalidadas correctamente`);
    } catch (error) {
      console.error(`Error invalidando cachés de liga ${leagueId}:`, error);
    }
  }
};

export default leagueService;
