import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/colors';
import authApiNest from '../api/authApiNest';

/**
 * Pantalla para restablecer la contraseña usando un token recibido por email
 * Se accede a esta pantalla desde un enlace de deep linking
 */
const ResetPasswordScreen = ({ navigation, route }) => {
  // Obtener el token de los parámetros de navegación
  const { token } = route.params || {};
  
  // Estado para correo electrónico (para solicitar token)
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  
  // Estados para los campos de contraseña
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // Estados para mostrar/ocultar contraseñas
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Estado para validación detallada de la contraseña
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });
  
  // Estado para mostrar errores de validación
  const [passwordError, setPasswordError] = useState('');
  const [confirmError, setConfirmError] = useState('');
  
  // Estado para controlar la carga
  const [isLoading, setIsLoading] = useState(false);
  
  // Estado para controlar el modo de la pantalla
  const [hasToken, setHasToken] = useState(!!token);

  // Validar token al cargar la pantalla
  useEffect(() => {
    try {
      if (token) {
        // Validar formato del token para evitar errores
        console.log('💡 Token de restablecimiento recibido:', 
          typeof token === 'string' && token.length > 10 ? token.substring(0, 10) + '...' : 'formato inválido');
        setHasToken(true);
      } else {
        console.log('💡 No hay token, mostrando formulario de solicitud');
        setHasToken(false);
      }
    } catch (error) {
      console.error('❌ Error al validar token:', error);
      // En caso de error, mostrar pantalla de solicitud
      setHasToken(false);
    }
  }, [token]);

  // Validar contraseña cuando cambia
  useEffect(() => {
    const validation = validatePasswordRequirements(newPassword);
    setPasswordValidation(validation);
    
    // Establecer mensaje de error si es necesario
    if (newPassword && !allRequirementsMet(validation)) {
      setPasswordError('La contraseña no cumple con todos los requisitos');
    } else {
      setPasswordError('');
    }
  }, [newPassword]);

  // Validar confirmación cuando cambia
  useEffect(() => {
    validateConfirmation(confirmPassword);
  }, [confirmPassword, newPassword]);

  // Verificar si todos los requisitos de contraseña están cumplidos
  const allRequirementsMet = (validation) => {
    return validation.length && 
           validation.uppercase && 
           validation.lowercase && 
           validation.number && 
           validation.special;
  };

  // Función para validar cada requisito de la contraseña
  const validatePasswordRequirements = (password) => {
    if (!password) {
      return {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      };
    }
    
    return {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      // Caracteres especiales: incluye puntos, comas, guiones y guiones bajos
      special: /[!@#$%^&*()_+\-=\[\]{};':\"\\|,.<>\/?_]/.test(password)
    };
  };
  
  // Función para validar la contraseña completa
  const validatePassword = () => {
    if (!newPassword) {
      return false;
    }
    
    return allRequirementsMet(passwordValidation);
  };

  // Función para validar la confirmación
  const validateConfirmation = (confirmation) => {
    if (!confirmation) {
      setConfirmError('');
      return false;
    }
    
    if (confirmation !== newPassword) {
      setConfirmError('Las contraseñas no coinciden');
      return false;
    }
    
    setConfirmError('');
    return true;
  };

  // Función para solicitar un token de restablecimiento
  const handleRequestToken = async () => {
    // Validar email
    if (!email || !email.trim()) {
      setEmailError('Por favor, introduce tu email');
      return;
    }
    
    // Expresión regular para validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError('Por favor, introduce un email válido');
      return;
    }
    
    setEmailError('');
    setIsLoading(true);
    
    try {
      // Llamar a la API para solicitar un token de restablecimiento
      await authApiNest.requestPasswordReset(email);
      
      // Mostrar mensaje de éxito
      Alert.alert(
        '¡Email enviado!',
        'Te hemos enviado un email con instrucciones para restablecer tu contraseña. Revisa tu bandeja de entrada.',
        [{ text: 'OK', onPress: () => navigation.navigate('Login') }]
      );
      
    } catch (error) {
      console.error('Error al solicitar token:', error);
      
      // Mostrar mensaje de error
      let errorMessage = 'No se pudo enviar el email de recuperación. Inténtalo de nuevo.';
      
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        switch (status) {
          case 400:
            errorMessage = 'El formato del email no es válido';
            break;
          case 404:
            errorMessage = 'No existe ninguna cuenta con este email';
            break;
          case 429:
            errorMessage = 'Demasiados intentos. Inténtalo más tarde';
            break;
          default:
            errorMessage = errorData?.message || 'Error al enviar el email de recuperación';
        }
      } else if (error.request) {
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Función para restablecer la contraseña
  const handleResetPassword = async () => {
    // Validar que exista el token y sea una cadena de texto
    if (!token || typeof token !== 'string') {
      Alert.alert('Error', 'No hay un token válido para restablecer la contraseña');
      return;
    }
    
    // Validar que la contraseña cumpla todos los requisitos
    if (!allRequirementsMet(passwordValidation)) {
      Alert.alert('Error', 'La contraseña debe cumplir todos los requisitos de seguridad');
      return;
    }
    
    // Validar confirmación
    if (!validateConfirmation(confirmPassword)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Llamar a la API para restablecer la contraseña
      console.log('🔑 Restableciendo contraseña con token:', 
        typeof token === 'string' && token.length > 10 ? token.substring(0, 10) + '...' : 'token presente pero formato no imprimible');
      
      await authApiNest.resetPassword(token, newPassword);
      
      console.log('✅ Contraseña restablecida exitosamente');
      
      // Mostrar mensaje de éxito
      Alert.alert(
        '¡Éxito!',
        'Tu contraseña ha sido restablecida correctamente. Ahora puedes iniciar sesión con tu nueva contraseña.',
        [{ text: 'Iniciar sesión', onPress: () => navigation.navigate('Login') }]
      );
    } catch (error) {
      console.error('❌ Error al restablecer la contraseña:', error);
      
      // Mostrar mensaje de error apropiado
      let errorMessage = 'No se pudo restablecer la contraseña. Inténtalo de nuevo.';
      
      try {
        if (error.response) {
          const status = error.response.status;
          const errorData = error.response.data;
          console.log('📄 Datos de error recibidos:', JSON.stringify(errorData || 'Sin datos'));
          
          // Manejar mensajes que pueden venir en formato de array
          if (Array.isArray(errorData?.message)) {
            errorMessage = errorData.message.join('\n');
            console.log('📝 Mensaje de error en array:', errorMessage);
          } else {
            switch (status) {
              case 400:
                if (errorData?.message?.includes('invalid') || errorData?.message?.includes('expired')) {
                  errorMessage = 'El enlace ha expirado o no es válido. Solicita un nuevo enlace de recuperación.';
                } else if (errorData?.message?.includes('contener') || errorData?.message?.includes('carácter especial')) {
                  errorMessage = 'La contraseña debe contener al menos una mayúscula, una minúscula, un número y un carácter especial.';
                } else if (errorData?.message?.includes('weak')) {
                  errorMessage = 'La contraseña es demasiado débil. Debe tener al menos 8 caracteres e incluir letras y números.';
                } else {
                  errorMessage = errorData?.message || 'Error al restablecer la contraseña';
                }
                break;
              case 404:
                errorMessage = 'El token de recuperación no es válido o ha expirado';
                break;
              default:
                errorMessage = errorData?.message || 'Error al restablecer la contraseña';
            }
          }
        } else if (error.request) {
          errorMessage = 'Error de conexión. Verifica tu conexión a internet';
        } else {
          errorMessage = error.message || 'Error al procesar la solicitud';
        }
      } catch (parseError) {
        // En caso de error al procesar la respuesta de error
        console.error('❌ Error al procesar datos de error:', parseError);
        errorMessage = 'Ocurrió un error inesperado. Inténtalo de nuevo más tarde.';
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Renderizar un campo de email
  const renderEmailField = () => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>Email</Text>
      <View style={[styles.inputContainer, emailError ? styles.inputError : null]}>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder="<EMAIL>"
          placeholderTextColor="#999"
          keyboardType="email-address"
          autoCapitalize="none"
          autoCompleteType="email"
        />
      </View>
      {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
    </View>
  );
  
  // Renderizar un campo de contraseña
  const renderPasswordField = (label, value, setValue, showPassword, setShowPassword, error) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={[styles.passwordInputContainer, error ? styles.inputError : null]}>
        <TextInput
          style={styles.passwordInput}
          value={value}
          onChangeText={setValue}
          secureTextEntry={!showPassword}
          placeholderTextColor="#999"
          placeholder="••••••••••"
        />
        <TouchableOpacity
          style={styles.eyeIcon}
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons
            name={showPassword ? 'eye-off-outline' : 'eye-outline'}
            size={24}
            color="#999"
          />
        </TouchableOpacity>
      </View>
      {error ? <Text style={styles.errorText}>{error}</Text> : null}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Ionicons name="chevron-back" size={24} color="#e0fe10" />
              <Text style={styles.headerTitle}>Restablecer contraseña</Text>
            </TouchableOpacity>
          </View>

          {/* Contenido condicional basado en si hay token o no */}
          {!hasToken ? (
            // Formulario de solicitud de token
            <View>
              {/* Logo y título para solicitud de token */}
              <View style={styles.logoContainer}>
                <Ionicons name="mail-outline" size={70} color="#e0fe10" />
                <Text style={styles.title}>Recupera tu cuenta</Text>
                <Text style={styles.subtitle}>
                  Introduce tu email y te enviaremos un enlace para restablecer tu contraseña
                </Text>
              </View>

              {/* Formulario de email */}
              <View style={styles.settingsContainer}>
                {renderEmailField()}

                {/* Botón de solicitar token */}
                <TouchableOpacity
                  style={[styles.resetButton, isLoading && styles.disabledButton]}
                  onPress={handleRequestToken}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#000" size="small" />
                  ) : (
                    <Text style={styles.resetButtonText}>Enviar instrucciones</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            // Formulario de restablecimiento de contraseña
            <View>
              {/* Logo y título */}
              <View style={styles.logoContainer}>
                <Ionicons name="lock-open-outline" size={70} color="#e0fe10" />
                <Text style={styles.title}>Crear nueva contraseña</Text>
                <Text style={styles.subtitle}>
                  Tu contraseña debe cumplir los siguientes requisitos:
                </Text>
                
                {/* Requisitos de contraseña con indicadores */}
                <View style={styles.requirementsContainer}>
                  <View style={styles.requirementRow}>
                    <Ionicons 
                      name={passwordValidation.length ? "checkmark-circle" : "close-circle"} 
                      size={18} 
                      color={passwordValidation.length ? "#2ecc71" : "#e74c3c"} 
                    />
                    <Text style={[styles.requirementText, passwordValidation.length && styles.validRequirement]}>
                      Mínimo 8 caracteres
                    </Text>
                  </View>
                  
                  <View style={styles.requirementRow}>
                    <Ionicons 
                      name={passwordValidation.uppercase ? "checkmark-circle" : "close-circle"} 
                      size={18} 
                      color={passwordValidation.uppercase ? "#2ecc71" : "#e74c3c"} 
                    />
                    <Text style={[styles.requirementText, passwordValidation.uppercase && styles.validRequirement]}>
                      Al menos una letra mayúscula (A-Z)
                    </Text>
                  </View>
                  
                  <View style={styles.requirementRow}>
                    <Ionicons 
                      name={passwordValidation.lowercase ? "checkmark-circle" : "close-circle"} 
                      size={18} 
                      color={passwordValidation.lowercase ? "#2ecc71" : "#e74c3c"} 
                    />
                    <Text style={[styles.requirementText, passwordValidation.lowercase && styles.validRequirement]}>
                      Al menos una letra minúscula (a-z)
                    </Text>
                  </View>
                  
                  <View style={styles.requirementRow}>
                    <Ionicons 
                      name={passwordValidation.number ? "checkmark-circle" : "close-circle"} 
                      size={18} 
                      color={passwordValidation.number ? "#2ecc71" : "#e74c3c"} 
                    />
                    <Text style={[styles.requirementText, passwordValidation.number && styles.validRequirement]}>
                      Al menos un número (0-9)
                    </Text>
                  </View>
                  
                  <View style={styles.requirementRow}>
                    <Ionicons 
                      name={passwordValidation.special ? "checkmark-circle" : "close-circle"} 
                      size={18} 
                      color={passwordValidation.special ? "#2ecc71" : "#e74c3c"} 
                    />
                    <Text style={[styles.requirementText, passwordValidation.special && styles.validRequirement]}>
                      Al menos un carácter especial (!@#$%&*.,-_)
                    </Text>
                  </View>
                </View>
              </View>

              {/* Password Fields */}
              <View style={styles.settingsContainer}>
                {renderPasswordField(
                  'Nueva Contraseña',
                  newPassword,
                  setNewPassword,
                  showNewPassword,
                  setShowNewPassword,
                  passwordError
                )}

                {renderPasswordField(
                  'Confirmar Nueva Contraseña',
                  confirmPassword,
                  setConfirmPassword,
                  showConfirmPassword,
                  setShowConfirmPassword,
                  confirmError
                )}

                {/* Botón de restablecer contraseña */}
                <TouchableOpacity
                  style={[styles.resetButton, isLoading && styles.disabledButton]}
                  onPress={handleResetPassword}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#000" size="small" />
                  ) : (
                    <Text style={styles.resetButtonText}>Guardar nueva contraseña</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#232323',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 30
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 30
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center'
  },
  subtitle: {
    fontSize: 16,
    color: '#aaaaaa',
    textAlign: 'center',
    marginBottom: 10
  },
  requirementsContainer: {
    width: '100%',
    paddingHorizontal: 10,
    marginBottom: 20
  },
  requirementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  requirementText: {
    fontSize: 14,
    color: '#aaaaaa',
    marginLeft: 8
  },
  validRequirement: {
    color: '#2ecc71'
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    color: '#598392',
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingHorizontal: 15,
    height: 50,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c'
  },
  passwordInput: {
    flex: 1,
    color: '#232323',
    fontSize: 16,
  },
  input: {
    flex: 1,
    color: '#232323',
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingHorizontal: 15,
    height: 50,
  },
  eyeIcon: {
    padding: 5,
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 14,
    marginTop: 5,
  },
  resetButton: {
    backgroundColor: '#e0fe10',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 30,
  },
  disabledButton: {
    opacity: 0.7,
  },
  resetButtonText: {
    color: '#232323',
    fontSize: 16,
    fontWeight: 'bold',
  }
});

export default ResetPasswordScreen;
