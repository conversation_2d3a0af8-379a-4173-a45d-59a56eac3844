import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';

export enum EstadoLiga {
  PREPARACION = 'Preparacion',
  ACTIVA = 'Activa',
  FINALIZADA = 'Finalizada',
}

@Entity('ligas')
export class Liga {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  nombre: string;

  @Column({ type: 'text' })
  descripcion: string;

  @Column({ name: 'fecha_inicio' })
  fechaInicio: Date;

  @Column({ name: 'fecha_fin' })
  fechaFin: Date;

  @Column({ name: 'duracion_semanas' })
  duracionSemanas: number;

  @Column({
    type: 'enum',
    enum: EstadoLiga,
    default: EstadoLiga.PREPARACION,
  })
  estado: EstadoLiga;

  @Column({ name: 'precio_inscripcion', type: 'decimal', precision: 10, scale: 2 })
  precioInscripcion: number;

  @Column({ name: 'tiene_descuento_early', default: false })
  tieneDescuentoEarly: boolean;

  @Column({ name: 'precio_early', type: 'decimal', precision: 10, scale: 2, nullable: true })
  precioEarly: number;

  @Column({ name: 'categoria_rx', default: true })
  categoriaRx: boolean;

  @Column({ name: 'categoria_intermedio', default: true })
  categoriaIntermedio: boolean;

  @Column({ name: 'categoria_scaled', default: true })
  categoriaScaled: boolean;

  @Column({ name: 'genero_masculino', default: true })
  generoMasculino: boolean;

  @Column({ name: 'genero_femenino', default: true })
  generoFemenino: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
