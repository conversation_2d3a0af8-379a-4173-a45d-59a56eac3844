/**
 * Battle Service
 * Gestiona todas las operaciones relacionadas con desafíos PVP
 * Implementa optimizaciones de caché y tiempo real para batallas activas
 */
import apiClient from '../api/apiClient';
import cacheManager from '../utils/cacheManager';

// Claves de caché para desafíos
const CACHE_KEYS = {
  MY_ACTIVE_BATTLES: 'battles_active',
  MY_BATTLE_HISTORY: 'battles_history',
  BATTLE_DETAIL: (id) => `battle_${id}`,
  BATTLE_RESULTS: (id) => `battle_${id}_results`,
  SUGGESTED_OPPONENTS: 'battles_suggested_opponents'
};

const battleService = {
  /**
   * Obtiene todos los desafíos activos del usuario
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de desafíos activos
   */
  async getMyActiveBattles(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero (con TTL muy corto para batallas activas)
      if (!forceRefresh) {
        const cachedBattles = await cacheManager.get(CACHE_KEYS.MY_ACTIVE_BATTLES);
        if (cachedBattles) {
          return cachedBattles;
        }
      }
      
      // Obtener datos frescos
      const battles = await apiClient.get('/api/battles/active');
      
      // Guardar en caché (2 minutos - TTL corto por ser datos activos)
      await cacheManager.set(CACHE_KEYS.MY_ACTIVE_BATTLES, battles, 2 * 60 * 1000);
      
      return battles;
    } catch (error) {
      console.error('Error obteniendo desafíos activos:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene el historial de desafíos completados
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de desafíos históricos
   */
  async getMyBattleHistory(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedHistory = await cacheManager.get(CACHE_KEYS.MY_BATTLE_HISTORY);
        if (cachedHistory) {
          return cachedHistory;
        }
      }
      
      // Obtener datos frescos
      const history = await apiClient.get('/api/battles/history');
      
      // Guardar en caché (15 minutos)
      await cacheManager.set(CACHE_KEYS.MY_BATTLE_HISTORY, history, 15 * 60 * 1000);
      
      return history;
    } catch (error) {
      console.error('Error obteniendo historial de desafíos:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene detalles de un desafío específico
   * @param {string} battleId - ID del desafío
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Datos del desafío
   */
  async getBattleDetails(battleId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BATTLE_DETAIL(battleId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedBattle = await cacheManager.get(cacheKey);
        if (cachedBattle) {
          return cachedBattle;
        }
      }
      
      // Obtener datos frescos
      const battle = await apiClient.get(`/api/battles/${battleId}`);
      
      // TTL basado en estado: si está en progreso, caché corta (5 min), si ya terminó, más larga (30 min)
      const ttl = ['pending', 'accepted', 'in_progress'].includes(battle.status) 
        ? 5 * 60 * 1000  // 5 minutos
        : 30 * 60 * 1000; // 30 minutos
      
      // Guardar en caché
      await cacheManager.set(cacheKey, battle, ttl);
      
      return battle;
    } catch (error) {
      console.error(`Error obteniendo detalles del desafío ${battleId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene resultados de un desafío
   * @param {string} battleId - ID del desafío
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Object>} - Resultados del desafío
   */
  async getBattleResults(battleId, forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.BATTLE_RESULTS(battleId);
      
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedResults = await cacheManager.get(cacheKey);
        if (cachedResults) {
          return cachedResults;
        }
      }
      
      // Obtener datos frescos
      const results = await apiClient.get(`/api/battles/${battleId}/results`);
      
      // Si el desafío está completado, caché larga (24h), si no, corta (5 min)
      const ttl = results.battleCompleted
        ? 24 * 60 * 60 * 1000  // 24 horas
        : 5 * 60 * 1000;       // 5 minutos
      
      // Guardar en caché
      await cacheManager.set(cacheKey, results, ttl);
      
      return results;
    } catch (error) {
      console.error(`Error obteniendo resultados del desafío ${battleId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Obtiene adversarios sugeridos para desafiar
   * @param {boolean} forceRefresh - Forzar actualización
   * @returns {Promise<Array>} - Lista de adversarios sugeridos
   */
  async getSuggestedOpponents(forceRefresh = false) {
    try {
      // Intentar obtener de caché primero
      if (!forceRefresh) {
        const cachedOpponents = await cacheManager.get(CACHE_KEYS.SUGGESTED_OPPONENTS);
        if (cachedOpponents) {
          return cachedOpponents;
        }
      }
      
      // Obtener datos frescos
      const opponents = await apiClient.get('/api/battles/suggested-opponents');
      
      // Guardar en caché (1 hora)
      await cacheManager.set(CACHE_KEYS.SUGGESTED_OPPONENTS, opponents, 60 * 60 * 1000);
      
      return opponents;
    } catch (error) {
      console.error('Error obteniendo adversarios sugeridos:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Crea un nuevo desafío
   * @param {Object} battleData - Datos del desafío
   * @param {string} battleData.opponentId - ID del adversario
   * @param {string} battleData.wodType - Tipo de WOD
   * @param {string} battleData.description - Descripción del WOD
   * @param {Date} battleData.expiresAt - Fecha de expiración
   * @returns {Promise<Object>} - Desafío creado
   */
  async createBattle(battleData) {
    try {
      const battle = await apiClient.post('/api/battles', battleData);
      
      // Invalidar caché de desafíos activos
      await cacheManager.invalidate(CACHE_KEYS.MY_ACTIVE_BATTLES);
      
      return battle;
    } catch (error) {
      console.error('Error creando desafío:', error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Acepta un desafío
   * @param {string} battleId - ID del desafío
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async acceptBattle(battleId) {
    try {
      const result = await apiClient.post(`/api/battles/${battleId}/accept`);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.MY_ACTIVE_BATTLES),
        cacheManager.invalidate(CACHE_KEYS.BATTLE_DETAIL(battleId))
      ]);
      
      return result;
    } catch (error) {
      console.error(`Error aceptando desafío ${battleId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Rechaza un desafío
   * @param {string} battleId - ID del desafío
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async rejectBattle(battleId) {
    try {
      const result = await apiClient.post(`/api/battles/${battleId}/reject`);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.MY_ACTIVE_BATTLES),
        cacheManager.invalidate(CACHE_KEYS.BATTLE_DETAIL(battleId))
      ]);
      
      // Actualizar historial si el rechazo cambia el estado a completado
      await cacheManager.invalidate(CACHE_KEYS.MY_BATTLE_HISTORY);
      
      return result;
    } catch (error) {
      console.error(`Error rechazando desafío ${battleId}:`, error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Envía el resultado de un desafío
   * @param {string} battleId - ID del desafío
   * @param {Object} resultData - Datos del resultado
   * @returns {Promise<Object>} - Respuesta de la API
   */
  async submitBattleResult(battleId, resultData) {
    try {
      const result = await apiClient.post(`/api/battles/${battleId}/results`, resultData);
      
      // Invalidar cachés
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.BATTLE_DETAIL(battleId)),
        cacheManager.invalidate(CACHE_KEYS.BATTLE_RESULTS(battleId))
      ]);
      
      // Si ambos resultados están enviados, invalidar también las listas
      if (result.battleCompleted) {
        await Promise.all([
          cacheManager.invalidate(CACHE_KEYS.MY_ACTIVE_BATTLES),
          cacheManager.invalidate(CACHE_KEYS.MY_BATTLE_HISTORY)
        ]);
      }
      
      return result;
    } catch (error) {
      console.error(`Error enviando resultado para desafío ${battleId}:`, 
        error.response?.data || error.message);
      throw error;
    }
  },
  
  /**
   * Invalida todas las cachés relacionadas con un desafío
   * @param {string} battleId - ID del desafío
   * @returns {Promise<void>}
   */
  async invalidateBattleCache(battleId) {
    try {
      await Promise.all([
        cacheManager.invalidate(CACHE_KEYS.MY_ACTIVE_BATTLES),
        cacheManager.invalidate(CACHE_KEYS.MY_BATTLE_HISTORY),
        cacheManager.invalidate(CACHE_KEYS.BATTLE_DETAIL(battleId)),
        cacheManager.invalidate(CACHE_KEYS.BATTLE_RESULTS(battleId))
      ]);
      
      console.log(`Cachés del desafío ${battleId} invalidadas correctamente`);
    } catch (error) {
      console.error(`Error invalidando cachés del desafío ${battleId}:`, error);
    }
  }
};

export default battleService;
