import {
  List,
  Datagrid,
  TextField,
  Edit<PERSON>utton,
  Edit,
  TextInput,
  Create,
  required,
  Filter,
  SimpleForm
} from 'react-admin';
import { Box, Typography, Paper } from '@mui/material';

// Filtro para la lista de movimientos
const MovimientoFilter = (props: any) => (
  <Filter {...props}>
    <TextInput label="Buscar" source="q" alwaysOn />
  </Filter>
);

// Lista de movimientos
export const MovimientoList = () => (
  <List filters={<MovimientoFilter />}>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="nombre" />
      <TextField source="descripcion" />
      <EditButton />
    </Datagrid>
  </List>
);

// Interfaces para los tipos de datos
interface VarianteInputProps {
  nivel: string;
  genero: string;
}

// Componente compacto para mostrar variante de movimiento
const VarianteInput = ({ nivel, genero }: VarianteInputProps) => {
  const nombreBase = `${nivel}${genero}`;
  const label = `${nivel.toUpperCase()} ${genero === 'Masc' ? 'M' : 'F'}`;

  return (
    <Box sx={{ mb: 1 }}>
      <TextInput
        source={`${nombreBase}`}
        label={label}
        fullWidth
        size="small"
      />
    </Box>
  );
};

// Componente común para el formulario compacto
const MovimientoForm = () => {
  return (
    <SimpleForm>
      {/* Información Básica */}
      <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ color: '#1976d2', fontWeight: 'bold', mb: 2 }}>
          📝 Información Básica
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Box sx={{ flex: '1 1 300px', minWidth: '250px' }}>
            <TextInput source="nombre" validate={[required()]} fullWidth />
          </Box>
          <Box sx={{ flex: '1 1 300px', minWidth: '250px' }}>
            <TextInput source="descripcion" multiline rows={2} fullWidth />
          </Box>
        </Box>
      </Paper>

      {/* Variantes en Layout Compacto */}
      <Paper elevation={1} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ color: '#1976d2', fontWeight: 'bold', mb: 2 }}>
          🏋️ Variantes por Categoría
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          {/* Columna RX */}
          <Box sx={{ flex: '1 1 200px', minWidth: '180px' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', color: '#d32f2f' }}>
              RX (Avanzado)
            </Typography>
            <VarianteInput nivel="rx" genero="Masc" />
            <VarianteInput nivel="rx" genero="Fem" />
          </Box>

          {/* Columna INT */}
          <Box sx={{ flex: '1 1 200px', minWidth: '180px' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', color: '#ed6c02' }}>
              INT (Intermedio)
            </Typography>
            <VarianteInput nivel="int" genero="Masc" />
            <VarianteInput nivel="int" genero="Fem" />
          </Box>

          {/* Columna SC */}
          <Box sx={{ flex: '1 1 200px', minWidth: '180px' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
              SC (Escalado)
            </Typography>
            <VarianteInput nivel="sc" genero="Masc" />
            <VarianteInput nivel="sc" genero="Fem" />
          </Box>
        </Box>
      </Paper>
    </SimpleForm>
  );
};

// Componente de edición de movimientos
export const MovimientoEdit = () => (
  <Edit>
    <MovimientoForm />
  </Edit>
);

// Componente de creación de movimientos
export const MovimientoCreate = () => (
  <Create>
    <MovimientoForm />
  </Create>
);
