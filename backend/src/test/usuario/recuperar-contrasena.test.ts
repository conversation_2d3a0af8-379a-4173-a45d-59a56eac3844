import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsuariosService } from '../../modules/usuarios/usuarios.service';
import { Usuario, NivelUsuario, GeneroUsuario } from '../../modules/usuarios/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { JwtModule, JwtService } from '@nestjs/jwt';
import jwtConfig from '../../config/jwt.config';
import { NotFoundException } from '@nestjs/common';
import { DatabaseTestHelper, UsuariosTestHelper, AuthTestHelper, usuariosFixtures } from '../helper';

/**
 * Test de recuperación de contraseña
 * 
 * Este test simula el proceso completo de recuperación de contraseña:
 * 1. Solicitud de recuperación
 * 2. Generación de token de recuperación
 * 3. Validación del token
 * 4. Actualización de la contraseña
 */
describe('Recuperar Contraseña Tests', () => {
  let usuariosService: UsuariosService;
  let jwtService: JwtService;
  let testUserId: string;

  // Mock del servicio de email para pruebas
  const mockEmailService = {
    sendPasswordResetEmail: jest.fn().mockResolvedValue(true)
  };

  beforeAll(async () => {
    // Verificar conexión a la base de datos antes de iniciar las pruebas
    if (!(await DatabaseTestHelper.checkDatabaseConnection())) {
      throw new Error('Error de conexión a la base de datos');
    }

    // Crear primero el módulo base con servicios de usuarios usando el helper
    const usuariosModule = await UsuariosTestHelper.createUsuariosTestingModule();
    usuariosService = usuariosModule.get<UsuariosService>(UsuariosService);

    // Configurar el JWT para pruebas
    const authModule = await Test.createTestingModule({
      imports: [
        JwtModule.registerAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => ({
            secret: configService.get('jwt.secret'),
            signOptions: { expiresIn: '1h' }, // Token de recuperación expira en 1 hora
          }),
        }),
        ConfigModule.forRoot({
          isGlobal: true,
          load: [jwtConfig],
          envFilePath: '.env.development',
        }),
      ],
      providers: [
        {
          provide: 'EmailService', // Simulamos un servicio de email
          useValue: mockEmailService
        }
      ]
    }).compile();

    jwtService = authModule.get<JwtService>(JwtService);

    // Crear un usuario de prueba
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = {
      nombre: 'Usuario Recuperación',
      alias: 'usuario_recuperacion',
      email: '<EMAIL>',
      password: hashedPassword,
      nivel: NivelUsuario.INTERMEDIO,
      genero: GeneroUsuario.MASCULINO,
    };

    // Limpiar datos anteriores
    try {
      const existingUser = await usuariosService['usuariosRepository'].findOne({
        where: { email: testUser.email },
      });

      if (existingUser) {
        await usuariosService['usuariosRepository'].delete(existingUser.id);
      }
    } catch (error) {
      // Ignoramos errores, el usuario posiblemente no existe
    }

    // Crear usuario nuevo para las pruebas
    const createdUser = await usuariosService['usuariosRepository'].save(
      usuariosService['usuariosRepository'].create(testUser)
    );

    testUserId = createdUser.id;
  });

  afterAll(async () => {
    // Limpiar: eliminar el usuario de prueba
    try {
      await usuariosService['usuariosRepository'].delete(testUserId);
    } catch (error) {
      // Ignoramos errores en la limpieza
    }
  });

  // Función simulada para solicitar recuperación de contraseña
  async function solicitarRecuperacionContrasena(email: string) {
    // 1. Verificar que el usuario existe
    const usuario = await usuariosService.findByEmail(email);

    if (!usuario) {
      throw new NotFoundException(`Usuario con email ${email} no encontrado`);
    }

    // 2. Generar token de recuperación
    const payload = {
      sub: usuario.id,
      email: usuario.email,
      type: 'password-reset'
    };

    const token = jwtService.sign(payload, { expiresIn: '1h' });

    // 3. Enviar email (simulado)
    await mockEmailService.sendPasswordResetEmail(usuario.email, token);

    return { success: true, message: 'Email de recuperación enviado' };
  }

  // Función simulada para validar token de recuperación
  function validarTokenRecuperacion(token: string) {
    try {
      const payload = jwtService.verify(token);

      // Verificar que es un token de recuperación de contraseña
      if (payload.type !== 'password-reset') {
        return { valid: false, message: 'Token inválido' };
      }

      return {
        valid: true,
        userId: payload.sub,
        email: payload.email
      };
    } catch (error) {
      return { valid: false, message: 'Token inválido o expirado' };
    }
  }

  // Función simulada para actualizar contraseña después de recuperación
  async function actualizarContrasenaRecuperacion(userId: string, newPassword: string) {
    // 1. Actualizar la contraseña
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // 2. Actualizar en la base de datos
    await usuariosService['usuariosRepository'].update(userId, {
      password: hashedPassword
    });

    return { success: true, message: 'Contraseña actualizada correctamente' };
  }

  // Test para el flujo completo de recuperación de contraseña
  it('debe completar el flujo de recuperación de contraseña correctamente', async () => {
    // 1. Solicitar recuperación
    const solicitudResult = await solicitarRecuperacionContrasena('<EMAIL>');
    expect(solicitudResult.success).toBe(true);
    expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalled();

    // Obtener el token del mock (en un caso real estaría en el email)
    const token = mockEmailService.sendPasswordResetEmail.mock.calls[0][1];

    // 2. Validar el token
    const validacionResult = validarTokenRecuperacion(token);
    expect(validacionResult.valid).toBe(true);
    expect(validacionResult.userId).toBe(testUserId);
    expect(validacionResult.email).toBe('<EMAIL>');

    // 3. Actualizar la contraseña
    const nuevaContrasena = 'NuevaContrasena456!';
    const actualizacionResult = await actualizarContrasenaRecuperacion(
      validacionResult.userId,
      nuevaContrasena
    );
    expect(actualizacionResult.success).toBe(true);

    // 4. Verificar que la contraseña se actualizó correctamente
    const usuario = await usuariosService.findOne(testUserId);
    const passwordValid = await bcrypt.compare(nuevaContrasena, usuario.password);
    expect(passwordValid).toBe(true);
  });

  // Test para solicitud de recuperación con email inexistente
  it('debe rechazar solicitud de recuperación para email inexistente', async () => {
    await expect(
      solicitarRecuperacionContrasena('<EMAIL>')
    ).rejects.toThrow(NotFoundException);

    // Verificar que no se envió email
    expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalledWith(
      '<EMAIL>',
      expect.anything()
    );
  });

  // Test para validar token inválido
  it('debe rechazar token de recuperación inválido', () => {
    const resultado = validarTokenRecuperacion('token.invalido.jwt');
    expect(resultado.valid).toBe(false);
  });

  // Test para validar token expirado
  it('debe rechazar token de recuperación expirado', () => {
    // Vamos a crear un token con tiempo de vida muy corto
    // y esperar a que expire para validarlo
    
    // Crear el token pero sin la propiedad exp en el payload 
    // y usando la opción de expiresIn muy corta
    const payload = { 
      sub: testUserId, 
      email: '<EMAIL>',
      type: 'password-reset'
    };
    
    // Construir un token manualmente con fecha de expiración en el pasado
    // para evitar problemas con la biblioteca jsonwebtoken
    const header = { alg: 'HS256', typ: 'JWT' };
    const nowInSeconds = Math.floor(Date.now() / 1000);
    const expirationTime = nowInSeconds - 3600; // Expirado hace 1 hora
    
    const payloadWithExp = {
      ...payload,
      iat: nowInSeconds - 7200, // Emitido hace 2 horas
      exp: expirationTime
    };
    
    // Convertir a base64
    const headerStr = Buffer.from(JSON.stringify(header)).toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
    const payloadStr = Buffer.from(JSON.stringify(payloadWithExp)).toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
    
    // Crear una firma ficticia (no importa para probar expiración)
    const signature = 'invalid_signature_just_for_testing_expiration';
    
    // Formar el token JWT
    const tokenExpirado = `${headerStr}.${payloadStr}.${signature}`;
    
    // Probar la validación del token usando la función real
    const resultado = validarTokenRecuperacion(tokenExpirado);
    expect(resultado.valid).toBe(false);
    expect(resultado.message).toContain('expirado');
  });
});
