import React, { createContext, useState, useContext, useEffect } from 'react';
import { auth } from './firebaseConfig';
import { onAuthStateChanged, signOut, reload } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUser(user);
        setIsAuthenticated(true);
        setEmailVerified(user.emailVerified);
      } else {
        setUser(null);
        setIsAuthenticated(false);
        setEmailVerified(false);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const login = async (userData) => {
    try {
      const token = await userData.getIdToken();
      await AsyncStorage.setItem('jwt', token);

      // Verificar si es el primer inicio de sesión del usuario
      const setupCompleted = await AsyncStorage.getItem('setupCompleted');
      const isFirstLogin = setupCompleted !== 'true';

      // Actualizar el estado de verificación de email
      setEmailVerified(userData.emailVerified);

      setUser({
        ...userData,
        isFirstLogin
      });
      setIsAuthenticated(true);

      return isFirstLogin; // Retornamos si es la primera vez para la navegación
    } catch (error) {
      console.error('Error storing JWT:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Log out from Firebase
      await signOut(auth);

      // Eliminar todos los tokens y datos de usuario
      const keysToRemove = [
        'jwt',                // Token de Firebase
        'strapi-auth',        // Token de Strapi
        'user_data',          // Datos de usuario
        'userEmail',          // Email del usuario
        'userName'            // Nombre de usuario
      ];

      // Eliminar todos los datos de una vez
      await AsyncStorage.multiRemove(keysToRemove);

      // Update authentication state
      setIsAuthenticated(false);
      setUser(null);
      console.log(`Logout successful: ${user?.email || 'unknown user'}`);
      return true;
    } catch (error) {
      console.error('Error in logout process:', error);
      throw error;
    }
  };

  // Función para refrescar el estado del usuario
  const refreshUser = async () => {
    try {
      if (auth.currentUser) {
        await reload(auth.currentUser);
        setEmailVerified(auth.currentUser.emailVerified);
        setUser(auth.currentUser);
        return auth.currentUser.emailVerified;
      }
      return false;
    } catch (error) {
      console.error('Error refreshing user:', error);
      return false;
    }
  };

  if (loading) {
    return null; // or a loading spinner
  }

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      login,
      logout,
      loading,
      emailVerified,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};