import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContextNest';
import Toast from 'react-native-toast-message';
import ParticipantCard from '../components/ParticipantCard';
import ScoreSubmissionForm from '../components/ScoreSubmissionForm';
import pvpEventApi from '../api/pvpEventApi';
import { format } from 'date-fns';

const PVPEventDetailsScreen = ({ route, navigation }) => {
  const { eventId } = route.params;
  const { user } = useAuth();
  const [event, setEvent] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showScoreModal, setShowScoreModal] = useState(false);
  const [userParticipation, setUserParticipation] = useState(null);

  const loadEventDetails = async () => {
    try {
      const [eventData, participantsData] = await Promise.all([
        pvpEventApi.fetchPVPEvents(eventId),
        pvpEventApi.fetchParticipants(eventId),
      ]);

      setEvent(eventData);
      setParticipants(participantsData);

      // Find user's participation
      const userPart = participantsData.find(
        p => p.firebase_uid === user.uid
      );
      setUserParticipation(userPart);
    } catch (error) {
      console.error('Error loading event details:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load event details',
      });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEventDetails();
    setRefreshing(false);
  };

  useEffect(() => {
    loadEventDetails();
  }, [eventId]);

  const handleJoinEvent = async () => {
    try {
      const participantData = {
        firebase_uid: user.uid,
        participant_name: user.displayName || 'Anonymous',
        profile_pic_url: user.photoURL,
        sport: 'CrossFit', // This could come from user profile
      };

      await pvpEventApi.joinPVPEvent(eventId, participantData);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'You have joined the event!',
      });
      loadEventDetails();
    } catch (error) {
      console.error('Error joining event:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to join the event',
      });
    }
  };

  const handleSubmitScore = async (scoreData) => {
    try {
      await pvpEventApi.updatePVPEventScore(userParticipation.id, scoreData);
      setShowScoreModal(false);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Score submitted successfully!',
      });
      loadEventDetails();
    } catch (error) {
      console.error('Error submitting score:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to submit score',
      });
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#eb8d28" />
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Event not found</Text>
      </View>
    );
  }

  const isEventExpired = new Date(event.expiration_date) < new Date();
  const hasSubmittedScore = userParticipation?.score !== null;

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#eb8d28']}
            tintColor="#eb8d28"
          />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>{event.title}</Text>
          <View style={styles.locationContainer}>
            <Icon name="location-on" size={16} color="#666" />
            <Text style={styles.location}>{event.location}</Text>
          </View>
          <View style={styles.timeContainer}>
            <Icon name="access-time" size={16} color="#666" />
            <Text style={styles.timeText}>
              Expires: {format(new Date(event.expiration_date), 'PPp')}
            </Text>
          </View>
        </View>

        <View style={styles.wodSection}>
          <Text style={styles.sectionTitle}>Workout</Text>
          <Text style={styles.wodName}>{event.wod_name}</Text>
          <Text style={styles.wodDescription}>{event.wod_description}</Text>
          <View style={styles.wodDetails}>
            <View style={styles.wodDetail}>
              <Icon name="timer" size={16} color="#666" />
              <Text style={styles.wodDetailText}>{event.time_cap} min cap</Text>
            </View>
            <View style={styles.wodDetail}>
              <Icon name="leaderboard" size={16} color="#666" />
              <Text style={styles.wodDetailText}>{event.scoring_method}</Text>
            </View>
          </View>
        </View>

        <View style={styles.participantsSection}>
          <Text style={styles.sectionTitle}>Participants</Text>
          {participants.map((participant, index) => (
            <ParticipantCard
              key={participant.id}
              participant={participant}
              rank={index + 1}
            />
          ))}
        </View>
      </ScrollView>

      {!userParticipation && !isEventExpired && (
        <TouchableOpacity
          style={styles.joinButton}
          onPress={handleJoinEvent}
        >
          <Text style={styles.joinButtonText}>Join Event</Text>
        </TouchableOpacity>
      )}

      {userParticipation && !isEventExpired && !hasSubmittedScore && (
        <TouchableOpacity
          style={styles.submitButton}
          onPress={() => setShowScoreModal(true)}
        >
          <Text style={styles.submitButtonText}>Submit Score</Text>
        </TouchableOpacity>
      )}

      <Modal
        visible={showScoreModal}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <ScoreSubmissionForm
              event={event}
              onSubmit={handleSubmitScore}
              onCancel={() => setShowScoreModal(false)}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#666',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  location: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  wodSection: {
    padding: 16,
    backgroundColor: '#fff',
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  wodName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  wodDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  wodDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wodDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  wodDetailText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  participantsSection: {
    padding: 16,
    backgroundColor: '#fff',
    marginTop: 8,
  },
  joinButton: {
    backgroundColor: '#eb8d28',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  joinButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    backgroundColor: '#eb8d28',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    minHeight: '50%',
    maxHeight: '90%',
  },
});

export default PVPEventDetailsScreen;
