import { PartialType } from '@nestjs/mapped-types';
import { CreateResultadoDto } from './create-resultado.dto';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class UpdateResultadoDto extends PartialType(CreateResultadoDto) {
  @IsOptional()
  @IsNumber()
  flags?: number;

  @IsOptional()
  @IsNumber()
  flagsRecibidos?: number;

  @IsOptional()
  @IsNumber()
  puntuacionRaw?: number;
}
