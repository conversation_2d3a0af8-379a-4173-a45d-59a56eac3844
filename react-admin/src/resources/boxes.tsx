import * as React from 'react';
import { 
  List, 
  Datagrid, 
  TextField, 
  Edit, 
  SimpleForm, 
  TextInput,
  Create,
  ImageField,
  ImageInput,
  ReferenceArrayField,
  SingleFieldList,
  ChipField,
  ReferenceArrayInput,
  AutocompleteArrayInput,
  required,
  ReferenceInput,
  AutocompleteInput,
  SelectInput,
  TabbedForm,
  FormTab,
  useNotify,
  useRedirect
} from 'react-admin';

// Lista de Boxes
export const BoxList = () => (
  <List>
    <Datagrid rowClick="edit">
      <TextField source="id" />
      <TextField source="nombre" />
      <TextField source="ubicacion" label="Ubicación" />
      <TextField source="estado" />
      <ReferenceArrayField source="miembros" reference="users" label="Atletas">
        <SingleFieldList>
          <ChipField source="nombre" />
        </SingleFieldList>
      </ReferenceArrayField>
    </Datagrid>
  </List>
);

// Componente de edición de boxes
export const BoxEdit = () => {
  const formatUserOption = (record: any) => record ? `${record.nombre} (${record.email})` : '';
  
  return (
    <Edit>
      <TabbedForm>
        <FormTab label="Información básica" icon={<span>🏢</span>}>
          <TextInput disabled source="id" />
          <TextInput source="nombre" validate={[required()]} fullWidth />
          <TextInput source="ubicacion" label="Ubicación" validate={[required()]} multiline rows={3} fullWidth />
          <TextInput source="descripcion" multiline rows={5} fullWidth />
        </FormTab>

        <FormTab label="Imagen y estado" icon={<span>📷</span>}>
          <ImageInput 
            source="logo" 
            label="Logo" 
            accept={{
              'image/*': []
            }}
          >
            <ImageField source="src" title="title" />
          </ImageInput>
          
          <SelectInput source="estado" choices={[
            { id: 'Activo', name: 'Activo' },
            { id: 'Inactivo', name: 'Inactivo' },
          ]} defaultValue="Activo" fullWidth />
        </FormTab>

        <FormTab label="Miembros" icon={<span>👥</span>}>
          <ReferenceInput 
            source="ownerId" 
            reference="users" 
            label="Dueño del box" 
            required 
            filter={{ estado: 'Activo' }}
          >
            <AutocompleteInput optionText={formatUserOption} fullWidth />
          </ReferenceInput>
          
          <ReferenceArrayInput 
            source="miembros" 
            reference="users" 
            label="Atletas"
            filter={{ estado: 'Activo' }}
          >
            <AutocompleteArrayInput optionText={formatUserOption} fullWidth />
          </ReferenceArrayInput>
        </FormTab>
      </TabbedForm>
    </Edit>
  );
};

// Componente de creación de boxes con manejo de errores
export const BoxCreate = () => {
  const notify = useNotify();
  const redirect = useRedirect();
  const formatUserOption = (record: any) => record ? `${record.nombre} (${record.email})` : '';

  // Crearemos un componente separado para el manejo de eventos
  const handleSubmitSuccess = () => {
    notify('Box creado correctamente', { type: 'success' });
    redirect('list', 'boxes');
  };

  // El formulario tiene su propio ciclo de vida
  const handleSubmitFailure = (error: any) => {
    if (error && error.body && error.body.message) {
      notify(`Error: ${error.body.message}`, { type: 'warning' });
    } else {
      notify('Error al crear el Box. Asegúrate de que has seleccionado un propietario.', { type: 'warning' });
    }
  };

  return (
    <Create mutationOptions={{ 
      onSuccess: handleSubmitSuccess, 
      onError: handleSubmitFailure 
    }}>
      <TabbedForm>
        <FormTab label="Información básica" icon={<span>🏢</span>}>
          <TextInput source="nombre" validate={[required()]} fullWidth />
          <TextInput source="ubicacion" label="Ubicación" validate={[required()]} multiline rows={3} fullWidth />
          <TextInput source="descripcion" multiline rows={5} fullWidth />
        </FormTab>

        <FormTab label="Imagen y estado" icon={<span>📷</span>}>
          <ImageInput 
            source="logo" 
            label="Logo" 
            accept={{
              'image/*': []
            }}
          >
            <ImageField source="src" title="title" />
          </ImageInput>
          
          <SelectInput source="estado" choices={[
            { id: 'Activo', name: 'Activo' },
            { id: 'Inactivo', name: 'Inactivo' },
          ]} defaultValue="Activo" fullWidth />
        </FormTab>

        <FormTab label="Miembros" icon={<span>👥</span>}>
          <ReferenceInput 
            source="ownerId" 
            reference="users" 
            label="Dueño del box (requerido)" 
            required
            filter={{ estado: 'Activo' }}
            helperText="Seleccionar un propietario es obligatorio para crear un box"
          >
            <AutocompleteInput optionText={formatUserOption} fullWidth />
          </ReferenceInput>
          
          <ReferenceArrayInput 
            source="miembros" 
            reference="users" 
            label="Atletas"
            filter={{ estado: 'Activo' }}
          >
            <AutocompleteArrayInput optionText={formatUserOption} fullWidth />
          </ReferenceArrayInput>
        </FormTab>
      </TabbedForm>
    </Create>
  );
};
