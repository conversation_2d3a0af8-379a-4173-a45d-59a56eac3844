# Informe Oficial de Infraestructura para "The WOD League"

## Resumen Ejecutivo

Este documento detalla la infraestructura recomendada para implementar y operar **"The WOD League"**, una aplicación para competiciones remotas de CrossFit que requiere lógica de negocio compleja, actualizaciones en tiempo real, escalabilidad y un modelo de datos relacional. Basado en un análisis exhaustivo de los requisitos de la aplicación, se propone una arquitectura basada en **NestJS** con un stack personalizado, alojado en **Amazon Web Services (AWS)**. La solución incluye componentes clave como **PostgreSQL**, **Redis**, **WebSockets**, y **AWS S3**, diseñados para garantizar rendimiento, flexibilidad y escalabilidad. Al final, se presenta una estimación aproximada de costes para dos escenarios: un MVP con 1000 usuarios activos mensuales y un sistema escalado para 10,000 usuarios.

---

## Requisitos de la Aplicación

"The WOD League" es una plataforma que combina ligas virtuales, desafíos PVP, competiciones entre boxes (Battle Box), y eventos especiales. Los requisitos clave de infraestructura son:

1. **Modelo de Datos Relacional**: Entidades como Usuarios, Ligas, WODs, Resultados, Clasificaciones, y relaciones complejas (por ejemplo, Inscriptions → Categories → Leagues).
2. **Lógica de Negocio Compleja**: Cálculos de puntuación (por ejemplo, proporcional por tipo de WOD, bonos de consistencia), detección de outliers, y mecánicas de desafíos PVP/Battle Box.
3. **Actualizaciones en Tiempo Real**: Clasificaciones en vivo, notificaciones (cambios de posición, estados de desafíos), y tablas de clasificación dinámicas.
4. **Escalabilidad**: Soporte para miles de usuarios, múltiples ligas simultáneas, y picos de concurrencia durante plazos de WODs.
5. **Integraciones**: API de Instagram para compartir resultados, pasarelas de pago (por ejemplo, Stripe), y autenticación OAuth.
6. **Seguridad**: Gestión de roles (Usuario, BoxOwner, Admin), protección de datos sensibles, y validación de resultados.
7. **Desarrollo y Mantenimiento**: Desarrollo rápido para un MVP, con flexibilidad para futuras extensiones y mantenibilidad a largo plazo.

---

## Arquitectura Propuesta

La infraestructura utiliza **NestJS** como framework backend, combinado con tecnologías de código abierto y servicios gestionados de AWS. La arquitectura está diseñada para ser modular, escalable y optimizada para los requisitos de la aplicación.

### 1. Backend
- **Tecnología**: NestJS con Express
  - **Descripción**: Framework de Node.js basado en TypeScript, ideal para aplicaciones escalables con lógica compleja. Maneja APIs RESTful, lógica de negocio (puntuaciones, detección de outliers, desafíos), y autenticación.
  - **Funcionalidades**:
    - Procesamiento de resultados y cálculos de puntuación.
    - Gestión de desafíos PVP y Battle Box.
    - Integración con APIs externas (Instagram, Stripe).
  - **Justificación**: Estructura modular (controladores, servicios) y soporte para TypeScript garantizan mantenibilidad y escalabilidad.

### 2. Base de Datos
- **Tecnología**: PostgreSQL (AWS RDS)
  - **Descripción**: Base de datos relacional de código abierto, gestionada mediante AWS Relational Database Service (RDS).
  - **Funcionalidades**:
    - Almacenamiento de entidades (Usuarios, Ligas, WODs, Resultados, etc.) con relaciones complejas.
    - Consultas optimizadas para clasificaciones, estadísticas históricas, y reportes.
  - **Justificación**: Soporta el modelo relacional de la app, con uniones eficientes y escalabilidad vertical/horizontal.

### 3. Caché
- **Tecnología**: Redis (AWS Elasticache)
  - **Descripción**: Base de datos en memoria para almacenamiento de datos frecuentes.
  - **Funcionalidades**:
    - Caché de clasificaciones en tiempo real para reducir la carga en PostgreSQL.
    - Almacenamiento de sesiones de usuario y datos temporales.
  - **Justificación**: Mejora el rendimiento de consultas intensivas y soporta alta concurrencia.

### 4. Tiempo Real
- **Tecnología**: WebSockets con Socket.io
  - **Descripción**: Biblioteca integrada en NestJS para comunicaciones en tiempo real.
  - **Funcionalidades**:
    - Actualizaciones instantáneas de tablas de clasificación.
    - Notificaciones de eventos (cambios de posición, desafíos aceptados).
  - **Justificación**: Ofrece control total sobre la lógica en tiempo real, más flexible que los listeners de Firebase.

### 5. Almacenamiento de Archivos
- **Tecnología**: AWS S3
  - **Descripción**: Servicio de almacenamiento de objetos para archivos estáticos.
  - **Funcionalidades**:
    - Almacenamiento de fotos de usuarios, imágenes de WODs, y badges.
    - Entrega de archivos mediante URLs seguras.
  - **Justificación**: Escalable, económico, y compatible con integraciones de frontend.

### 6. Autenticación
- **Tecnología**: Passport.js con JWT y OAuth
  - **Descripción**: Biblioteca integrada en NestJS para autenticación segura.
  - **Funcionalidades**:
    - Autenticación por correo/contraseña y redes sociales (por ejemplo, Instagram, Google).
    - Gestión de roles (Usuario, BoxOwner, Admin) con JWT.
  - **Justificación**: Flexible, seguro, y compatible con estándares OAuth para integraciones sociales.

### 7. Notificaciones Push
- **Tecnología**: OneSignal
  - **Descripción**: Servicio externo para notificaciones push.
  - **Funcionalidades**:
    - Envío de notificaciones sobre eventos clave (plazos de WODs, resultados validados, desafíos).
  - **Justificación**: Gratis hasta 30,000 suscriptores, fácil de integrar con NestJS.

### 8. Despliegue
- **Tecnología**: Docker + AWS Elastic Beanstalk (MVP) / Kubernetes (EKS) (Escalado)
  - **Descripción**:
    - **Elastic Beanstalk**: Plataforma gestionada para desplegar el backend en el MVP, minimizando la configuración inicial.
    - **Kubernetes (EKS)**: Orquestación de contenedores para escalado horizontal en producción.
  - **Funcionalidades**:
    - Despliegue automatizado de la aplicación NestJS.
    - Escalado dinámico según la carga (por ejemplo, picos durante plazos de WODs).
  - **Justificación**: Elastic Beanstalk simplifica el MVP; Kubernetes ofrece escalabilidad robusta para miles de usuarios.

### 9. Monitoreo y CI/CD
- **Tecnologías**:
  - **Monitoreo**: Prometheus + Grafana (integrados en AWS).
  - **CI/CD**: GitHub Actions.
- **Funcionalidades**:
  - Monitoreo de rendimiento (latencia, uso de CPU, errores).
  - Despliegues automatizados tras cambios en el código.
- **Justificación**: Mejora la visibilidad operativa y agiliza el desarrollo.

### 10. Integraciones Externas
- **API de Instagram**: Para compartir resultados y badges en redes sociales.
- **Stripe**: Para procesar pagos de inscripciones y suscripciones premium.
- **Justificación**: NestJS permite integraciones personalizadas mediante módulos HTTP y bibliotecas específicas.

---

## Diagrama de Arquitectura

```
[Frontend: React/Vue/Angular]
       ↓ (REST API + WebSockets)
[Backend: NestJS + Express]
    ↓                ↓
[PostgreSQL: AWS RDS] [Redis: AWS Elasticache]
    ↓                ↓
[AWS S3: Imágenes] [Socket.io: Tiempo Real]
       ↓
[Docker + Elastic Beanstalk (MVP) / Kubernetes EKS (Producción)]
       ↓
[Monitoreo: Prometheus + Grafana] [CI/CD: GitHub Actions]
       ↓
[Integraciones: Instagram API, Stripe, OneSignal]
```

**Flujo Ejemplo**:
1. Un usuario envía un resultado de WOD mediante la API REST.
2. NestJS valida el resultado, calcula la puntuación, y lo guarda en PostgreSQL.
3. Redis actualiza el caché de clasificaciones.
4. Socket.io notifica a los usuarios conectados del cambio en el ranking.
5. El usuario comparte su resultado en Instagram mediante la API.

---

## Implementación y Escalabilidad

### Fase 1: MVP (2-3 Meses)
- **Despliegue**: Elastic Beanstalk con una instancia EC2 t3.micro, RDS t3.micro, y Elasticache t3.micro.
- **Funcionalidades Core**:
  - Gestión de usuarios (registro, perfil, autenticación).
  - Creación y gestión de ligas/WODs.
  - Envío de resultados y cálculos de puntuación.
  - Clasificaciones básicas en tiempo real.
- **Pruebas**: Aprovechar el 37% de tests existentes (según "W-SUMMARY.md"), priorizando lógica de puntuación y rankings.

### Fase 2: Escalado (6-12 Meses)
- **Despliegue**: Migrar a Kubernetes (EKS) con nodos t3.medium, RDS t3.medium, y Elasticache t3.medium.
- **Optimizaciones**:
  - Índices en PostgreSQL para consultas frecuentes (clasificaciones, resultados).
  - Caché agresivo en Redis para tablas de clasificación.
  - Auto-scaling en Kubernetes para picos de uso.
- **Nuevas Funcionalidades**: Desafíos PVP, Battle Box, eventos especiales, analíticas avanzadas.

---

## Requisitos de Personal
- **Desarrolladores Backend**: 1-2 con experiencia en NestJS, TypeScript, y bases de datos relacionales.
- **DevOps**: 1 ingeniero (o consultor) para configurar Kubernetes, monitoreo, y CI/CD.
- **Frontend**: 1-2 desarrolladores para la interfaz (React/Vue/Angular), fuera del alcance de este documento.
- **Soporte**: Equipo pequeño para monitoreo inicial y resolución de incidencias.

---

## Estimación de Costes

La estimación de costes se basa en dos escenarios: un **MVP con 1000 usuarios activos mensuales** y un **sistema escalado para 10,000 usuarios**. Los precios reflejan tarifas aproximadas de AWS en Europa (Frankfurt, 2025) y asumen un uso moderado con optimizaciones básicas.

### Suposiciones
- **Usuarios**: 1000 (MVP) y 10,000 (escalado).
- **Operaciones**:
  - 10 resultados de WODs/usuario/mes.
  - 100 lecturas/usuario/mes para clasificaciones.
  - 5 notificaciones push/usuario/mes.
  - Almacenamiento de imágenes: 1 GB (MVP), 10 GB (escalado).
- **Tráfico**: 1 GB (MVP), 10 GB (escalado) para WebSockets y S3.

### Costes para MVP (1000 Usuarios)
1. **Compute (Elastic Beanstalk)**:
   - 1 EC2 t3.micro: $0.012/hora * 720 = $8.64/mes.
   - ALB: $0.0225/hora * 720 + $0.008/GB * 1 = $16.208/mes.
   - **Total**: $24.85/mes
2. **PostgreSQL (RDS)**:
   - db.t3.micro, multi-AZ: $0.034/hora * 720 = $24.48/mes.
   - 20 GB SSD: $0.115/GB * 20 = $2.30/mes.
   - **Total**: $26.78/mes
3. **Redis (Elasticache)**:
   - cache.t3.micro: $0.017/hora * 720 = $12.24/mes.
   - **Total**: $12.24/mes
4. **Storage (S3)**:
   - 1 GB: $0.023/GB = $0.023/mes.
   - Operaciones: 1000 PUT ($0.005/1000) + 10,000 GET ($0.0004/1000) = $0.009/mes.
   - **Total**: $0.032/mes
5. **WebSockets**:
   - Tráfico: 1 GB = $0.09/GB = $0.09/mes.
6. **Notificaciones (OneSignal)**: Gratis.
7. **Autenticación (NestJS)**: Incluido en EC2.
- **Total Estimado**: ~$64.00/mes

### Costes para Escalado (10,000 Usuarios)
1. **Compute (Kubernetes EKS)**:
   - 2 nodos EC2 t3.medium: $0.048/hora * 2 * 720 = $69.12/mes.
   - EKS cluster: $0.10/hora * 720 = $72.00/mes.
   - ALB: $0.0225/hora * 720 + $0.008/GB * 10 = $16.28/mes.
   - **Total**: $157.40/mes
2. **PostgreSQL (RDS)**:
   - db.t3.medium, multi-AZ: $0.136/hora * 720 = $97.92/mes.
   - 100 GB SSD: $0.115/GB * 100 = $11.50/mes.
   - **Total**: $109.42/mes
3. **Redis (Elasticache)**:
   - cache.t3.medium: $0.068/hora * 720 = $48.96/mes.
   - **Total**: $48.96/mes
4. **Storage (S3)**:
   - 10 GB: $0.023/GB * 10 = $0.23/mes.
   - Operaciones: 10,000 PUT ($0.005/1000) + 100,000 GET ($0.0004/1000) = $0.09/mes.
   - **Total**: $0.32/mes
5. **WebSockets**:
   - Tráfico: 10 GB = $0.09/GB * 10 = $0.90/mes.
6. **Notificaciones (OneSignal)**: Gratis.
7. **Autenticación (NestJS)**: Incluido en EC2.
- **Total Estimado**: ~$317.00/mes

### Optimizaciones de Costes
- **Instancias Reservadas**: Hasta 30-50% de descuento en EC2, RDS, y Elasticache con compromisos de 1-3 años.
- **Auto-scaling**: Configurar Kubernetes para escalar nodos según la carga.
- **Caching**: Usar Redis para minimizar consultas a PostgreSQL.
- **Planes de Ahorro**: AWS Savings Plans para reducir costes a largo plazo.

---

## Conclusión

La infraestructura propuesta basada en **NestJS** con **PostgreSQL**, **Redis**, **WebSockets**, y **AWS** ofrece una solución robusta, escalable y flexible para "The WOD League". Soporta el modelo relacional, la lógica compleja, y las actualizaciones en tiempo real requeridas, con costes iniciales moderados (~$64/mes para 1000 usuarios) y escalabilidad eficiente (~$317/mes para 10,000 usuarios). Comparada con alternativas como Firebase, esta arquitectura evita la dependencia de proveedores y optimiza el rendimiento para las necesidades específicas de la aplicación. La implementación en fases (MVP con Elastic Beanstalk, escalado con Kubernetes) asegura un desarrollo ágil y una transición suave hacia la producción.