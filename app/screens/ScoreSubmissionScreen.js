import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import pvpEventApi from '../api/pvpEventApi';
import Toast from 'react-native-toast-message';

const ScoreSubmissionScreen = ({ route, navigation }) => {
  const { eventId, participantId } = route.params;
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [score, setScore] = useState({
    rounds: '',
    reps: '',
    time: '',
    weight: '',
    notes: '',
  });

  const validateScore = () => {
    if (!score.rounds && !score.reps && !score.time && !score.weight) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please enter at least one score value',
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateScore()) return;

    try {
      setSubmitting(true);
      const scoreData = {
        ...score,
        rounds: score.rounds ? parseInt(score.rounds, 10) : null,
        reps: score.reps ? parseInt(score.reps, 10) : null,
        weight: score.weight ? parseFloat(score.weight) : null,
      };

      await pvpEventApi.updatePVPEventScore(participantId, scoreData);
      
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Score submitted successfully',
      });
      
      navigation.goBack();
    } catch (error) {
      console.error('Error submitting score:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to submit score. Please try again.',
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#eb8d28" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Rounds</Text>
            <TextInput
              style={styles.input}
              value={score.rounds}
              onChangeText={(text) => setScore({ ...score, rounds: text })}
              keyboardType="numeric"
              placeholder="Enter number of rounds"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Reps</Text>
            <TextInput
              style={styles.input}
              value={score.reps}
              onChangeText={(text) => setScore({ ...score, reps: text })}
              keyboardType="numeric"
              placeholder="Enter number of reps"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Time (mm:ss)</Text>
            <TextInput
              style={styles.input}
              value={score.time}
              onChangeText={(text) => setScore({ ...score, time: text })}
              placeholder="Enter time (e.g., 12:30)"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Weight (kg)</Text>
            <TextInput
              style={styles.input}
              value={score.weight}
              onChangeText={(text) => setScore({ ...score, weight: text })}
              keyboardType="numeric"
              placeholder="Enter weight in kg"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={score.notes}
              onChangeText={(text) => setScore({ ...score, notes: text })}
              placeholder="Add any additional notes"
              multiline
              numberOfLines={4}
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, submitting && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Icon name="check" size={24} color="#fff" />
                <Text style={styles.submitButtonText}>Submit Score</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#eb8d28',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default ScoreSubmissionScreen;
