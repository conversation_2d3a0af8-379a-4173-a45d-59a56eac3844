// Definir la URL de Strapi según el entorno
const STRAPI_URL = 'http://localhost:1337'; // Para iOS simulator
// const STRAPI_URL = 'http://********:1337'; // Para Android emulator

// Helper function to handle API responses
const handleResponse = async (response) => {
  const text = await response.text();
  let data;
  try {
    data = JSON.parse(text);
  } catch (e) {
    return text;
  }
  if (!response.ok) {
    throw new Error(data.error?.message || 'API request failed');
  }
  return data;
};

/**
 * Fetch all battles
 * @returns {Promise<Array>} Array of battles
 */
const fetchBattles = async () => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/battles?populate=*`);
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error fetching battles:', error);
    throw error;
  }
};

/**
 * Fetch battles where user is a participant
 * @param {string} userId - Firebase user ID
 * @returns {Promise<Array>} Array of user's battles
 */
const fetchUserBattles = async (userId) => {
  try {
    const response = await fetch(
      `${STRAPI_URL}/api/battles?filters[participants][userId][$eq]=${userId}&populate=*`
    );
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error fetching user battles:', error);
    throw error;
  }
};

/**
 * Create a new battle
 * @param {Object} battleData - Battle data including title, description, etc.
 * @returns {Promise<Object>} Created battle
 */
const createBattle = async (battleData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/battles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: battleData }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error creating battle:', error);
    throw error;
  }
};

/**
 * Join a battle as a participant
 * @param {string} battleId - Battle ID
 * @param {Object} participantData - Participant data including user info
 * @returns {Promise<Object>} Updated battle
 */
const joinBattle = async (battleId, participantData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/battle-participants`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: {
          ...participantData,
          battle: battleId,
        },
      }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error joining battle:', error);
    throw error;
  }
};

/**
 * Update a participant's score in a battle
 * @param {string} participantId - Participant ID
 * @param {Object} scoreData - Score data including rounds, reps, time, etc.
 * @returns {Promise<Object>} Updated participant data
 */
const updateBattleScore = async (participantId, scoreData) => {
  try {
    const response = await fetch(`${STRAPI_URL}/api/battle-participants/${participantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: scoreData,
      }),
    });
    const data = await handleResponse(response);
    return data.data;
  } catch (error) {
    console.error('Error updating battle score:', error);
    throw error;
  }
};

export default {
  fetchBattles,
  fetchUserBattles,
  createBattle,
  joinBattle,
  updateBattleScore,
};
