import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator
} from 'react-native';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/colors';
import authApiNest from '../api/authApiNest';
import AsyncStorage from '@react-native-async-storage/async-storage';

const PasswordSettingsScreen = ({ navigation }) => {

  // Estados para los campos de contraseña
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Estado para la validación de contraseña
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });

  // Estado para error de confirmación
  const [confirmError, setConfirmError] = useState('');

  // Estados para mostrar/ocultar contraseñas
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Estados para el modal de recuperación de contraseña
  const [showForgotModal, setShowForgotModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');

  // Estado para controlar la carga
  const [isLoading, setIsLoading] = useState(false);

  // Función para verificar los requisitos de contraseña
  const checkPasswordRequirements = (password) => {
    if (!password) {
      return {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      };
    }

    return {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      // Caracteres especiales: incluye puntos, comas, guiones y guiones bajos
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?_]/.test(password)
    };
  };

  // Función para validar si todos los requisitos se cumplen
  const allRequirementsMet = (validation) => {
    return validation.length &&
      validation.uppercase &&
      validation.lowercase &&
      validation.number &&
      validation.special;
  };

  // Actualizar la validación cuando cambia la contraseña
  useEffect(() => {
    setPasswordValidation(checkPasswordRequirements(newPassword));
  }, [newPassword]);

  // Función para validar la confirmación
  const validateConfirmation = () => {
    if (!confirmPassword) {
      setConfirmError('');
      return false;
    }

    if (confirmPassword !== newPassword) {
      setConfirmError('Las contraseñas no coinciden');
      return false;
    }

    setConfirmError('');
    return true;
  };

  // Validar confirmación cuando cambia
  useEffect(() => {
    validateConfirmation();
  }, [confirmPassword, newPassword]);

  // Función para cambiar la contraseña
  const handleChangePassword = async () => {
    // Validar que las contraseñas coinciden
    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Las contraseñas nuevas no coinciden');
      return;
    }

    // Validar que la contraseña cumple con todos los requisitos
    if (!allRequirementsMet(passwordValidation)) {
      Alert.alert('Error', 'La contraseña no cumple con todos los requisitos de seguridad');
      return;
    }

    setIsLoading(true);

    try {
      // Obtener el email del usuario
      const userEmail = await AsyncStorage.getItem('userEmail');

      if (!userEmail) {
        Alert.alert('Error', 'No se pudo obtener la información del usuario. Por favor, inicia sesión de nuevo.');
        return;
      }

      console.log('Cambiando contraseña para:', userEmail);

      // Cambiar la contraseña usando NestJS API
      await authApiNest.changePassword(currentPassword, newPassword, confirmPassword);

      console.log('Contraseña actualizada correctamente');

      // Mostrar mensaje de éxito
      Alert.alert(
        'Éxito',
        'Tu contraseña ha sido actualizada correctamente',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);

      // Mostrar mensaje de error apropiado
      let errorMessage = 'No se pudo cambiar la contraseña. Inténtalo de nuevo.';

      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        switch (status) {
          case 401:
            errorMessage = 'La contraseña actual es incorrecta';
            break;
          case 400:
            if (errorData?.message?.includes('weak')) {
              errorMessage = 'La nueva contraseña es demasiado débil';
            } else {
              errorMessage = errorData?.message || 'Datos inválidos';
            }
            break;
          case 403:
            errorMessage = 'Por razones de seguridad, debes volver a iniciar sesión antes de cambiar tu contraseña';
            break;
          default:
            errorMessage = errorData?.message || 'Error al cambiar la contraseña';
        }
      } else if (error.request) {
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      } else {
        errorMessage = error.message || 'Error al procesar la solicitud';
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Función para mostrar el modal de recuperación de contraseña
  const handleForgotPassword = async () => {
    // Inicializar el email con el email del usuario actual si está disponible
    const userEmail = await AsyncStorage.getItem('userEmail');
    if (userEmail) {
      setResetEmail(userEmail);
    } else {
      setResetEmail('');
    }
    setShowForgotModal(true);
  };

  // Estado para controlar la carga del reset de contraseña
  const [isResetting, setIsResetting] = useState(false);

  // Función para enviar el email de recuperación de contraseña
  const handleResetPassword = async () => {
    if (!resetEmail.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Email requerido',
        text2: 'Por favor, introduce tu email',
      });
      return;
    }

    setIsResetting(true);

    try {
      // Usar la nueva API de NestJS
      await authApiNest.requestPasswordReset(resetEmail);

      Toast.show({
        type: 'success',
        text1: 'Email enviado',
        text2: 'Revisa tu bandeja de entrada para restablecer tu contraseña',
      });
      setShowForgotModal(false);
    } catch (error) {
      console.log('Error al restablecer contraseña:', error);
      let errorMessage = 'Error al enviar el email';

      // Analizar error según respuesta del servidor
      if (error.response) {
        const status = error.response.status;

        switch (status) {
          case 400:
            errorMessage = 'El formato del email no es válido';
            break;
          case 404:
            errorMessage = 'No existe ninguna cuenta con este email';
            break;
          case 429:
            errorMessage = 'Demasiados intentos. Inténtalo más tarde';
            break;
          default:
            errorMessage = error.response.data?.message || 'Error al enviar el email de recuperación';
        }
      } else if (error.request) {
        errorMessage = 'Error de conexión. Verifica tu conexión a internet';
      }

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: errorMessage,
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Renderizar los requisitos de contraseña
  const renderPasswordRequirements = () => (
    <View style={styles.requirementsContainer}>
      <Text style={styles.requirementsTitle}>La contraseña debe cumplir:</Text>

      <View style={styles.requirementRow}>
        <Ionicons
          name={passwordValidation.length ? "checkmark-circle" : "close-circle"}
          size={18}
          color={passwordValidation.length ? "#2ecc71" : "#e74c3c"}
        />
        <Text style={[styles.requirementText, passwordValidation.length && styles.validRequirement]}>
          Mínimo 8 caracteres
        </Text>
      </View>

      <View style={styles.requirementRow}>
        <Ionicons
          name={passwordValidation.uppercase ? "checkmark-circle" : "close-circle"}
          size={18}
          color={passwordValidation.uppercase ? "#2ecc71" : "#e74c3c"}
        />
        <Text style={[styles.requirementText, passwordValidation.uppercase && styles.validRequirement]}>
          Al menos una letra mayúscula (A-Z)
        </Text>
      </View>

      <View style={styles.requirementRow}>
        <Ionicons
          name={passwordValidation.lowercase ? "checkmark-circle" : "close-circle"}
          size={18}
          color={passwordValidation.lowercase ? "#2ecc71" : "#e74c3c"}
        />
        <Text style={[styles.requirementText, passwordValidation.lowercase && styles.validRequirement]}>
          Al menos una letra minúscula (a-z)
        </Text>
      </View>

      <View style={styles.requirementRow}>
        <Ionicons
          name={passwordValidation.number ? "checkmark-circle" : "close-circle"}
          size={18}
          color={passwordValidation.number ? "#2ecc71" : "#e74c3c"}
        />
        <Text style={[styles.requirementText, passwordValidation.number && styles.validRequirement]}>
          Al menos un número (0-9)
        </Text>
      </View>

      <View style={styles.requirementRow}>
        <Ionicons
          name={passwordValidation.special ? "checkmark-circle" : "close-circle"}
          size={18}
          color={passwordValidation.special ? "#2ecc71" : "#e74c3c"}
        />
        <Text style={[styles.requirementText, passwordValidation.special && styles.validRequirement]}>
          Al menos un carácter especial (!@#$%&*.,-_)
        </Text>
      </View>
    </View>
  );

  // Renderizar los requisitos de contraseña
  // Renderizar un campo de contraseña
  const renderPasswordField = (label, value, setValue, showPassword, setShowPassword) => (
    <View style={styles.fieldContainer}>
      <Text style={styles.fieldLabel}>{label}</Text>
      <View style={styles.passwordInputContainer}>
        <TextInput
          style={styles.passwordInput}
          value={value}
          onChangeText={setValue}
          secureTextEntry={!showPassword}
          placeholderTextColor="#999"
          placeholder="••••••••••"
        />
        <TouchableOpacity
          style={styles.eyeIcon}
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons
            name={showPassword ? 'eye-off-outline' : 'eye-outline'}
            size={24}
            color="#999"
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={24} color="#e0fe10" />
          <Text style={styles.headerTitle}>Cambiar de contraseña</Text>
        </TouchableOpacity>
      </View>

      {/* Sección de información sobre requisitos de contraseña */}
      <View style={styles.securityInfoContainer}>
        <View style={styles.securityHeader}>
          <Ionicons name="lock-closed" size={22} color="#e0fe10" />
          <Text style={styles.securityTitle}>Requisitos de seguridad</Text>
        </View>
        {renderPasswordRequirements()}
      </View>

      {/* Password Settings */}
      <View style={styles.settingsContainer}>
        {renderPasswordField(
          'Contraseña Actual',
          currentPassword,
          setCurrentPassword,
          showCurrentPassword,
          setShowCurrentPassword
        )}

        <TouchableOpacity onPress={handleForgotPassword}>
          <Text style={styles.forgotPasswordText}>¿Olvidaste Tu Contraseña?</Text>
        </TouchableOpacity>

        {renderPasswordField(
          'Nueva Contraseña',
          newPassword,
          setNewPassword,
          showNewPassword,
          setShowNewPassword
        )}

        {/* Espacio para separar campos */}
        <View style={{ height: 10 }} />

        {renderPasswordField(
          'Confirmar Nueva Contraseña',
          confirmPassword,
          setConfirmPassword,
          showConfirmPassword,
          setShowConfirmPassword
        )}
        {confirmError ? <Text style={styles.errorText}>{confirmError}</Text> : null}

        {/* Botón de cambiar contraseña */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.disabledButton]}
          onPress={handleChangePassword}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFF" size="small" />
          ) : (
            <Text style={styles.saveButtonText}>Cambiar contraseña</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Modal de Recuperación de Contraseña */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showForgotModal}
        onRequestClose={() => setShowForgotModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Restablecer Contraseña</Text>
            <Text style={styles.modalText}>
              Introduce tu email y te enviaremos un enlace para restablecer tu contraseña.
            </Text>
            <TextInput
              style={styles.modalInput}
              placeholder="Email"
              keyboardType="email-address"
              autoCapitalize="none"
              value={resetEmail}
              onChangeText={setResetEmail}
            />
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowForgotModal(false)}
              >
                <Text style={styles.modalCancelText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.resetButton, isResetting && styles.disabledButton]}
                onPress={handleResetPassword}
                disabled={isResetting}
              >
                {isResetting ? (
                  <ActivityIndicator color="#FFF" size="small" />
                ) : (
                  <Text style={styles.resetButtonText}>Enviar email de recuperación</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  securityInfoContainer: {
    marginHorizontal: 15,
    marginTop: 10,
    marginBottom: 10,
    backgroundColor: Colors.BACKGROUND,
    borderRadius: 8,
    padding: 15,

  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e0fe10',
    marginLeft: 8,
  },
  requirementsContainer: {
    width: '100%',
    marginBottom: 5,
  },
  requirementsTitle: {
    fontSize: 14,
    color: '#aaaaaa',
    marginBottom: 10,
  },
  requirementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 14,
    color: '#aaaaaa',
    marginLeft: 8,
  },
  validRequirement: {
    color: '#2ecc71',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 14,
    marginTop: -15,
    marginBottom: 10,
    marginLeft: 10,
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#e0fe10',
    marginLeft: 5,
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 20,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    color: '#598392',
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingHorizontal: 15,
    height: 50,
  },
  passwordInput: {
    flex: 1,
    color: '#232323',
    fontSize: 16,
  },
  eyeIcon: {
    padding: 5,
  },
  forgotPasswordText: {
    color: '#e0fe10',
    textAlign: 'right',
    marginTop: -10,
    marginBottom: 20,
  },
  saveButton: {
    backgroundColor: '#e0fe10',
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: '#232323',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#232323',
  },
  modalText: {
    fontSize: 14,
    marginBottom: 15,
    textAlign: 'center',
    color: '#232323',
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    marginBottom: 20,
    color: '#232323',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  modalCancelText: {
    color: '#666',
    fontWeight: 'bold',
  },
  resetButton: {
    backgroundColor: '#232323',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  resetButtonText: {
    color: '#e0fe10',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PasswordSettingsScreen;
