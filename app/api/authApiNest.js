import { NESTJS_URL } from '../config';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { jwtDecode } from 'jwt-decode';

/**
 * Authentication API service for NestJS backend
 * Handles all authentication-related requests
 */
const authApiNest = {
  /**
   * Register a new user
   * @param {Object} userData - User data including email, password, username, etc.
   * @returns {Promise<Object>} - Response from NestJS API
   */
  register: async (userData) => {
    try {
      const response = await axios.post(`${NESTJS_URL}/api/auth/register`, userData);
      
      // Store tokens if they exist in the response
      if (response.data && response.data.access_token) {
        await AsyncStorage.setItem('auth-token', response.data.access_token);
        
        if (response.data.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.data.refresh_token);
        }
        
        // Store user email for convenience
        if (userData.email) {
          await AsyncStorage.setItem('userEmail', userData.email);
        }
        
        // Store username if available
        if (userData.username || userData.fullName) {
          await AsyncStorage.setItem('userName', userData.username || userData.fullName);
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error in register:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Login user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Response from NestJS API
   */
  /**
   * Procesa una respuesta exitosa de login
   * @param {Object} response - Respuesta del servidor
   * @param {string} email - Email del usuario
   * @returns {Object} - Datos procesados de la respuesta
   */
  handleLoginSuccess: async (response, email) => {
    if (response.data && response.data.access_token) {
      await AsyncStorage.setItem('auth-token', response.data.access_token);
      
      if (response.data.refresh_token) {
        await AsyncStorage.setItem('refresh-token', response.data.refresh_token);
      }
      
      // Almacenar email del usuario para comodidad
      await AsyncStorage.setItem('userEmail', email);
      
      // Almacenar nombre de usuario si está disponible
      if (response.data.user && (response.data.user.username || response.data.user.fullName)) {
        await AsyncStorage.setItem('userName', response.data.user.username || response.data.user.fullName);
      }
      
      // Almacenar estado de configuración si está disponible
      if (response.data.user && response.data.user.setupCompleted !== undefined) {
        await AsyncStorage.setItem('setupCompleted', response.data.user.setupCompleted.toString());
      }
    }
    
    return response.data;
  },

  /**
   * Login user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Response from NestJS API
   */
  login: async (email, password) => {
    try {
      // Usar la ruta correcta con prefijo /api
      const loginUrl = `${NESTJS_URL}/api/auth/login`;
      
      const response = await axios.post(loginUrl, { email, password });
      return await authApiNest.handleLoginSuccess(response, email);
    } catch (error) {
      console.error('Error in login:', error.response?.data || error);
      
      // Transformar errores específicos en mensajes más amigables
      if (error.response) {
        const status = error.response.status;
        
        // Transformar 404 (Not Found) en un error más amigable
        if (status === 404) {
          const customError = new Error('Usuario no encontrado');
          customError.response = {
            status: 404,
            data: { message: 'El usuario no existe en nuestro sistema', code: 'USER_NOT_FOUND' }
          };
          throw customError;
        }
      }
      
      // Si no es un error específico que queramos transformar, simplemente lanzar el error original
      throw error;
    }
  },
  
  /**
   * Refresh token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} - Response from NestJS API
   */
  refreshToken: async (refreshToken) => {
    try {
      const response = await axios.post(`${NESTJS_URL}/api/auth/refresh`, { 
        refresh_token: refreshToken 
      });
      
      if (response.data && response.data.access_token) {
        await AsyncStorage.setItem('auth-token', response.data.access_token);
        
        if (response.data.refresh_token) {
          await AsyncStorage.setItem('refresh-token', response.data.refresh_token);
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error in refreshToken:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Send verification email to user
   * @param {string} email - User email
   * @returns {Promise<Object>} - Response from NestJS API
   */
  sendVerificationEmail: async (email) => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await axios.post(
        `${NESTJS_URL}/api/auth/send-verification-email`,
        { email },
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error in sendVerificationEmail:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Verify user email with verification token
   * @param {string} token - Verification token
   * @returns {Promise<Object>} - Response from NestJS API
   */
  verifyEmail: async (token) => {
    try {
      const response = await axios.post(`${NESTJS_URL}/api/auth/verify-email`, { token });
      return response.data;
    } catch (error) {
      console.error('Error in verifyEmail:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Check if email is verified
   * @returns {Promise<Object>} - Response from NestJS API
   */
  checkEmailVerification: async () => {
    try {
      // Obtener token de autenticación
      const token = await AsyncStorage.getItem('auth-token');
      if (!token) {
        throw new Error('No hay token de autenticación');
      }
      
      // Verificar estado con el servidor
      const response = await axios.get(`${NESTJS_URL}/api/auth/verify-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Estado de verificación:', response.data);
      
      // Verificamos todas las posibles propiedades que podrían indicar verificación
      // El backend puede usar 'verified', 'emailVerified' o 'emailVerificado'
      const isVerified = 
        response.data.verified === true || 
        response.data.emailVerified === true || 
        response.data.emailVerificado === true;
      
      // Si el email está verificado según cualquiera de las propiedades,
      // devolvemos un objeto normalizado con la propiedad 'verified' en true
      if (isVerified) {
        return { verified: true };
      }
      
      // Si tenemos acceso a un token JWT, verificamos también si indica que está verificado
      try {
        const jwtToken = await AsyncStorage.getItem('auth-token');
        if (jwtToken) {
          const decoded = jwtDecode(jwtToken);
          if (decoded && (decoded.emailVerificado === true || decoded.emailVerified === true)) {
            console.log('🔍 Email verificado según JWT');
            return { verified: true };
          }
        }
      } catch (jwtError) {
        console.warn('Error al verificar JWT:', jwtError.message);
        // Continuamos con el flujo normal incluso si hay error con el JWT
      }
      
      return response.data;
    } catch (error) {
      console.error('Error al verificar email:', error.message);
      throw error;
    }
  },
  
  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} - Response from NestJS API
   */
  requestPasswordReset: async (email) => {
    try {
      const response = await axios.post(`${NESTJS_URL}/api/auth/forgot-password`, { email });
      return response.data;
    } catch (error) {
      console.error('Error in requestPasswordReset:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Reset password with token
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} - Response from NestJS API
   */
  resetPassword: async (token, newPassword) => {
    try {
      const response = await axios.post(`${NESTJS_URL}/api/auth/reset-password`, {
        token,
        password: newPassword
      });
      
      return response.data;
    } catch (error) {
      console.error('Error in resetPassword:', error.response?.data || error);
      throw error;
    }
  },
  
  /**
   * Change password (when logged in)
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Confirmation of new password
   * @returns {Promise<Object>} - Response from NestJS API
   */
  changePassword: async (currentPassword, newPassword, confirmPassword = newPassword) => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      if (!token) {
        throw new Error('No auth token available');
      }
      
      const response = await axios.post(
        `${NESTJS_URL}/api/auth/change-password`,
        {
          currentPassword,
          newPassword,
          confirmPassword
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error in changePassword:', error.response?.data || error);
      throw error;
    }
  },

  /**
   * Logout user
   * @returns {Promise<void>}
   */
  logout: async () => {
    try {
      // Obtener el refresh token si existe
      const refreshToken = await AsyncStorage.getItem('refresh-token');
      
      if (refreshToken) {
        // Intentar revocar el refresh token en el servidor
        try {
          await axios.post(`${NESTJS_URL}/api/auth/logout`, {
            refresh_token: refreshToken
          });
        } catch (error) {
          console.error('Error calling logout endpoint:', error);
          // Continue with local logout even if server logout fails
        }
      }
      
      // Remove all auth-related items from AsyncStorage
      const keysToRemove = [
        'auth-token',
        'refresh-token',
        'userEmail',
        'userName',
        'setupCompleted'
      ];
      
      await AsyncStorage.multiRemove(keysToRemove);
      
      return true;
    } catch (error) {
      console.error('Error in logout:', error);
      throw error;
    }
  },

  /**
   * Eliminar la cuenta del usuario actual
   * Requiere autenticación y confirmación de contraseña
   * @param {string} password - Contraseña del usuario para confirmar la eliminación
   * @returns {Promise<Object>} - Respuesta del servidor
   */
  deleteAccount: async (password) => {
    try {
      // Obtener el token de autenticación
      const token = await AsyncStorage.getItem('auth-token');
      
      if (!token) {
        throw new Error('No hay sesión activa');
      }
      
      // Configurar los headers con el token
      const config = {
        headers: { Authorization: `Bearer ${token}` }
      };
      
      // Enviar petición DELETE al nuevo endpoint
      await axios.delete(
        `${NESTJS_URL}/api/perfil/me`,
        { 
          data: { password },
          ...config 
        }
      );
      
      // Limpiar el almacenamiento
      await AsyncStorage.removeItem('auth-token');
      await AsyncStorage.removeItem('refresh-token');
      await AsyncStorage.removeItem('userProfile');
      
      return { success: true };
    } catch (error) {
      console.error('Error al eliminar la cuenta:', error);
      throw error;
    }
  }
};

export default authApiNest;
